import { pgTable, text, serial, integer, boolean, timestamp, pgEnum, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Enums
export const userRoleEnum = pgEnum('user_role', ['admin', 'assessor', 'school', 'parent', 'assessee', 'university']);
export const assessmentStatusEnum = pgEnum('assessment_status', [
  'VerificationPending', // New status for referrals waiting for admin verification
  'Enquiry', 
  'pre_assessment', 
  'scheduled', 
  'assessment_complete', 
  'report_writing', 
  'qa_review', 
  'completed'
]);
export const paymentStatusEnum = pgEnum('payment_status', ['unpaid', 'deposit_paid', 'fully_paid']);
export const referralTypeEnum = pgEnum('referral_type', ['individual', 'school', 'university']);
export const formStatusEnum = pgEnum('form_status', ['not_started', 'in_progress', 'completed']);
export const userStatusEnum = pgEnum('user_status', ['active', 'pending', 'email_verification_pending', 'admin_approval_pending', 'inactive']);
export const messageStatusEnum = pgEnum('message_status', ['unread', 'read', 'archived']);
export const notificationTypeEnum = pgEnum('notification_type', ['assessment', 'message', 'form', 'system', 'payment', 'report']);

// Universities table
export const universities = pgTable("universities", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  emailDomain: text("email_domain").notNull().unique(),
  address: text("address"),
  phone: text("phone"),
  website: text("website"),
  verified: boolean("verified").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Users table - compatible with existing schema
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  email: text("email").notNull().unique(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  fullName: text("full_name").notNull(),
  role: userRoleEnum("role").notNull(),
  status: userStatusEnum("status").default('active'),
  phone: text("phone"),
  organization: text("organization"),
  position: text("position"),  // Added for university staff and other roles
  department: text("department"),  // Added for university staff department
  createdAt: timestamp("created_at").defaultNow(),
});

// Password reset tokens table
export const passwordResetTokens = pgTable("password_reset_tokens", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  token: text("token").notNull(),
  expires: timestamp("expires").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  usedAt: timestamp("used_at"),
});

// Assessees table for people being assessed
export const assessees = pgTable("assessees", {
  id: serial("id").primaryKey(),
  fullName: text("full_name").notNull(),
  preferredName: text("preferred_name"),
  dateOfBirth: timestamp("date_of_birth").notNull(),
  email: text("email"),
  phone: text("phone"),
  phoneWork: text("phone_work"),
  address: text("address"),
  hearAbout: text("hear_about"),
  hearAboutOther: text("hear_about_other"),
  courseProgram: text("course_program"), // Added for university students
  yearOfStudy: text("year_of_study"),    // Added for university students
  schoolId: integer("school_id").references(() => schools.id),
  parentId: integer("parent_id").references(() => users.id),
  userId: integer("user_id").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
});

// Schools table
export const schools = pgTable("schools", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  address: text("address"),
  contactPerson: text("contact_person"),
  contactEmail: text("contact_email"),
  contactPhone: text("contact_phone"),
  userId: integer("user_id").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
});

// Assessments table - compatible with existing schema
export const assessments = pgTable("assessments", {
  id: serial("id").primaryKey(),
  assesseeId: integer("assessee_id").references(() => assessees.id).notNull(),
  assessorId: integer("assessor_id").references(() => users.id),
  referringUserId: integer("referring_user_id").references(() => users.id),
  referralType: referralTypeEnum("referral_type").notNull(),
  status: assessmentStatusEnum("status").notNull().default('VerificationPending'),
  paymentStatus: paymentStatusEnum("payment_status").notNull().default('unpaid'),
  depositAmount: integer("deposit_amount"),
  finalAmount: integer("final_amount"),
  depositPaidAt: timestamp("deposit_paid_at"),
  finalPaidAt: timestamp("final_paid_at"),
  scheduledDate: timestamp("scheduled_date"),
  completedDate: timestamp("completed_date"),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Referrals table
export const referrals = pgTable("referrals", {
  id: serial("id").primaryKey(),
  trackingId: text("tracking_id").unique(),
  referralType: referralTypeEnum("referral_type").notNull(),
  status: text("status").notNull(),
  referringUserId: integer("referring_user_id").references(() => users.id),
  assesseeId: integer("assessee_id").references(() => assessees.id),
  assessmentId: integer("assessment_id").references(() => assessments.id),
  schoolName: text("school_name"),
  schoolEmail: text("school_email"),
  schoolPhone: text("school_phone"),
  department: text("department"),
  staffName: text("staff_name"),
  staffPosition: text("staff_position"),
  parentName: text("parent_name"),
  parentEmail: text("parent_email"),
  parentPhone: text("parent_phone"),
  relationship: text("relationship"),
  assesseeFullName: text("assessee_full_name"),
  assesseeEmail: text("assessee_email"),
  assesseePhone: text("assessee_phone"),
  dateOfBirth: timestamp("date_of_birth"),
  assesseeYear: text("assessee_year"),
  assesseeCourse: text("assessee_course"),
  reasonForReferral: text("reason_for_referral"),
  previousAssessment: text("previous_assessment"),
  previousAssessmentDetails: text("previous_assessment_details"),
  additionalNotes: text("additional_notes"),
  assessmentConcerns: text("assessment_concerns"),
  schoolContactConsent: boolean("school_contact_consent").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertReferralSchema = createInsertSchema(referrals).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

// Public tracking records
export const publicTrackingRecords = pgTable("public_tracking_records", {
  id: serial("id").primaryKey(),
  trackingId: text("tracking_id").notNull().unique(),
  assessmentId: integer("assessment_id").references(() => assessments.id).notNull(),
  universityId: integer("university_id").references(() => universities.id),
  assesseeId: integer("assessee_id").references(() => assessees.id).notNull(),
  expiresAt: timestamp("expires_at"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Forms table
export const forms = pgTable("forms", {
  id: serial("id").primaryKey(),
  assessmentId: integer("assessment_id").references(() => assessments.id).notNull(),
  formType: text("form_type").notNull(), // 'parent_under_16', 'assessee_over_16', 'school'
  status: formStatusEnum("status").notNull().default('not_started'),
  data: text("data"), // JSON string of form data (kept for backward compatibility)
  completedById: integer("completed_by_id").references(() => users.id),
  completedAt: timestamp("completed_at"),
  accessToken: text("access_token").unique(), // Secure token for accessing form without login
  accessTokenExpiresAt: timestamp("access_token_expires_at"), // Token expiration date
  lastAccessedAt: timestamp("last_accessed_at"), // Track when form was last accessed
  lastAccessedIp: text("last_accessed_ip"), // Track IP of last access for security
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Form Questions table (static questions)
export const formQuestions = pgTable("form_questions", {
  id: serial("id").primaryKey(),
  formType: text("form_type").notNull(), // 'parent_under_16', 'assessee_over_16', 'school'
  section: text("section").notNull(), // e.g., 'personal_info', 'educational_history', etc.
  questionKey: text("question_key").notNull(), // unique identifier for the question
  questionText: text("question_text").notNull(), // actual question text
  questionType: text("question_type").notNull(), // 'text', 'select', 'radio', 'checkbox', etc.
  required: boolean("required").default(false),
  options: text("options"), // JSON string of options for select, radio, checkbox
  order: integer("order").notNull(), // for ordering questions within a section
});

// Form Responses table (actual answers)
export const formResponses = pgTable("form_responses", {
  id: serial("id").primaryKey(),
  formId: integer("form_id").references(() => forms.id).notNull(),
  questionId: integer("question_id").references(() => formQuestions.id).notNull(),
  responseText: text("response_text"), // text response
  responseBoolean: boolean("response_boolean"), // boolean response
  responseNumber: integer("response_number"), // numeric response
  responseOptions: text("response_options"), // JSON string for multiple selections
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Documents table
export const documents = pgTable("documents", {
  id: serial("id").primaryKey(),
  assessmentId: integer("assessment_id").references(() => assessments.id).notNull(),
  name: text("name").notNull(),
  type: text("type").notNull(), // 'report', 'attachment', etc.
  data: text("data"), // Base64 encoded data or reference to file location (legacy)
  filePath: text("file_path"), // File system path for uploaded files
  fileName: text("file_name"), // System generated filename
  originalName: text("original_name"), // Original filename from upload
  mimeType: text("mime_type"), // File MIME type
  fileSize: integer("file_size"), // File size in bytes
  uploadedById: integer("uploaded_by_id").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
});

// Activities for audit log
export const activities = pgTable("activities", {
  id: serial("id").primaryKey(),
  assessmentId: integer("assessment_id").references(() => assessments.id),
  userId: integer("user_id").references(() => users.id),
  action: text("action").notNull(),
  details: text("details"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Admin activity logs
export const adminActivityLogs = pgTable("admin_activity_logs", {
  id: serial("id").primaryKey(),
  adminId: integer("admin_id").references(() => users.id).notNull(),
  action: text("action").notNull(),
  targetUserId: integer("target_user_id").references(() => users.id),
  targetUniversityId: integer("target_university_id").references(() => universities.id),
  details: text("details"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Conversations table (holds message threads)
export const conversations = pgTable("conversations", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  assessmentId: integer("assessment_id").references(() => assessments.id), // Optional, conversations can be general or assessment-specific
  createdById: integer("created_by_id").references(() => users.id).notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Conversation participants (users involved in a conversation)
export const conversationParticipants = pgTable("conversation_participants", {
  id: serial("id").primaryKey(),
  conversationId: integer("conversation_id").references(() => conversations.id).notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  joinedAt: timestamp("joined_at").defaultNow(),
  leftAt: timestamp("left_at"), // When user was removed or left the conversation
});

// Messages within conversations
export const messages = pgTable("messages", {
  id: serial("id").primaryKey(),
  conversationId: integer("conversation_id").references(() => conversations.id).notNull(),
  senderId: integer("sender_id").references(() => users.id).notNull(),
  content: text("content").notNull(),
  attachmentId: integer("attachment_id").references(() => documents.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at"),
  editedAt: timestamp("edited_at"),
});

// Message status for each recipient (read status etc.)
export const messageRecipients = pgTable("message_recipients", {
  id: serial("id").primaryKey(),
  messageId: integer("message_id").references(() => messages.id).notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  status: messageStatusEnum("status").default('unread'),
  readAt: timestamp("read_at"),
});

// Notifications (system and user-generated)
export const notifications = pgTable("notifications", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  type: notificationTypeEnum("type").notNull(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  status: messageStatusEnum("status").default('unread'),
  url: text("url"), // Optional link to relevant content
  sourceId: text("source_id"), // Reference ID to the source (assessmentId, messageId, etc.)
  sourceType: text("source_type"), // Type of source ('assessment', 'message', 'system', etc.)
  data: jsonb("data"), // Additional data as JSON
  createdAt: timestamp("created_at").defaultNow(),
  readAt: timestamp("read_at"),
});

// Notification preferences for users
export const notificationPreferences = pgTable("notification_preferences", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  // Preferences stored as JSON
  preferences: jsonb("preferences").notNull(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Issues table for application issue tracking
export const issues = pgTable("issues", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  priority: text("priority").notNull().default('medium'), // low, medium, high, critical
  status: text("status").notNull().default('open'), // open, in_progress, resolved, closed
  category: text("category").notNull(), // bug, feature_request, enhancement, other
  reportedById: integer("reported_by_id").references(() => users.id).notNull(),
  assignedToId: integer("assigned_to_id").references(() => users.id),
  attachments: jsonb("attachments").default([]), // Array of attachment file paths/URLs
  metadata: jsonb("metadata"), // Additional structured data (browser, OS, etc.)
  resolvedAt: timestamp("resolved_at"),
  resolvedById: integer("resolved_by_id").references(() => users.id),
  resolutionNotes: text("resolution_notes"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Insert schemas
export const insertUniversitySchema = createInsertSchema(universities).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertPublicTrackingRecordSchema = createInsertSchema(publicTrackingRecords).omit({
  id: true,
  createdAt: true
});

export const insertUserSchema = createInsertSchema(users).omit({ 
  id: true, 
  createdAt: true,
  updatedAt: true
});

// Create the base schema
const baseAssesseeSchema = createInsertSchema(assessees).omit({ 
  id: true, 
  createdAt: true 
});

// Add custom refinement to handle date format conversions
export const insertAssesseeSchema = baseAssesseeSchema.transform((data) => {
  // Convert dateOfBirth to Date if it's a string
  if (data.dateOfBirth && typeof data.dateOfBirth === 'string') {
    return {
      ...data,
      dateOfBirth: new Date(data.dateOfBirth)
    };
  }
  return data;
});

export const insertSchoolSchema = createInsertSchema(schools).omit({ 
  id: true, 
  createdAt: true 
});

// Create the base schema
const baseAssessmentSchema = createInsertSchema(assessments).omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

// Add custom refinement to handle date format conversions
export const insertAssessmentSchema = baseAssessmentSchema.transform((data) => {
  const result = { ...data };
  
  // Convert date fields to proper Date objects if they're strings
  if (data.scheduledDate && typeof data.scheduledDate === 'string') {
    result.scheduledDate = new Date(data.scheduledDate);
  }
  
  if (data.depositPaidAt && typeof data.depositPaidAt === 'string') {
    result.depositPaidAt = new Date(data.depositPaidAt);
  }
  
  if (data.finalPaidAt && typeof data.finalPaidAt === 'string') {
    result.finalPaidAt = new Date(data.finalPaidAt);
  }
  
  if (data.completedDate && typeof data.completedDate === 'string') {
    result.completedDate = new Date(data.completedDate);
  }
  
  return result;
});

export const insertFormSchema = createInsertSchema(forms).omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

export const insertDocumentSchema = createInsertSchema(documents).omit({ 
  id: true, 
  createdAt: true 
});

export const insertActivitySchema = createInsertSchema(activities).omit({ 
  id: true, 
  createdAt: true 
});

export const insertFormQuestionSchema = createInsertSchema(formQuestions).omit({
  id: true
});

export const insertFormResponseSchema = createInsertSchema(formResponses).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

// Insert schemas for messaging and notification system
export const insertConversationSchema = createInsertSchema(conversations).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertConversationParticipantSchema = createInsertSchema(conversationParticipants).omit({
  id: true,
  joinedAt: true
});

export const insertMessageSchema = createInsertSchema(messages).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  editedAt: true
});

export const insertMessageRecipientSchema = createInsertSchema(messageRecipients).omit({
  id: true,
  readAt: true
});

export const insertNotificationSchema = createInsertSchema(notifications).omit({
  id: true,
  createdAt: true,
  readAt: true
});

export const insertNotificationPreferenceSchema = createInsertSchema(notificationPreferences).omit({
  id: true,
  updatedAt: true
});

export const insertIssueSchema = createInsertSchema(issues).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  resolvedAt: true,
  resolvedById: true
});

// Types
export type University = typeof universities.$inferSelect;
export type InsertUniversity = z.infer<typeof insertUniversitySchema>;

export type PublicTrackingRecord = typeof publicTrackingRecords.$inferSelect;
export type InsertPublicTrackingRecord = z.infer<typeof insertPublicTrackingRecordSchema>;

export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Assessee = typeof assessees.$inferSelect;
export type InsertAssessee = z.infer<typeof insertAssesseeSchema>;

export type School = typeof schools.$inferSelect;
export type InsertSchool = z.infer<typeof insertSchoolSchema>;

export type Assessment = typeof assessments.$inferSelect;
export type InsertAssessment = z.infer<typeof insertAssessmentSchema>;

export type Form = typeof forms.$inferSelect;
export type InsertForm = z.infer<typeof insertFormSchema>;

export type FormQuestion = typeof formQuestions.$inferSelect;
export type InsertFormQuestion = z.infer<typeof insertFormQuestionSchema>;

export type FormResponse = typeof formResponses.$inferSelect;
export type InsertFormResponse = z.infer<typeof insertFormResponseSchema>;

export type Document = typeof documents.$inferSelect;
export type InsertDocument = z.infer<typeof insertDocumentSchema>;

export type Activity = typeof activities.$inferSelect;
export type InsertActivity = z.infer<typeof insertActivitySchema>;

export type Referral = typeof referrals.$inferSelect;
export type InsertReferral = z.infer<typeof insertReferralSchema>;

// Types for messaging and notification system
export type Conversation = typeof conversations.$inferSelect;
export type InsertConversation = z.infer<typeof insertConversationSchema>;

export type ConversationParticipant = typeof conversationParticipants.$inferSelect;
export type InsertConversationParticipant = z.infer<typeof insertConversationParticipantSchema>;

export type Message = typeof messages.$inferSelect;
export type InsertMessage = z.infer<typeof insertMessageSchema>;

export type MessageRecipient = typeof messageRecipients.$inferSelect;
export type InsertMessageRecipient = z.infer<typeof insertMessageRecipientSchema>;

export type Notification = typeof notifications.$inferSelect;
export type InsertNotification = z.infer<typeof insertNotificationSchema>;

export type NotificationPreference = typeof notificationPreferences.$inferSelect;
export type InsertNotificationPreference = z.infer<typeof insertNotificationPreferenceSchema>;

export type Issue = typeof issues.$inferSelect;
export type InsertIssue = z.infer<typeof insertIssueSchema>;

// Issue Comments table
export const issueComments = pgTable("issue_comments", {
  id: serial("id").primaryKey(),
  issueId: integer("issue_id").notNull().references(() => issues.id),
  userId: integer("user_id").notNull().references(() => users.id),
  comment: text("comment").notNull(),
  commentType: text("comment_type").notNull().default("update"), // update, status_change, assignment
  metadata: jsonb("metadata"), // For storing additional context like old/new status
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertIssueCommentSchema = createInsertSchema(issueComments).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type IssueComment = typeof issueComments.$inferSelect;
export type InsertIssueComment = z.infer<typeof insertIssueCommentSchema>;

// Assessment Notes table for timestamped notes
export const assessmentNotes = pgTable("assessment_notes", {
  id: serial("id").primaryKey(),
  assessmentId: integer("assessment_id").notNull().references(() => assessments.id),
  userId: integer("user_id").notNull().references(() => users.id),
  note: text("note").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertAssessmentNoteSchema = createInsertSchema(assessmentNotes).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type AssessmentNote = typeof assessmentNotes.$inferSelect;
export type InsertAssessmentNote = z.infer<typeof insertAssessmentNoteSchema>;
