A2lexa: User Types and Workflows
The A2lexa platform supports 6 distinct user types, each with their own specific workflows and capabilities:

1. Administrator
Role: Overall system management and oversight

Workflows:

Dashboard view of all assessments and system statistics
User management (create, edit, deactivate accounts)
Access to all assessments regardless of referral source
Assignment of assessors to assessments
Review and modification of assessment status
Financial tracking (payment status updates)
Report generation and analytics
System configuration and settings management

2. Assessor
Role: Professional who conducts the SpLD assessments

Workflows:

View assigned assessments on dashboard
Review pre-assessment forms (parent, school, assessee)
Schedule assessment appointments
Complete assessor form during/after assessment
Upload assessment documents and reports
Update assessment status (scheduled → assessment complete → report writing → completed)
Add notes to assessment cases
Communication with stakeholders

3. University
Role: Educational institution referring students for assessment

Workflows:

Submit new referrals for students
Complete university referral form
Track status of referred assessments
View assessment progress and results (limited)
Upload supporting documents
Receive and access final reports for referred students

4. School
Role: Educational institution involved in student assessment

Workflows:

View assessments for students from their school
Complete school pre-assessment questionnaire
Provide academic history and performance data
Upload supporting documents (test results, previous reports)
Track assessment progress
Access recommendations after assessment completion

5. Parent/Guardian
Role: Parent or guardian of an assessee under 16 years old

Workflows:

View child's assessment details and status
Complete parent/guardian pre-assessment questionnaire
Provide developmental and educational history
Submit payment for assessment services
Upload relevant documents (previous reports, medical information)
Schedule assessment appointment
Receive and access final assessment report

6. Assessee
Role: Individual over 16 years old being assessed

Workflows:

View own assessment details and status
Complete self-assessment questionnaire
Provide educational history and concerns
Submit payment for assessment services
Schedule assessment appointment
Receive and access final assessment report
Key Cross-User Workflows
Assessment Lifecycle
Enquiry Phase:
Referral received (from university, school, parent, or self)
Initial information gathering
Pre-assessment forms sent to appropriate parties
Pre-Assessment Phase:
Collection of completed forms (parent/assessee, school)
Document uploads
Payment processing (deposit)
Assessor assignment
Assessment Phase:
Scheduling
Assessment conducted
Assessor form completion
Additional document collection if needed
Post-Assessment Phase:
Report writing
Quality assurance review
Final payment collection
Report delivery to stakeholders
Form Completion Workflows
Under 16 Assessment: Parent form + School form + Assessor form
Over 16 Assessment: Assessee form + School form (optional) + Assessor form
University Referral: University form + Assessee/Parent form + School form + Assessor form
The system tracks the status of each form, allowing for proper workflow progression only when required forms are completed.

ID	Username	Email	Role
1.	parent (id: 1)
•	Email: <EMAIL>
•	Role: parent
•	Status: active
2.	university (id: 2)
•	Email: <EMAIL>
•	Role: university
•	Status: active
3.	testuser (id: 3)
•	Email: <EMAIL>
•	Role: parent
•	Status: active
4.	<EMAIL> (id: 4)
•	Email: <EMAIL>
•	Role: university
•	Status: active
5.	admin (id: 5)
•	Email: <EMAIL>
•	Role: admin
•	Status: active
6.	<EMAIL> (id: 6)
•	Email: <EMAIL>
•	Role: university
•	Status: active
