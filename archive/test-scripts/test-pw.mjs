// test-pw.mjs
import { scrypt, randomBytes, timingSafeEqual } from 'crypto';
import { promisify } from 'util';
const scryptAsync = promisify(scrypt);

async function comparePasswords(supplied, stored) {
  try {
    const [hashed, salt] = stored.split(".");
    const hashedBuf = Buffer.from(hashed, "hex");
    const suppliedBuf = await scryptAsync(supplied, salt, 64);
    return timingSafeEqual(hashedBuf, suppliedBuf);
  } catch (error) {
    console.error("Error comparing passwords:", error);
    return false;
  }
}

async function hashPassword(password) {
  const salt = randomBytes(16).toString("hex");
  const buf = await scryptAsync(password, salt, 64);
  return `${buf.toString("hex")}.${salt}`;
}

// Test with our stored password format
async function runTests() {
  const stored1 = "58c5c02315e43780ac20da845ee18c42cde8e0d2ca65f8456d90a48f67ea2f01d1c5e74f7c5be7bf7f762e7db6555dd87de0ce219a989d7af5d9518de98c2968.5f8a27fd326444e2ace9fad7112b544a";
  
  console.log("Testing user 1 password matches:");
  console.log("'test':", await comparePasswords("test", stored1));
  console.log("'password123':", await comparePasswords("password123", stored1));
  
  // Create a new password hash for 'test'
  const testHash = await hashPassword("test");
  console.log("\nGenerated hash for 'test':", testHash);
  console.log("Validating new hash:", await comparePasswords("test", testHash));
  
  // Compare with different password
  console.log("\nTesting with wrong password:");
  console.log("'wrongpass':", await comparePasswords("wrongpass", testHash));
}

runTests();