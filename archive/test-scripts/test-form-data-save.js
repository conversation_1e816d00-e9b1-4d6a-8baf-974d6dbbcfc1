/**
 * Test script to directly verify form data saving
 */
import pkg from 'pg';
const { Pool } = pkg;

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function testFormDataSaving() {
  console.log('Testing form data saving...');
  
  try {
    // Test form ID
    const formId = 25;
    
    // Create test data
    const testData = {
      firstName: "Test User",
      lastName: "Example",
      email: "<EMAIL>",
      phone: "************",
      dateOfBirth: "1990-01-01",
      fieldOne: "Value 1",
      fieldTwo: "Value 2",
      submittedAt: new Date().toISOString()
    };
    
    // Convert to JSON string
    const dataString = JSON.stringify(testData);
    console.log(`Test data created (${dataString.length} chars):`);
    console.log(JSON.stringify(testData, null, 2));
    
    // Update the form
    const updateResult = await pool.query(
      `UPDATE forms 
       SET data = $1, 
           status = 'in_progress',
           updated_at = NOW()
       WHERE id = $2
       RETURNING id, status, data`,
      [dataString, formId]
    );
    
    console.log('\nUpdate result:');
    console.log(updateResult.rows[0]);
    
    // Verify data was saved correctly
    const verifyResult = await pool.query(
      'SELECT id, status, data FROM forms WHERE id = $1',
      [formId]
    );
    
    console.log('\nVerify data was saved:');
    const savedForm = verifyResult.rows[0];
    console.log(`Form ID: ${savedForm.id}`);
    console.log(`Status: ${savedForm.status}`);
    console.log(`Data type: ${typeof savedForm.data}`);
    
    // Parse the saved data if it's a string
    if (typeof savedForm.data === 'string') {
      try {
        const parsedData = JSON.parse(savedForm.data);
        console.log('Parsed data:');
        console.log(JSON.stringify(parsedData, null, 2));
      } catch (e) {
        console.error('Error parsing saved data:', e);
        console.log('Raw data:', savedForm.data);
      }
    } else if (typeof savedForm.data === 'object') {
      console.log('Data (object):');
      console.log(JSON.stringify(savedForm.data, null, 2));
    } else {
      console.log('Raw data:', savedForm.data);
    }
  } catch (error) {
    console.error('Error testing form data saving:', error);
  } finally {
    await pool.end();
  }
}

testFormDataSaving();