/**
 * Admin Notification Test Script
 * 
 * This script tests the functionality of admin notifications for new referrals
 * by creating a test referral and checking if notifications are created
 */

import { db } from './server/db';
import { notifications, users } from './shared/schema';
import { eq } from 'drizzle-orm';

async function testAdminNotifications() {
  console.log('Testing admin notifications for new referrals...');
  
  try {
    // 1. Get all admin users
    const adminUsers = await db.select()
      .from(users)
      .where(eq(users.role, 'admin'));
    
    console.log(`Found ${adminUsers.length} admin users in the system`);
    
    if (adminUsers.length === 0) {
      console.log('No admin users found. Skipping test.');
      return;
    }
    
    // 2. List any admin notifications
    for (const admin of adminUsers) {
      console.log(`\nChecking notifications for admin: ${admin.email} (ID: ${admin.id})`);
      
      const userNotifications = await db.select()
        .from(notifications)
        .where(eq(notifications.userId, admin.id));
      
      console.log(`Found ${userNotifications.length} total notifications`);
      
      // Filter for assessment-related notifications
      const assessmentNotifs = userNotifications.filter(n => n.type === 'assessment');
      console.log(`Found ${assessmentNotifs.length} assessment notifications`);
      
      // Filter for unread notifications
      const unreadNotifs = userNotifications.filter(n => n.status === 'unread');
      console.log(`Found ${unreadNotifs.length} unread notifications`);
      
      // Display recent notifications
      console.log('\nRecent notifications:');
      const recentNotifs = userNotifications
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5);
      
      for (const notif of recentNotifs) {
        console.log(`- [${notif.status}] ${notif.title}: ${notif.content}`);
        console.log(`  Created: ${notif.createdAt}, Source: ${notif.sourceType}/${notif.sourceId}`);
      }
    }
    
    console.log('\nNotification test complete!');
  } catch (error) {
    console.error('Error testing admin notifications:', error);
  } finally {
    // Close the database connection
    await db.end?.();
  }
}

testAdminNotifications();