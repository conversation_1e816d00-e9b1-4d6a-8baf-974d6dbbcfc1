/**
 * Test script to verify the complete verification flow
 * 
 * This script simulates the entire process of approving a referral, creating forms,
 * and sending the appropriate emails with form links.
 */

import { db, pool } from './server/db';
import { DatabaseStorage } from './server/db-storage';
import { createFormsForAssessment } from './server/utils/assessment-form-creation';
import { sendAssessmentStatusEmail } from './server/utils/assessment-status-email';
import { Request } from 'express';

// Create a storage instance
const storage = new DatabaseStorage();

async function testCompleteVerificationFlow() {
  try {
    console.log('=== Starting Complete Verification Flow Test ===');

    // Create a more robust mock request object to simulate the HTTP request
    const mockRequest = {
      protocol: 'https',
      headers: {
        host: 'a2lexa.cloud9assist.com',
        'x-forwarded-proto': 'https'
      },
      user: {
        id: 5, // Admin ID
        email: '<EMAIL>'
      },
      isAuthenticated: () => true,
      get: function(header: string) {
        // Mimic Express request.get() method
        if (header.toLowerCase() === 'host') {
          return this.headers.host;
        }
        return this.headers[header.toLowerCase()] || null;
      }
    } as unknown as Request;

    // Get a real assessment ID to test with
    const assessmentId = 26; // Use a test assessment ID
    
    console.log(`Getting assessment with ID ${assessmentId}`);
    const assessment = await storage.getAssessment(assessmentId);
    if (!assessment) {
      console.error(`Assessment with ID ${assessmentId} not found`);
      return;
    }

    console.log(`Found assessment: ID=${assessment.id}, Status=${assessment.status}`);
    
    // Get assessee information
    console.log(`Getting assessee with ID ${assessment.assesseeId}`);
    const assessee = await storage.getAssessee(assessment.assesseeId);
    if (!assessee) {
      console.error(`Assessee not found for assessment ${assessmentId}`);
      return;
    }
    console.log(`Found assessee: ${assessee.fullName}`);

    // Create forms for the assessment
    console.log('Creating forms for assessment...');
    const formCreationResult = await createFormsForAssessment(assessmentId, mockRequest);
    console.log(`Form creation result: ${formCreationResult.message}`);
    console.log(`${formCreationResult.createdForms.length} forms created`);
    
    // Get base URL 
    const baseUrl = 'https://a2lexa.cloud9assist.com';
    
    // Create a map of form links by form type
    const formLinks: {[key: string]: string} = {};
    
    // Extract tokens and prepare secure form links
    console.log('Generating form links:');
    formCreationResult.createdForms.forEach(form => {
      if (form.accessToken) {
        formLinks[form.formType] = `${baseUrl}/forms/access/${form.accessToken}`;
        console.log(`  - Added link for ${form.formType}: ${baseUrl}/forms/access/${form.accessToken}`);
      }
    });
    
    // Debug logging
    console.log('formLinks object:', formLinks);
    console.log('formLinks keys:', Object.keys(formLinks));
    console.log('formLinks has keys:', Object.keys(formLinks).length > 0);
    
    // Make a deep copy for the email (this is where the issue might be)
    const formLinksForEmail = JSON.parse(JSON.stringify(formLinks));
    
    // Debug the copied object
    console.log('formLinksForEmail after deep clone:', formLinksForEmail);
    console.log('formLinksForEmail keys:', Object.keys(formLinksForEmail));
    
    // Test sending email with these links
    console.log('\nSending test email with form links...');
    const emailResult = await sendAssessmentStatusEmail(
      '<EMAIL>', // Test email address
      {
        assessmentId,
        assesseeName: assessee.fullName,
        oldStatus: 'Enquiry',
        newStatus: 'pre_assessment',
        updatedBy: 'Verification Flow Test',
        notes: 'This is a test of the complete verification flow. The secure form links should be displayed below.',
        formLinks: formLinksForEmail
      }
    );
    
    console.log('Email send result:', emailResult);
    console.log('Test complete ✅');
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    // Close database connection
    await pool.end();
  }
}

// Run the test
testCompleteVerificationFlow();