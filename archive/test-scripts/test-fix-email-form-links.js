/**
 * Test script for the fixed form links in email
 * 
 * This script tests whether the form links are correctly displayed in assessment status emails
 * after our fix to the email template.
 */

// Import necessary modules - using CommonJS
const { sendEmail } = require('./server/utils/sendgrid');
const { createEmailTemplate, createPlainTextEmail } = require('./server/utils/email-template-base');

// Since we can't directly import the TypeScript file, we'll recreate the function here
async function sendTestAssessmentEmail(
  recipientEmail, 
  update
) {
  const fromEmail = process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.SENDGRID_FROM_NAME || 'A2lexa Assessment Platform Test';
  
  const subject = `TEST EMAIL - Assessment Status Update: ${update.assesseeName}`;
  
  // Format status for display
  const formatStatus = (status) => {
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  
  const oldStatusDisplay = formatStatus(update.oldStatus);
  const newStatusDisplay = formatStatus(update.newStatus);
  
  // Create the content for the email with conditional notes section
  let content = `
    <p>Hello there,</p>
    <p>The status of an assessment has been updated in the A2lexa platform.</p>
    
    <div style="background-color: #f3f4f6; border-radius: 4px; padding: 20px; margin: 20px 0;">
      <h3 style="margin-top: 0; color: #10b981;">Status Update Details</h3>
      <p><strong>Assessment ID:</strong> ${update.assessmentId}</p>
      <p><strong>Assessee Name:</strong> ${update.assesseeName}</p>
      <p><strong>Previous Status:</strong> ${oldStatusDisplay}</p>
      <p><strong>New Status:</strong> <span style="color: #10b981; font-weight: 600;">${newStatusDisplay}</span></p>
      <p><strong>Updated By:</strong> ${update.updatedBy}</p>
    </div>
    
    <p style="color: red; font-weight: bold;">THIS IS A TEST EMAIL - FORM LINKS SHOULD APPEAR BELOW</p>
  `;
  
  // Add notes section if provided
  if (update.notes) {
    content += `
      <div style="background-color: #f9fafb; border-left: 4px solid #6b7280; padding: 15px; margin: 20px 0;">
        <h4 style="margin-top: 0; color: #4b5563;">Notes:</h4>
        <p style="margin-bottom: 0;">${update.notes}</p>
      </div>
    `;
  }
  
  console.log('DEBUG EMAIL: update.formLinks:', update.formLinks);
  console.log('DEBUG EMAIL: update.formLinks type:', typeof update.formLinks);
  console.log('DEBUG EMAIL: update.formLinks keys:', update.formLinks ? Object.keys(update.formLinks) : 'no keys');
  console.log('DEBUG EMAIL: update.formLinks values:', update.formLinks ? Object.values(update.formLinks) : 'no values');
  
  // Add form links if they exist and the status is pre_assessment
  if (update.newStatus === 'pre_assessment') {
    // Make a copy of formLinks to avoid any issues with proxies or modifications
    const formLinksForEmail = update.formLinks ? {...update.formLinks} : {};
    
    // Log the form links for debugging
    console.log('DEBUG EMAIL: formLinksForEmail:', formLinksForEmail);
    console.log('DEBUG EMAIL: formLinksForEmail keys:', Object.keys(formLinksForEmail));
    
    // If there are form links to display
    if (formLinksForEmail && Object.keys(formLinksForEmail).length > 0) {
      content += `
        <div style="background-color: #eff6ff; border-left: 4px solid #3b82f6; padding: 15px; margin: 20px 0;">
          <h4 style="margin-top: 0; color: #1e40af;">Complete Your Pre-Assessment Forms</h4>
          
          <p style="font-weight: 500; margin-bottom: 10px;">You have TWO OPTIONS to complete your forms:</p>
          
          <div style="background-color: #ffffff; border: 1px solid #dbeafe; border-radius: 6px; padding: 12px; margin-bottom: 15px;">
            <p style="font-weight: 600; color: #1e40af; margin-top: 0; margin-bottom: 8px;">Option 1: Direct Form Links (Fastest Method)</p>
            <p style="margin-top: 0; margin-bottom: 10px;">Click the secure links below to access forms directly without logging in:</p>
            <ul style="margin-bottom: 10px;">
      `;
      
      // Add each form link with appropriate label
      Object.entries(formLinksForEmail).forEach(([formType, link]) => {
        let formName = "Pre-Assessment Form";
        
        // Set appropriate form name based on type
        if (formType === 'school') {
          formName = "School Pre-Assessment Questionnaire";
        } else if (formType === 'parent_under_16') {
          formName = "Parent/Guardian Pre-Assessment Questionnaire";
        } else if (formType === 'assessee_over_16') {
          formName = "Assessee Pre-Assessment Questionnaire";
        }
        
        content += `
          <li style="margin-bottom: 8px;">
            <a href="${link}" style="color: #2563eb; font-weight: 500; text-decoration: underline;">
              ${formName}
            </a>
            <span style="display: block; font-size: 0.9em; color: #4b5563; margin-top: 2px;">
              This link allows direct secure access without logging in. No account needed.
            </span>
          </li>
        `;
      });
      
      content += `
            </ul>
            <p style="font-size: 0.9em; color: #6b7280; margin-bottom: 0;">These secure links will expire in 30 days. No login required.</p>
          </div>
          
          <div style="background-color: #ffffff; border: 1px solid #dbeafe; border-radius: 6px; padding: 12px;">
            <p style="font-weight: 600; color: #1e40af; margin-top: 0; margin-bottom: 8px;">Option 2: Login To Your Account</p>
            <p style="margin-top: 0; margin-bottom: 10px;">You can also access forms by logging into your A2lexa account:</p>
            <ol style="margin-bottom: 10px;">
              <li style="margin-bottom: 5px;">Click the "View Assessment" button below</li>
              <li style="margin-bottom: 5px;">Navigate to the "Forms" section of the assessment</li>
              <li style="margin-bottom: 0px;">Complete each required form</li>
            </ol>
            <p style="font-size: 0.9em; color: #6b7280; margin-bottom: 0;">This method requires you to set up your account password first.</p>
          </div>
        </div>
      `;
    } else {
      // If no form links, still show the options section but with a message about missing links
      content += `
        <div style="background-color: #eff6ff; border-left: 4px solid #3b82f6; padding: 15px; margin: 20px 0;">
          <h4 style="margin-top: 0; color: #1e40af;">Complete Your Pre-Assessment Forms</h4>
          
          <p style="font-weight: 500; margin-bottom: 10px;">You have TWO OPTIONS to complete your forms:</p>
          
          <div style="background-color: #ffffff; border: 1px solid #dbeafe; border-radius: 6px; padding: 12px; margin-bottom: 15px;">
            <p style="font-weight: 600; color: #1e40af; margin-top: 0; margin-bottom: 8px;">Option 1: Direct Form Links (Fastest Method)</p>
            <p style="margin-top: 0; margin-bottom: 10px;">The form links are being prepared and will be available shortly. Please try Option 2 below or check back later.</p>
          </div>
          
          <div style="background-color: #ffffff; border: 1px solid #dbeafe; border-radius: 6px; padding: 12px;">
            <p style="font-weight: 600; color: #1e40af; margin-top: 0; margin-bottom: 8px;">Option 2: Login To Your Account</p>
            <p style="margin-top: 0; margin-bottom: 10px;">You can access forms by logging into your A2lexa account:</p>
            <ol style="margin-bottom: 10px;">
              <li style="margin-bottom: 5px;">Click the "View Assessment" button below</li>
              <li style="margin-bottom: 5px;">Navigate to the "Forms" section of the assessment</li>
              <li style="margin-bottom: 0px;">Complete each required form</li>
            </ol>
            <p style="font-size: 0.9em; color: #6b7280; margin-bottom: 0;">This method requires you to set up your account password first.</p>
          </div>
        </div>
      `;
    }
  }
  
  content += `
    <p>Please log in to the A2lexa platform to view the complete assessment details.</p>
  `;

  // Create the email using our template
  const templateOptions = {
    title: 'TEST - Assessment Status Update',
    preheader: `Assessment for ${update.assesseeName} has been updated to ${newStatusDisplay}`,
    content,
    buttonText: 'View Assessment',
    buttonUrl: `https://d2c3c615-061f-430e-89fd-a14158851d0a-00-2202dtwxh8oas.spock.replit.dev/assessments/${update.assessmentId}`
  };

  // Generate HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  try {
    return await sendEmail({
      to: recipientEmail,
      from: {
        email: fromEmail,
        name: fromName
      },
      subject,
      html,
      text
    });
  } catch (error) {
    console.error('Error sending assessment status update email:', error);
    return false;
  }
}

async function testFixedFormLinks() {
  try {
    console.log('Starting fixed form links email test...');

    // Create test form links
    const formLinks = {
      'school': 'https://test.com/forms/access/school-token-123',
      'parent_under_16': 'https://test.com/forms/access/parent-token-456',
      'assessee_over_16': 'https://test.com/forms/access/assessee-token-789'
    };

    // Create cloned formLinks to test
    const formLinksForEmail = {...formLinks};
    
    console.log('Form links object type:', typeof formLinksForEmail);
    console.log('Form links keys:', Object.keys(formLinksForEmail));
    console.log('Form links values:', Object.values(formLinksForEmail));

    // Send test email with form links to your own email for testing
    const result = await sendTestAssessmentEmail(
      // Replace with the email to test
      '<EMAIL>', 
      {
        assessmentId: 99999,
        assesseeName: 'Test Student',
        oldStatus: 'Enquiry',
        newStatus: 'pre_assessment',
        updatedBy: 'Test Fix Script',
        notes: 'This is a test email to verify the fixed form links implementation.',
        formLinks: formLinksForEmail
      }
    );

    console.log('Email sent successfully:', result);
  } catch (error) {
    console.error('Error in test:', error);
  }
}

testFixedFormLinks();