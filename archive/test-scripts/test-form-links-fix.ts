/**
 * Test script to verify the fix for form links in assessment emails
 * 
 * This script creates test form links and passes them to the email template
 * to check if they are correctly displayed in the email content.
 */

import { sendAssessmentStatusEmail } from './server/utils/assessment-status-email';

/**
 * Main test function that simulates an assessment status update email with form links
 */
async function testFormLinksFix() {
  try {
    console.log('=== Starting Form Links Email Fix Test ===');

    // Create test form links object
    const testFormLinks = {
      'school': 'https://test-domain.com/forms/access/school-token-123',
      'parent_under_16': 'https://test-domain.com/forms/access/parent-token-456',
      'assessee_over_16': 'https://test-domain.com/forms/access/assessee-token-789'
    };

    // Create a clone of formLinks to ensure we're testing the issue
    console.log('Original formLinks object:', testFormLinks);
    const clonedFormLinks = {...testFormLinks};
    console.log('Cloned formLinks object:', clonedFormLinks);
    
    console.log('Checking JSON deep clone...');
    const jsonClonedFormLinks = JSON.parse(JSON.stringify(testFormLinks));
    console.log('JSON cloned formLinks object:', jsonClonedFormLinks);

    // Show differences with a new field
    console.log('\nType comparison:');
    console.log('Original formLinks type:', typeof testFormLinks);
    console.log('Cloned formLinks type:', typeof clonedFormLinks);
    console.log('JSON cloned formLinks type:', typeof jsonClonedFormLinks);

    // Testing adding fields
    console.log('\nTrying to modify objects...');
    testFormLinks['test_field'] = 'original value';
    console.log('Original formLinks after modification:', testFormLinks);
    console.log('Cloned formLinks after original modification:', clonedFormLinks);
    console.log('JSON cloned formLinks after original modification:', jsonClonedFormLinks);

    // Send a test email using the assessment-status-email utils
    console.log('\nAttempting to send test email with form links...');
    const result = await sendAssessmentStatusEmail(
      // Use your test email address here
      '<EMAIL>', 
      {
        assessmentId: 99999,
        assesseeName: 'Test Student',
        oldStatus: 'Enquiry',
        newStatus: 'pre_assessment',
        updatedBy: 'Form Links Fix Test',
        notes: 'This is a test email to verify that form links are properly displayed in the email content after our fix.',
        // Use the fixed deep cloning JSON.parse + JSON.stringify as done in the fixed code
        formLinks: JSON.parse(JSON.stringify(testFormLinks))
      }
    );

    console.log('Email send result:', result);
    console.log('Test complete ✅');
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
testFormLinksFix();