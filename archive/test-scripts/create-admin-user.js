import crypto from 'crypto';
import { promisify } from 'util';
import pg from 'pg';

const { scrypt, randomBytes } = crypto;
const scryptAsync = promisify(scrypt);

async function hashPassword(password) {
  const salt = randomBytes(16).toString("hex");
  const buf = await scryptAsync(password, salt, 64);
  return `${buf.toString("hex")}.${salt}`;
}

async function createAdminUser() {
  // Create new connection to database
  const pool = new pg.Pool({
    connectionString: process.env.DATABASE_URL
  });

  try {
    // Check if user with this email exists
    const { rows } = await pool.query('SELECT id, username, email, role FROM users WHERE email = $1', ['<EMAIL>']);
    
    // Generate a proper password hash
    const hashedPassword = await hashPassword("password123");
    console.log("Generated password hash:", hashedPassword);
    
    let result;
    
    if (rows.length > 0) {
      // Update existing user to be admin
      result = await pool.query(`
        UPDATE users 
        SET role = 'admin', 
            username = 'admin', 
            password = $1,
            full_name = 'System Administrator'
        WHERE email = $2
        RETURNING id, username, email, role;
      `, [hashedPassword, '<EMAIL>']);
      
      console.log("Updated existing user to admin:", result.rows[0]);
    } else {
      // Create new admin user
      result = await pool.query(`
        INSERT INTO users (username, email, password, full_name, role, phone)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, username, email, role;
      `, [
        'admin', 
        '<EMAIL>',
        hashedPassword,
        'System Administrator',
        'admin',
        ''
      ]);
      
      console.log("Created new admin user:", result.rows[0]);
    }
  } catch (error) {
    console.error("Error creating admin user:", error);
  } finally {
    await pool.end();
  }
}

createAdminUser().catch(console.error);