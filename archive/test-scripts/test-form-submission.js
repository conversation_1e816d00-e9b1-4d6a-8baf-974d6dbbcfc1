/**
 * Test script to verify form submission is working properly
 */
import fetch from 'node-fetch';

// Test form data to submit
const testFormData = {
  firstName: "<PERSON>",
  lastName: "Do<PERSON>",
  dateOfBirth: "1990-01-01",
  email: "<EMAIL>",
  phone: "************",
  address: "123 Test Street",
  city: "Test City",
  postcode: "AB12 3CD",
  submittedAt: new Date().toISOString()
};

async function testFormSubmission() {
  try {
    // First login to get a session
    const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'  // Assuming this is the test password
      }),
      credentials: 'include'
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.statusText}`);
    }
    
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('Login successful, got cookies:', cookies);
    
    // Now update the form with our test data
    const formUpdateResponse = await fetch('http://localhost:5000/api/forms/23', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies
      },
      body: JSON.stringify({
        status: 'completed',
        data: JSON.stringify(testFormData)
      }),
      credentials: 'include'
    });
    
    if (!formUpdateResponse.ok) {
      throw new Error(`Form update failed: ${formUpdateResponse.statusText}`);
    }
    
    const result = await formUpdateResponse.json();
    console.log('Form update successful:', result);
    
    console.log('Testing complete - form should now have data stored properly.');
  } catch (error) {
    console.error('Error during test:', error);
  }
}

testFormSubmission();