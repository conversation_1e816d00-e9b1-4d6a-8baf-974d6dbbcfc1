/**
 * Test script for the form links in email
 */

import { sendAssessmentStatusEmail } from './server/utils/assessment-status-email';

async function testFormLinksEmail() {
  try {
    console.log('Starting email test...');

    // Create test form links
    const formLinks = {
      'school': 'https://test.com/forms/access/school-token',
      'parent_under_16': 'https://test.com/forms/access/parent-token',
      'assessee_over_16': 'https://test.com/forms/access/assessee-token'
    };

    // Create cloned formLinks to test
    const formLinksForEmail = {...formLinks};
    
    console.log('Form links object type:', typeof formLinksForEmail);
    console.log('Form links values:', Object.values(formLinksForEmail));

    // Send test email with form links
    const result = await sendAssessmentStatusEmail(
      // Replace with your email to test
      '<EMAIL>',
      {
        assessmentId: 123,
        assesseeName: 'Test Student',
        oldStatus: 'Enquiry',
        newStatus: 'pre_assessment',
        updatedBy: 'Test Script',
        notes: 'This is a test email to verify form links are correctly included.',
        formLinks: formLinksForEmail
      }
    );

    console.log('Email sent successfully:', result);
  } catch (error) {
    console.error('Error in test:', error);
  }
}

testFormLinksEmail();