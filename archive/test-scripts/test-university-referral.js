/**
 * Test script for creating a university referral with course program and year fields
 */
import { db } from './server/db-config.js';
import { assessments, assessees, users } from './shared/schema.js';
import { eq } from 'drizzle-orm';

async function createTestUniversityReferral() {
  try {
    console.log('Creating test university referral with course program and year of study...');
    
    // Create a test university staff user if needed
    let universityUser = await db.query.users.findFirst({
      where: eq(users.email, '<EMAIL>')
    });
    
    if (!universityUser) {
      console.log('Creating university staff user...');
      const [newUser] = await db.insert(users).values({
        email: '<EMAIL>',
        username: 'testuniversity',
        fullName: 'Test University Staff',
        role: 'university',
        passwordHash: '$2b$10$jUL7xrhZVeBXKAQU9wJKIuLJ3mOZkEv7GEeSRZUAp7awJVXjIEA.W', // 'password'
        status: 'active',
        organization: 'Test University'
      }).returning();
      
      universityUser = newUser;
      console.log(`Created university user with ID: ${universityUser.id}`);
    } else {
      console.log(`Found existing university user with ID: ${universityUser.id}`);
    }
    
    // Create a test student with course program and year of study
    const [testAssessee] = await db.insert(assessees).values({
      fullName: 'University Test Student',
      dateOfBirth: new Date('2000-01-01'),
      email: '<EMAIL>',
      phone: '1234567890',
      courseProgram: 'Computer Science',
      yearOfStudy: 'Year 2'
    }).returning();
    
    console.log(`Created assessee with ID: ${testAssessee.id}`);
    
    // Create an assessment record
    const [testAssessment] = await db.insert(assessments).values({
      assesseeId: testAssessee.id,
      referringUserId: universityUser.id,
      referralType: 'university',
      status: 'Enquiry',
      paymentStatus: 'unpaid',
      notes: JSON.stringify({
        departmentName: 'School of Computing',
        studentId: 'STU12345',
        disabilityOfficer: 'Jane Smith',
        concernDetails: 'Difficulty with reading and comprehension',
        reasonForReferral: 'Student struggles with timed examinations',
        additionalInformation: 'Student performs well in practical coursework'
      })
    }).returning();
    
    console.log(`Created assessment with ID: ${testAssessment.id}`);
    console.log('Test university referral created successfully!');
    
    console.log('\nReferral details:');
    console.log(`Student: ${testAssessee.fullName}`);
    console.log(`Course/Program: ${testAssessee.courseProgram}`);
    console.log(`Year of Study: ${testAssessee.yearOfStudy}`);
    console.log(`Referral Type: ${testAssessment.referralType}`);
    console.log(`Status: ${testAssessment.status}`);
    
  } catch (error) {
    console.error('Error creating test university referral:', error);
  } finally {
    process.exit(0);
  }
}

createTestUniversityReferral()
  .catch(err => console.error('Error in main execution:', err));