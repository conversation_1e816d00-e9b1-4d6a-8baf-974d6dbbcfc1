import crypto from 'crypto';
import { promisify } from 'util';
import pg from 'pg';

const { scrypt, randomBytes } = crypto;
const scryptAsync = promisify(scrypt);

async function hashPassword(password) {
  const salt = randomBytes(16).toString("hex");
  const buf = await scryptAsync(password, salt, 64);
  return `${buf.toString("hex")}.${salt}`;
}

async function fixAllUsers() {
  // Create new connection to database
  const pool = new pg.Pool({
    connectionString: process.env.DATABASE_URL
  });

  try {
    // Get all users
    const { rows: users } = await pool.query('SELECT id, username FROM users');
    console.log(`Found ${users.length} users to update`);
    
    // Create a secure random password for all users
    const securePassword = randomBytes(12).toString('hex');
    console.log("Generated secure random password (save this for distribution):", securePassword);
    const hashedPassword = await hashPassword(securePassword);
    console.log("Generated password hash:", hashedPassword);
    
    // Update each user
    for (const user of users) {
      await pool.query(`
        UPDATE users SET password = $1 WHERE id = $2
      `, [hashedPassword, user.id]);
      
      console.log(`Updated password for user ${user.username} (ID: ${user.id})`);
    }
    
    console.log("All users updated successfully!");
    
    // Verify updated users
    const { rows: updatedUsers } = await pool.query('SELECT id, username, SUBSTRING(password, 1, 20) as password_sample FROM users');
    console.table(updatedUsers);
    
  } catch (error) {
    console.error("Error updating users:", error);
  } finally {
    await pool.end();
  }
}

fixAllUsers().catch(console.error);