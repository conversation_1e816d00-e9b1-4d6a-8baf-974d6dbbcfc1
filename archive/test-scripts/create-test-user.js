import crypto from 'crypto';
import { promisify } from 'util';
import pg from 'pg';

const { scrypt, randomBytes } = crypto;
const scryptAsync = promisify(scrypt);

async function hashPassword(password) {
  const salt = randomBytes(16).toString("hex");
  const buf = await scryptAsync(password, salt, 64);
  return `${buf.toString("hex")}.${salt}`;
}

async function createTestUser() {
  // Create new connection to database
  const pool = new pg.Pool({
    connectionString: process.env.DATABASE_URL
  });

  try {
    // Generate a proper password hash
    const hashedPassword = await hashPassword("testuser123");
    console.log("Generated password hash:", hashedPassword);
    
    // Create test user
    const result = await pool.query(`
      INSERT INTO users (username, email, password, full_name, role, phone)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, username, email, role;
    `, [
      'testuser', 
      '<EMAIL>',
      hashedPassword,
      'Test User',
      'parent',
      '************'
    ]);
    
    console.log("Created new test user:", result.rows[0]);
  } catch (error) {
    console.error("Error creating test user:", error);
    
    // Check if user already exists
    if (error.code === '23505') { // Unique violation
      console.log("Test user already exists, updating password instead");
      
      // Update password
      const hashedPassword = await hashPassword("testuser123");
      const result = await pool.query(`
        UPDATE users 
        SET password = $1
        WHERE username = 'testuser'
        RETURNING id, username, email, role;
      `, [hashedPassword]);
      
      console.log("Updated test user password:", result.rows[0]);
    }
  } finally {
    await pool.end();
  }
}

createTestUser().catch(console.error);