import crypto from 'crypto';
import { promisify } from 'util';

const { scrypt, randomBytes, timingSafeEqual } = crypto;

const scryptAsync = promisify(scrypt);

async function hashPassword(password) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64));
  return `${buf.toString("hex")}.${salt}`;
}

async function comparePasswords(supplied, stored) {
  console.log("Comparing passwords:");
  console.log("Supplied password length:", supplied.length);
  console.log("Stored password format:", stored.includes(".") ? "valid" : "invalid");
  
  const [hashed, salt] = stored.split(".");
  if (!hashed || !salt) {
    console.log("ERROR: Invalid stored password format, missing hash or salt");
    return false;
  }
  
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64));
  
  const isEqual = timingSafeEqual(hashedBuf, suppliedBuf);
  console.log("Password match result:", isEqual);
  return isEqual;
}

async function runTest() {
  // Create a new hash for 'password123'
  const hashedPwd = await hashPassword("password123");
  console.log("Newly generated hash:", hashedPwd);
  
  // Test with the password we set for users in the database
  const storedHash = "13d439b42a3986b9b2f58193195eae9ee348cb9967e673514c4c4884bb4d80796e886b53225201c278d6be36bbf91f952bec6670d0c522321036220d54e29463.f38ed6ee6ee77d80b5dd8e53089a6f18";
  
  // Check if new password hash matches old one
  console.log("\nTEST 1: Testing if newly generated hash matches stored hash:");
  const newHashMatches = await comparePasswords("password123", hashedPwd);
  console.log("New hash validation:", newHashMatches ? "SUCCESS" : "FAILURE");
  
  // Check if 'password123' works with our stored hash
  console.log("\nTEST 2: Testing if 'password123' validates against stored hash:");
  const storedHashMatches = await comparePasswords("password123", storedHash);
  console.log("Stored hash validation:", storedHashMatches ? "SUCCESS" : "FAILURE");
  
  // Additional test - create two hashes for same password
  console.log("\nTEST 3: Creating multiple hashes for same password:");
  const hash1 = await hashPassword("password123");
  const hash2 = await hashPassword("password123");
  console.log("Hash 1:", hash1);
  console.log("Hash 2:", hash2);
  console.log("Are hashes identical?", hash1 === hash2);
  console.log("Does Hash 1 validate?", await comparePasswords("password123", hash1) ? "YES" : "NO");
  console.log("Does Hash 2 validate?", await comparePasswords("password123", hash2) ? "YES" : "NO");
}

// Run the test
runTest().catch(console.error);