-- Add the missing columns to the forms table
-- These columns already exist in the schema but not in the database

-- First check if the access_token column exists
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'forms' AND column_name = 'access_token'
  ) THEN
    -- Add access_token column
    ALTER TABLE forms ADD COLUMN access_token TEXT UNIQUE;
    RAISE NOTICE 'Added access_token column to forms table';
  ELSE
    RAISE NOTICE 'access_token column already exists in forms table';
  END IF;
  
  -- Check if the access_token_expires_at column exists
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'forms' AND column_name = 'access_token_expires_at'
  ) THEN
    -- Add access_token_expires_at column
    ALTER TABLE forms ADD COLUMN access_token_expires_at TIMESTAMP;
    RAISE NOTICE 'Added access_token_expires_at column to forms table';
  ELSE
    RAISE NOTICE 'access_token_expires_at column already exists in forms table';
  END IF;
  
  -- Check if last_accessed_at column exists
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'forms' AND column_name = 'last_accessed_at'
  ) THEN
    -- Add last_accessed_at column
    ALTER TABLE forms ADD COLUMN last_accessed_at TIMESTAMP;
    RAISE NOTICE 'Added last_accessed_at column to forms table';
  ELSE
    RAISE NOTICE 'last_accessed_at column already exists in forms table';
  END IF;
  
  -- Check if last_accessed_ip column exists
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'forms' AND column_name = 'last_accessed_ip'
  ) THEN
    -- Add last_accessed_ip column
    ALTER TABLE forms ADD COLUMN last_accessed_ip TEXT;
    RAISE NOTICE 'Added last_accessed_ip column to forms table';
  ELSE
    RAISE NOTICE 'last_accessed_ip column already exists in forms table';
  END IF;
END $$;