-- Add new personal info columns to the assessees table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'assessees' AND column_name = 'preferred_name'
  ) THEN
    ALTER TABLE assessees ADD COLUMN preferred_name TEXT;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'assessees' AND column_name = 'phone_work'
  ) THEN
    ALTER TABLE assessees ADD COLUMN phone_work TEXT;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'assessees' AND column_name = 'hear_about'
  ) THEN
    ALTER TABLE assessees ADD COLUMN hear_about TEXT;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'assessees' AND column_name = 'hear_about_other'
  ) THEN
    ALTER TABLE assessees ADD COLUMN hear_about_other TEXT;
  END IF;
END $$;
