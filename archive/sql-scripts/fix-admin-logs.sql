-- First, check the constraint
SELECT tc.constraint_name, tc.table_name, kcu.column_name, 
       ccu.table_name AS foreign_table_name, 
       ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu 
  ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_name = 'admin_activity_logs_target_user_id_fkey';

-- Drop the existing constraint
ALTER TABLE admin_activity_logs 
DROP CONSTRAINT IF EXISTS admin_activity_logs_target_user_id_fkey;

-- Add the constraint back but with ON DELETE SET NULL
ALTER TABLE admin_activity_logs
ADD CONSTRAINT admin_activity_logs_target_user_id_fkey
FOREIGN KEY (target_user_id) 
REFERENCES users(id) 
ON DELETE SET NULL;