/**
 * Migration script to move university department data from assessment notes to user records
 * 
 * This script:
 * 1. Finds all university referrals in the system
 * 2. Extracts the department name from the assessment notes
 * 3. Updates the associated university user record with the department information
 */

import { db } from './server/db.js';
import { assessments, users } from './shared/schema.js';
import { eq, and } from 'drizzle-orm';

async function migrateDepartmentData() {
  try {
    console.log('Starting university department migration...');
    
    // Find all assessments with referralType = 'university'
    const universityAssessments = await db.select()
      .from(assessments)
      .where(eq(assessments.referralType, 'university'));
    
    console.log(`Found ${universityAssessments.length} university referral assessments`);
    
    // Process each assessment
    let migratedCount = 0;
    let skippedCount = 0;
    
    for (const assessment of universityAssessments) {
      // Skip if there are no notes or no referring user
      if (!assessment.notes || !assessment.referringUserId) {
        console.log(`Skipping assessment ${assessment.id} - missing data`);
        skippedCount++;
        continue;
      }
      
      // Parse the notes JSON
      let notes;
      try {
        notes = typeof assessment.notes === 'string' 
          ? JSON.parse(assessment.notes)
          : assessment.notes;
      } catch (error) {
        console.log(`Error parsing notes for assessment ${assessment.id}: ${error.message}`);
        skippedCount++;
        continue;
      }
      
      // Extract department name
      const departmentName = notes.departmentName;
      
      if (!departmentName) {
        console.log(`Skipping assessment ${assessment.id} - no department name in notes`);
        skippedCount++;
        continue;
      }
      
      // Update the user record with the department
      await db.update(users)
        .set({ department: departmentName })
        .where(eq(users.id, assessment.referringUserId));
      
      console.log(`Updated user ${assessment.referringUserId} with department: ${departmentName}`);
      migratedCount++;
    }
    
    console.log('\nMigration summary:');
    console.log(`Total university assessments: ${universityAssessments.length}`);
    console.log(`Successfully migrated: ${migratedCount}`);
    console.log(`Skipped: ${skippedCount}`);
    console.log('Migration completed!');
    
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the migration
migrateDepartmentData();