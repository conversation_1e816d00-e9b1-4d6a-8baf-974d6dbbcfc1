import { logger } from './logger';
import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth } from "./auth";
import { insertAssessmentSchema, insertAssesseeSchema, insertFormSchema, insertDocumentSchema, insertActivitySchema } from "@shared/schema";
import { z } from "zod";
import { handlePublicUniversityReferral } from "./handlers/public-university-referral";
import { 
  requireAdmin, 
  getAllUsers, 
  getUserById, 
  updateUser, 
  inviteUser,
  getActivityLogs,
  deleteUser,
  resetUserPassword
} from "./handlers/admin-handlers";
import { handlePublicIndividualReferral } from "./handlers/public-individual-referral";
import { sendTestNotification } from "./handlers/test-notification";
import { handleTestFormSubmission } from "./handlers/test-form-handler";
import { 
  csrfProtection, 
  publicFormRateLimiter, 
  sanitizeInput, 
  sanitizeRichText 
} from './security-middleware';
import { verifyRecaptchaV3 } from './utils/recaptcha-v3-verification';
import {
  getPendingVerifications,
  approveReferral,
  rejectReferral,
  updateAssessmentNotes,
  getAssessmentDetails,
  updateAssessee
} from "./handlers/verification-handlers";
import {
  getAllAssessments,
  resetAssessmentStatus
} from "./handlers/all-assessments";
import {
  getAllUsers,
  updateUser
} from "./handlers/user-handlers";
import {
  getConversations,
  getConversation,
  createConversation,
  addParticipant,
  removeParticipant,
  sendMessage,
  editMessage,
  getNotifications,
  getUnreadNotificationCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getNotificationPreferences,
  updateNotificationPreferences
} from "./handlers/communication-handlers";
import { testPasswordResetEmail, testDirectEmail, testBasicTemplateEmail } from "./utils/email-test";
import debugEmailRouter from "./routes/debug-email";
import adminRouter from "./routes/admin";
import authRouter from "./routes/auth";
import assessmentsRouter from "./routes/assessments";
import usersRouter from "./routes/users";
import assesseesRouter from "./routes/assessees";
import { 
  generatePasswordResetToken, 
  generatePasswordResetLink,
  sendWelcomeEmailWithPasswordReset
} from "./utils/password-reset";

// Helper function to get the base URL from request
function getBaseUrlFromRequest(req: Request): string {
  return `${req.protocol}://${req.get('host')}`;
}

async function isUserRelatedToAssessee(userId: number, assesseeId: number): Promise<boolean> {
  try {
    // For parent relationship, assessee will have parent user ID
    const assessee = await storage.getAssessee(assesseeId);
    if (!assessee) return false;
    
    // Direct relationship (user is the parent of this assessee)
    if (assessee.parentId === userId) return true;
    
    // If the user is the assessee themselves
    const user = await storage.getUser(userId);
    if (user && user.role === 'assessee' && user.id === assessee.userId) return true;
    
    return false;
  } catch (error) {
    logger.error("Error checking user-assessee relationship:", error);
    return false;
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // CSRF Token endpoint for forms - generate a token without verification
  app.get('/api/csrf-token', async (req, res) => {
    try {
      // Import crypto using ES modules syntax
      const { randomBytes } = await import('crypto');
      
      // Generate a simple random token
      const token = randomBytes(32).toString('hex');
      logger.info('Simple security token generated successfully');
      
      // Store in session if available (but no type error if this fails - it's not critical)
      try {
        if (req.session) {
          (req.session as any).csrfToken = token;
        }
      } catch (e) {
        logger.info('Could not store token in session, but proceeding anyway');
      }
      
      res.json({ csrfToken: token });
    } catch (error) {
      logger.error('Error generating security token:', error);
      res.status(500).json({ message: 'Failed to generate security token' });
    }
  });
  
  // Import the clean reCAPTCHA v3 verification function
  const { verifyRecaptchaV3 } = await import('./utils/recaptcha-v3-verification.js');
  
  // Secure public routes with rate limiting and custom token validation
  app.post('/api/public/university-referral', 
    publicFormRateLimiter,
    async (req, res, next) => {
      logger.info('=== University referral submission received ===');
      logger.info('Request body keys:', Object.keys(req.body));
      logger.info('reCAPTCHA token present:', !!req.body.recaptchaToken);
      logger.info('reCAPTCHA token preview:', req.body.recaptchaToken?.substring(0, 20) + '...');
      logger.info('Request headers csrf-related:', {
        csrf_body: req.body._csrf,
        csrf_header: req.headers['x-csrf-token'],
        csrf_token_header: req.headers['csrf-token']
      });
      try {
        // Custom token validation
        if (!req.body._csrf && !req.headers['x-csrf-token'] && !req.headers['csrf-token']) {
          return res.status(403).json({ 
            error: 'Security token missing. Please refresh the page and try again.' 
          });
        }
        
        // For development environment or when using manual verification
        if (process.env.NODE_ENV !== 'production' && 
            (req.body._csrf === 'manual_verification_token' || 
             req.headers['x-csrf-token'] === 'manual_verification_token' ||
             req.headers['csrf-token'] === 'manual_verification_token')) {
          logger.info('Using development CSRF bypass token');
        } else {
          // Verify the token in session if available
          // This is a fallback and not critical if it fails
          try {
            if (req.session && (req.session as any).csrfToken) {
              const sessionToken = (req.session as any).csrfToken;
              const providedToken = req.body._csrf || 
                                   req.headers['x-csrf-token'] || 
                                   req.headers['csrf-token'];
              
              if (sessionToken !== providedToken) {
                logger.warn('CSRF token mismatch - session:', sessionToken, 'provided:', providedToken);
              }
            }
          } catch (e) {
            logger.info('Could not verify token against session, but proceeding anyway');
          }
        }
        
        // reCAPTCHA verification - required for public endpoints
        const recaptchaToken = req.body.recaptchaToken;
        if (!recaptchaToken) {
          return res.status(403).json({
            error: 'reCAPTCHA verification required. Please complete the verification and try again.'
          });
        }
        
        // Verify the reCAPTCHA v3 token
        const isValidRecaptcha = await verifyRecaptchaV3(recaptchaToken, 'university_referral');
        if (!isValidRecaptcha) {
          logger.info('reCAPTCHA v3 verification failed for token:', recaptchaToken);
          return res.status(403).json({
            error: "Sorry, something went wrong verifying you're human. Please try again."
          });
        }
        
        // Sanitize all text inputs before processing
        if (req.body) {
          // Define allowed keys to prevent prototype pollution
          const allowedKeys = [
            'firstName', 'lastName', 'email', 'phone', 'dateOfBirth',
            'address', 'parentName', 'parentEmail', 'parentPhone',
            'schoolName', 'schoolContact', 'concerns', 'previousAssessments',
            'additionalInformation', 'specialArrangements', 'recaptchaToken', '_csrf'
          ];
          
          Object.keys(req.body).forEach(key => {
            // Only process allowed keys and ignore prototype-related keys
            if (allowedKeys.includes(key) && typeof req.body[key] === 'string') {
              // Use sanitizeRichText for fields that need to allow some HTML
              if (['additionalInformation', 'specialArrangements'].includes(key)) {
                req.body[key] = sanitizeRichText(req.body[key]);
              } else {
                req.body[key] = sanitizeInput(req.body[key]);
              }
            }
          });
        }
        next();
      } catch (error) {
        logger.error('Error in public form middleware:', error);
        res.status(500).json({ error: 'An error occurred processing your request' });
      }
    },
    handlePublicUniversityReferral
  );
  
  app.post('/api/public/individual-referral', 
    publicFormRateLimiter,
    async (req, res, next) => {
      try {
        // Custom token validation
        if (!req.body._csrf && !req.headers['x-csrf-token'] && !req.headers['csrf-token']) {
          return res.status(403).json({ 
            error: 'Security token missing. Please refresh the page and try again.' 
          });
        }
        
        // For development environment or when using manual verification
        if (process.env.NODE_ENV !== 'production' && 
            (req.body._csrf === 'manual_verification_token' || 
             req.headers['x-csrf-token'] === 'manual_verification_token' ||
             req.headers['csrf-token'] === 'manual_verification_token')) {
          logger.info('Using development CSRF bypass token');
        } else {
          // Verify the token in session if available
          // This is a fallback and not critical if it fails
          try {
            if (req.session && (req.session as any).csrfToken) {
              const sessionToken = (req.session as any).csrfToken;
              const providedToken = req.body._csrf || 
                                   req.headers['x-csrf-token'] || 
                                   req.headers['csrf-token'];
              
              if (sessionToken !== providedToken) {
                logger.warn('CSRF token mismatch - session:', sessionToken, 'provided:', providedToken);
              }
            }
          } catch (e) {
            logger.info('Could not verify token against session, but proceeding anyway');
          }
        }
        
        // reCAPTCHA verification - required for public endpoints
        const recaptchaToken = req.body.recaptchaToken;
        if (!recaptchaToken) {
          return res.status(403).json({
            error: 'reCAPTCHA verification required. Please complete the verification and try again.'
          });
        }
        
        // Verify the reCAPTCHA v3 token
        const isValidRecaptcha = await verifyRecaptchaV3(recaptchaToken, 'individual_referral');
        if (!isValidRecaptcha) {
          logger.info('reCAPTCHA v3 verification failed for token:', recaptchaToken);
          return res.status(403).json({
            error: "Sorry, something went wrong verifying you're human. Please try again."
          });
        }
        
        // Sanitize all text inputs before processing
        if (req.body) {
          // Define allowed keys to prevent prototype pollution
          const allowedKeys = [
            'firstName', 'lastName', 'email', 'phone', 'dateOfBirth',
            'address', 'parentName', 'parentEmail', 'parentPhone',
            'schoolName', 'schoolContact', 'concerns', 'previousAssessments',
            'additionalInformation', 'specialArrangements', 'recaptchaToken', '_csrf'
          ];
          
          Object.keys(req.body).forEach(key => {
            // Only process allowed keys and ignore prototype-related keys
            if (allowedKeys.includes(key) && typeof req.body[key] === 'string') {
              // Use sanitizeRichText for fields that need to allow some HTML
              if (['additionalInformation', 'specialArrangements'].includes(key)) {
                req.body[key] = sanitizeRichText(req.body[key]);
              } else {
                req.body[key] = sanitizeInput(req.body[key]);
              }
            }
          });
        }
        next();
      } catch (error) {
        logger.error('Error in public form middleware:', error);
        res.status(500).json({ error: 'An error occurred processing your request' });
      }
    },
    handlePublicIndividualReferral
  );
  
  // Diagnostic route to verify password checking
  app.get("/api/test-password", async (req, res) => {
    try {
      const { scrypt, randomBytes, timingSafeEqual } = await import('crypto');
      const { promisify } = await import('util');
      const scryptAsync = promisify(scrypt);
      
      const comparePasswords = async (supplied: string, stored: string) => {
        try {
          const [hashed, salt] = stored.split(".");
          const hashedBuf = Buffer.from(hashed, "hex");
          const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
          return timingSafeEqual(hashedBuf, suppliedBuf);
        } catch (error) {
          logger.error("Password comparison error:", error);
          return false;
        }
      };
      
      // Get the stored password from the database
      const user1 = await storage.getUser(1);
      const user2 = await storage.getUser(2);
      
      // Test with some password combinations
      const results = {
        user1Password: user1?.password.substring(0, 20) + "...",
        user2Password: user2?.password.substring(0, 20) + "...",
        test_password123_match_user1: user1 ? await comparePasswords("password123", user1.password) : false,
        test_password123_match_user2: user2 ? await comparePasswords("password123", user2.password) : false,
        test_test_match_user1: user1 ? await comparePasswords("test", user1.password) : false,
        test_testuni_match_user2: user2 ? await comparePasswords("testuni", user2.password) : false
      };
      
      res.json(results);
    } catch (error) {
      logger.error("Test password error:", error);
      res.status(500).json({ message: "Error testing passwords" });
    }
  });
  
  // Set up authentication routes
  setupAuth(app);
  
  app.use(adminRouter);
  app.use(authRouter);
  app.use(assessmentsRouter);
  app.use(usersRouter);
  app.use(assesseesRouter);

  // API Routes
  // Users
  app.get("/api/users", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      // Only admins can list all users
      if (req.user.role !== 'admin') {
        return res.status(403).json({ message: "Only admins can access this resource" });
      }
      
      const role = req.query.role as string | undefined;
      const users = await storage.listUsers(role);
      
      // Remove passwords from response
      const sanitizedUsers = users.map(user => {
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      });
      
      res.json(sanitizedUsers);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });
  
  // Get users for select dropdown (for messaging, etc.)
  app.get("/api/users/select", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const role = req.query.role as string | undefined;
      const users = await storage.listUsers(role);
      
      // Remove sensitive information from response and return minimal data
      const sanitizedUsers = users
        .filter(user => user.status === 'active' && user.id !== req.user.id) // Filter active users and exclude current user
        .map(user => ({
          id: user.id,
          email: user.email,
          fullName: user.fullName || user.email.split('@')[0], // Fallback to username part of email if no full name
          role: user.role,
        }));
      
      res.json(sanitizedUsers);
    } catch (error) {
      logger.error("Error fetching users for selection:", error);
      res.status(500).json({ message: "Failed to fetch users for selection" });
    }
  });
  
  app.get("/api/users/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const userId = parseInt(req.params.id);
      
      // Users can only access their own data unless they're admins
      if (req.user.id !== userId && req.user.role !== 'admin') {
        return res.status(403).json({ message: "You don't have permission to access this resource" });
      }
      
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      res.json(userWithoutPassword);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });
  
  // Assessees
  app.post("/api/assessees", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      // Log what we receive from the client side
      logger.info('Received assessee data:', req.body);
      
      // Pre-process the date if needed
      let processedData = { ...req.body };
      // Ensure dateOfBirth is a Date object if it's a string
      if (typeof processedData.dateOfBirth === 'string') {
        processedData.dateOfBirth = new Date(processedData.dateOfBirth);
      }
      
      const data = insertAssesseeSchema.parse(processedData);
      
      // Validate permissions based on role
      if (req.user.role === 'parent' && data.parentId !== req.user.id) {
        return res.status(403).json({ message: "Parent can only create assessees for themselves" });
      }
      
      const assessee = await storage.createAssessee(data);
      
      // Log activity
      await storage.createActivity({
        userId: req.user.id,
        action: 'create_assessee',
        details: `Created assessee: ${assessee.fullName}`
      });
      
      res.status(201).json(assessee);
    } catch (error) {
      logger.error('Error creating assessee:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create assessee" });
    }
  });
  
  app.get("/api/assessees", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      let assessees = [];
      
      // Filter by parent ID if specified
      if (req.query.parentId) {
        const parentId = parseInt(req.query.parentId as string);
        // Check permissions
        if (req.user.role !== 'admin' && req.user.id !== parentId) {
          return res.status(403).json({ message: "Not authorized to view these assessees" });
        }
        assessees = await storage.listAssesseesByParent(parentId);
      } 
      // Filter by school ID if specified
      else if (req.query.schoolId) {
        const schoolId = parseInt(req.query.schoolId as string);
        // For school users, check if this is their school
        if (req.user.role === 'school') {
          const schools = await storage.listSchools();
          const userSchool = schools.find(s => s.userId === req.user.id);
          if (!userSchool || userSchool.id !== schoolId) {
            return res.status(403).json({ message: "Not authorized to view these assessees" });
          }
        }
        assessees = await storage.listAssesseesBySchool(schoolId);
      }
      // Return relevant assessees based on user role
      else {
        // Admin can see all assessees
        if (req.user.role === 'admin' || req.user.role === 'assessor') {
          // No filter provided - for admin/assessor get all assessees
          // Use storage directly instead of trying to access assesseeIdCounter
          assessees = await storage.listAllAssessees();
        } 
        // Parent can only see their assessees
        else if (req.user.role === 'parent') {
          assessees = await storage.listAssesseesByParent(req.user.id);
        }
        // School can only see assessees in their school
        else if (req.user.role === 'school') {
          const schools = await storage.listSchools();
          const userSchool = schools.find(s => s.userId === req.user.id);
          if (userSchool) {
            assessees = await storage.listAssesseesBySchool(userSchool.id);
          }
        }
      }
      
      res.json(assessees);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch assessees" });
    }
  });

  
  // Assessments
  
  // Renew an expired form access token
  app.post("/api/forms/renew-token", async (req, res) => {
    logger.info(`Form token renewal request received`);
    
    try {
      // Request should include formId and assessmentId for verification
      const { formId, assessmentId } = req.body;
      
      if (!formId || !assessmentId) {
        return res.status(400).json({ 
          message: "Form ID and Assessment ID are required" 
        });
      }
      
      const { renewFormAccessToken } = await import('./utils/form-token-access');
      
      // Attempt to renew the token
      const newToken = await renewFormAccessToken(formId, assessmentId);
      
      if (!newToken) {
        return res.status(400).json({ 
          message: "Unable to renew token. The form may be completed or invalid." 
        });
      }
      
      // Return the new token
      res.json({ 
        success: true,
        token: newToken,
        message: "Form access token renewed successfully"
      });
    } catch (error) {
      logger.error('Error renewing form access token:', error);
      res.status(500).json({ 
        message: "An error occurred while renewing the access token",
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
  
  // Token-based form submission (public, no auth required)
  app.post("/api/forms/submit/:token", async (req, res) => {
    logger.info(`Token-based form submission request: ${req.params.token}`);
    
    try {
      const { validateFormAccessToken } = await import('./utils/form-token-access');
      
      // Validate the access token
      const form = await validateFormAccessToken(req.params.token, req);
      
      if (!form) {
        logger.info(`Invalid or expired token for form submission: ${req.params.token}`);
        return res.status(401).json({ 
          message: "Invalid or expired access token", 
          expired: true 
        });
      }
      
      // Form submissions should include responses
      if (!req.body.responses) {
        logger.info("Missing responses in submission:", req.body);
        return res.status(400).json({ message: "Form responses are required" });
      }
      
      // Make sure responses is treated as an array
      let responsesArray = req.body.responses;
      if (!Array.isArray(responsesArray)) {
        logger.info("Converting responses to array:", responsesArray);
        responsesArray = [responsesArray];
      }
      
      logger.info(`Processing ${responsesArray.length} responses for form ID ${form.id}`);
      
      // Handle both legacy and new response formats
      const responses = responsesArray.map((response: {questionId?: number, questionKey?: string, value: string}) => {
        // For submissions that might not have question IDs yet (direct form submissions)
        if (!response.questionId && response.questionKey) {
          logger.info(`Creating form response with questionKey ${response.questionKey}`);
          // Create a key-based response
          return {
            formId: form.id,
            questionKey: response.questionKey,
            value: response.value || ''
          };
        } else {
          // Standard response with question ID
          return {
            formId: form.id,
            questionId: response.questionId || 0,
            value: response.value || ''
          };
        }
      });
      
      // Save the responses
      const savedResponses = await storage.bulkUpsertFormResponses(responses);
      
      // If form is being marked as completed
      if (req.body.status === 'completed') {
        // Update form status with proper form data handling
        logger.info(`Completing form ${form.id} with data provided:`, req.body.data ? "Yes" : "No");
        
        // Ensure form data is properly saved
        let formDataToSave = req.body.data;
        
        // Print some diagnostic info about the data being saved
        if (formDataToSave) {
          try {
            // Check if the data is valid JSON
            const parsedData = JSON.parse(formDataToSave);
            logger.info(`Form data is valid JSON with ${Object.keys(parsedData).length} fields`);
            logger.info(`Sample fields:`, Object.keys(parsedData).slice(0, 5));
          } catch (e) {
            logger.error(`Error parsing form data as JSON:`, e);
            
            // If data isn't valid JSON, try to convert it
            if (typeof formDataToSave === 'object') {
              formDataToSave = JSON.stringify(formDataToSave);
              logger.info(`Converted form data object to JSON string`);
            }
          }
        } else {
          // If no data provided, try to use existing form data
          if (form.data) {
            logger.info(`Using existing form data during completion`);
            formDataToSave = form.data;
          }
        }
        
        // Update the form with all necessary fields
        logger.info(`Saving form ${form.id} as completed with data length:`, 
                    formDataToSave ? (typeof formDataToSave === 'string' ? formDataToSave.length : 'object') : 'null');
        
        // Debug the actual data that will be saved
        logger.info('Form data to be saved:', formDataToSave);
        
        // Make sure we're saving form data as a string if it's an object
        if (formDataToSave && typeof formDataToSave === 'object') {
          formDataToSave = JSON.stringify(formDataToSave);
          logger.info('Converted object to string for storage, new length:', formDataToSave.length);
        }
        
        await storage.updateForm(form.id, {
          status: 'completed',
          completedAt: new Date(),
          // For token-based access, no user ID is available
          completedById: null,
          // Make sure to save the form data properly
          data: formDataToSave
        });
        
        // Log activity (anonymous)
        await storage.createActivity({
          userId: null,
          assessmentId: form.assessmentId,
          action: 'complete_form',
          details: `${form.formType.replace('_', ' ')} form completed anonymously via token access`
        });
        
        // Get all forms for this assessment
        const allForms = await storage.getFormsByAssessment(form.assessmentId);
        const allCompleted = allForms.every(f => f.status === 'completed');
        
        // Get assessment
        const assessment = await storage.getAssessment(form.assessmentId);
        
        // If all forms are completed and assessment is in VerificationPending status,
        // update status to pre_assessment
        if (allCompleted && assessment && assessment.status === 'VerificationPending') {
          await storage.updateAssessment(assessment.id, { status: 'pre_assessment' });
          
          // Log status change
          await storage.createActivity({
            userId: null,
            assessmentId: form.assessmentId,
            action: 'update_status',
            details: `Assessment moved to Pre-Assessment phase after all forms completed`
          });
        }
      } else {
        // Just update the form data if not completing
        // Make sure to properly handle the form data - this is where partial form data gets saved
        if (req.body.data) {
          logger.info(`Updating form ${form.id} with data:`, req.body.data.substring(0, 100) + "...");
          
          // Ensure req.body.data is a valid JSON string
          let formDataToSave = req.body.data;
          if (typeof formDataToSave === 'object') {
            formDataToSave = JSON.stringify(formDataToSave);
            logger.info(`Converted form data object to JSON string`);
          }
          
          await storage.updateForm(form.id, {
            data: formDataToSave,
            status: 'in_progress',
            updatedAt: new Date()
          });
        } else {
          logger.info(`No form data provided for update of form ${form.id}`);
          await storage.updateForm(form.id, {
            data: form.data,
            updatedAt: new Date()
          });
        }
      }
      
      // Return success
      res.json({ 
        success: true, 
        message: "Form responses saved successfully",
        responses: savedResponses
      });
      
    } catch (error) {
      logger.error('Error in token-based form submission:', error);
      res.status(500).json({ 
        message: "An error occurred processing the form submission",
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
  
  // Token-based form access (public, no auth required)
  app.get("/api/forms/access/:token", async (req, res) => {
    logger.info(`Token-based form access request: ${req.params.token}`);
    
    try {
      const { validateFormAccessToken } = await import('./utils/form-token-access');
      
      // Validate the access token
      const form = await validateFormAccessToken(req.params.token, req);
      
      if (!form) {
        logger.info(`Invalid or expired token: ${req.params.token}`);
        return res.status(401).json({ 
          message: "Invalid or expired access token", 
          expired: true 
        });
      }
      
      // Get the assessment to include with response
      const assessment = await storage.getAssessment(form.assessmentId);
      if (!assessment) {
        logger.info(`Associated assessment not found: ${form.assessmentId}`);
        return res.status(404).json({ message: "Associated assessment not found" });
      }
      
      // Get assessee information
      const assessee = await storage.getAssessee(assessment.assesseeId);
      if (!assessee) {
        logger.info(`Assessee not found: ${assessment.assesseeId}`);
        return res.status(404).json({ message: "Assessee information not found" });
      }
      
      logger.info(`Valid token access for form ID ${form.id}, type: ${form.formType}`);
      
      // Get the form questions
      const questions = await storage.getFormQuestionsByType(form.formType);
      
      // Get existing responses if any
      const responses = await storage.getFormResponses(form.id);
      
      // Return the full form with questions and responses
      res.json({
        form,
        assessment,
        assessee,
        questions,
        responses
      });
    } catch (error) {
      logger.error('Error in token-based form access:', error);
      res.status(500).json({ message: "An error occurred processing the form access token" });
    }
  });
  
  // Token-based form update (public, no auth required)
  app.patch("/api/forms/access/:token", async (req, res) => {
    logger.info(`Token-based form update request: ${req.params.token}`);
    
    try {
      const token = req.params.token;
      
      // Find the form by token
      const form = await storage.getFormByAccessToken(token);
      if (!form) {
        return res.status(404).json({ message: "Form not found with the provided token" });
      }
      
      // Check if the token is expired
      if (form.accessTokenExpiresAt && new Date(form.accessTokenExpiresAt) < new Date()) {
        return res.status(401).json({ message: "Form access token has expired" });
      }
      
      // Check if the form is already completed
      if (form.status === 'completed') {
        return res.status(400).json({ message: "This form has already been completed and cannot be modified" });
      }
      
      // Get form update data from the request body
      const { status, data } = req.body;
      
      // Validate the status if provided
      if (status && !['not_started', 'in_progress', 'completed'].includes(status)) {
        return res.status(400).json({ message: "Invalid form status" });
      }
      
      // Update the form with the new data
      const updateData: any = {
        updatedAt: new Date()
      };
      
      if (status) {
        updateData.status = status;
      }
      
      if (data) {
        updateData.data = data;
      }
      
      // If form is being marked as completed, set the completion timestamp
      if (status === 'completed') {
        updateData.completedAt = new Date();
      }
      
      // Update the form
      const updatedForm = await storage.updateForm(form.id, updateData);
      
      if (!updatedForm) {
        return res.status(500).json({ message: "Failed to update form" });
      }
      
      // Create an activity record if the form was completed
      if (status === 'completed' && form.status !== 'completed') {
        try {
          await storage.createActivity({
            type: 'form_completed',
            assessmentId: form.assessmentId,
            userId: null, // No user ID for token-based submissions
            details: `${form.formType} form completed via token access`,
            createdAt: new Date()
          });
          
          logger.info(`Activity created for completion of form ${form.id}`);
        } catch (error) {
          logger.error(`Error creating activity for form completion:`, error);
          // Don't block the response for activity creation errors
        }
      }
      
      logger.info(`Successfully updated form ${form.id} via token access`);
      res.json(updatedForm);
    } catch (error) {
      logger.error(`Error in token-based form update:`, error);
      res.status(500).json({ message: "An error occurred processing the form update" });
    }
  });
  
  // Forms - authenticated access
  app.get("/api/forms/:id", async (req, res) => {
    logger.info(`Form access request for ID: ${req.params.id}`);
    logger.info(`Authentication status: ${req.isAuthenticated()}`);
    
    if (!req.isAuthenticated()) {
      logger.info('Unauthorized access attempt to form endpoint');
      return res.status(401).json({ message: "Authentication required" });
    }
    
    try {
      logger.info(`User authenticated: ${req.user.username}, ID: ${req.user.id}, Role: ${req.user.role}`);
      
      if (!req.params.id) {
        logger.info('Missing form ID parameter');
        return res.status(400).json({ message: "Missing form ID" });
      }
      
      // Extract a numeric ID even if embedded in other characters
      const matches = req.params.id.match(/(\d+)/);
      if (!matches || !matches[1]) {
        logger.info(`Invalid form ID format: ${req.params.id}`);
        return res.status(400).json({ message: "The string did not match the expected pattern." });
      }
      
      const formId = parseInt(matches[1]);
      logger.info(`Parsed form ID: ${formId}`);
      
      const form = await storage.getForm(formId);
      
      if (!form) {
        logger.info(`Form not found with ID: ${formId}`);
        return res.status(404).json({ message: "Form not found" });
      }
      
      logger.info(`Form found, assessment ID: ${form.assessmentId}`);
      
      // Check permissions
      const assessment = await storage.getAssessment(form.assessmentId);
      if (!assessment) {
        logger.info(`Associated assessment not found: ${form.assessmentId}`);
        return res.status(404).json({ message: "Associated assessment not found" });
      }
      
      logger.info(`Assessment found, checking user permissions for role: ${req.user.role}`);
      
      // Admin and assessor can access all forms
      if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
        logger.info(`User role requires additional permission check: ${req.user.role}`);
        // For other roles, check if they are related to this assessment
        const userAssessments = await storage.listAssessmentsByUser(req.user.id, req.user.role);
        logger.info(`User has access to ${userAssessments.length} assessments`);
        
        const hasAccess = userAssessments.find(a => a.id === form.assessmentId);
        if (!hasAccess) {
          logger.info(`Access denied: User does not have permission for assessment ${form.assessmentId}`);
          return res.status(403).json({ message: "Not authorized to view this form" });
        }
        logger.info('User has permission to access this form');
      } else {
        logger.info(`Admin/assessor role granted access: ${req.user.role}`);
      }
      
      logger.info(`Returning form data for ID: ${formId}`);
      res.json(form);
    } catch (error) {
      logger.error(`Error in /api/forms/:id endpoint:`, error);
      res.status(500).json({ message: "Failed to fetch form" });
    }
  });
  
  // Get forms for an assessment
  app.get("/api/assessments/:id/forms", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const assessmentId = parseInt(req.params.id);
      const assessment = await storage.getAssessment(assessmentId);
      
      if (!assessment) {
        return res.status(404).json({ message: "Assessment not found" });
      }
      
      // Admin and assessor can access all assessment forms
      if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
        // For other roles, check if they are related to this assessment
        const userAssessments = await storage.listAssessmentsByUser(req.user.id, req.user.role);
        if (!userAssessments.find(a => a.id === assessmentId)) {
          return res.status(403).json({ message: "Not authorized to view these forms" });
        }
      }
      
      const forms = await storage.getFormsByAssessment(assessmentId);
      res.json(forms);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch forms" });
    }
  });
  
  // Create a new form for an assessment
  app.post("/api/assessments/:id/forms", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    // Only admin and assessor can create forms
    if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
      return res.status(403).json({ message: "Not authorized to create forms" });
    }
    
    try {
      const assessmentId = parseInt(req.params.id);
      const assessment = await storage.getAssessment(assessmentId);
      
      if (!assessment) {
        return res.status(404).json({ message: "Assessment not found" });
      }
      
      // Check if the form type makes sense for this assessment
      const { formType } = req.body;
      
      // If it's a parent form, ensure the assessee is under 16
      if (formType === 'parent_under_16') {
        const assessee = await storage.getAssessee(assessment.assesseeId);
        if (!assessee) {
          return res.status(404).json({ message: "Assessee not found" });
        }
        
        const dob = new Date(assessee.dateOfBirth);
        const today = new Date();
        const age = today.getFullYear() - dob.getFullYear();
        
        // Check if they've had their birthday this year
        const hadBirthday = 
          today.getMonth() > dob.getMonth() || 
          (today.getMonth() === dob.getMonth() && today.getDate() >= dob.getDate());
        
        const actualAge = hadBirthday ? age : age - 1;
        
        if (actualAge >= 16) {
          return res.status(400).json({ 
            message: "Cannot create parent form for assessee 16 or older" 
          });
        }
      }
      
      // If it's an assessee form, ensure the assessee is over 16
      if (formType === 'assessee_over_16') {
        const assessee = await storage.getAssessee(assessment.assesseeId);
        if (!assessee) {
          return res.status(404).json({ message: "Assessee not found" });
        }
        
        const dob = new Date(assessee.dateOfBirth);
        const today = new Date();
        const age = today.getFullYear() - dob.getFullYear();
        
        // Check if they've had their birthday this year
        const hadBirthday = 
          today.getMonth() > dob.getMonth() || 
          (today.getMonth() === dob.getMonth() && today.getDate() >= dob.getDate());
        
        const actualAge = hadBirthday ? age : age - 1;
        
        if (actualAge < 16) {
          return res.status(400).json({ 
            message: "Cannot create assessee form for someone under 16" 
          });
        }
      }
      
      // Create the form
      const newForm = await storage.createForm({
        assessmentId,
        formType,
        status: 'not_started',
        completedById: null,
        data: req.body.data || null,
      });
      
      // Log activity
      await storage.createActivity({
        userId: req.user.id,
        assessmentId,
        action: 'create_form',
        details: `Created ${formType.replace('_', ' ')} form`
      });
      
      res.status(201).json(newForm);
    } catch (error) {
      res.status(500).json({ message: "Failed to create form" });
    }
  });
  
  app.patch("/api/forms/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      if (!req.params.id) {
        return res.status(400).json({ message: "Missing form ID" });
      }
      
      // Extract a numeric ID even if embedded in other characters
      const matches = req.params.id.match(/(\d+)/);
      if (!matches || !matches[1]) {
        return res.status(400).json({ message: "The string did not match the expected pattern." });
      }
      
      const formId = parseInt(matches[1]);
      const form = await storage.getForm(formId);
      
      if (!form) {
        return res.status(404).json({ message: "Form not found" });
      }
      
      // Check permissions based on form type and user role
      const assessment = await storage.getAssessment(form.assessmentId);
      if (!assessment) {
        return res.status(404).json({ message: "Associated assessment not found" });
      }
      
      // Admin and assessors can update any form
      if (req.user.role === 'admin' || req.user.role === 'assessor') {
        logger.info(`Admin/assessor ${req.user.role} updating form ${form.id}`);
        // Skip permission checks for admin/assessor
      } else {
        // For other roles, enforce form-specific permissions
        if (form.formType === 'school' && req.user.role !== 'school' && req.user.role !== 'university') {
          return res.status(403).json({ message: "Only schools or universities can update school forms" });
        } else if (form.formType === 'parent_under_16' && req.user.role !== 'parent') {
          return res.status(403).json({ message: "Only parents can update parent forms" });
        } else if (form.formType === 'assessee_over_16' && req.user.role !== 'assessee') {
          return res.status(403).json({ message: "Only assessees can update their own forms" });
        }
        
        // For parent/school/assessee, ensure they're connected to this assessment
        const userAssessments = await storage.listAssessmentsByUser(req.user.id, req.user.role);
        if (!userAssessments.find(a => a.id === form.assessmentId)) {
          return res.status(403).json({ message: "Not authorized to update this form" });
        }
      }
      
      // Get form data from request body
      let formData = req.body;
      
      logger.info("Received form data update:", JSON.stringify(formData, null, 2));
      
      // Make sure data is handled properly regardless of form status
      if (formData.data) {
        try {
          // Make sure it's a string - if it's already an object, stringify it
          if (typeof formData.data !== 'string') {
            formData.data = JSON.stringify(formData.data);
          }
          
          // Parse it to validate it's a proper JSON string
          const parsedData = JSON.parse(formData.data);
          logger.info("Valid form data received:", formData.data.substring(0, 100) + "...");
          
          // Add timestamp if not present
          if (!parsedData.submittedAt) {
            const dataObj = { ...parsedData, submittedAt: new Date().toISOString() };
            formData.data = JSON.stringify(dataObj);
          }
        } catch (error) {
          logger.error("Error with form data JSON format:", error);
          // Return error to client
          return res.status(400).json({ 
            message: "Invalid form data format. Please ensure data is valid JSON.",
            error: error.message
          });
        }
      } else {
        logger.warn("No form data in submission request");
      }
      
      // Add additional metadata if form is being completed
      if (req.body.status === 'completed') {
        formData = {
          ...formData,
          completedById: req.user.id,
          completedAt: new Date()
        };
        
        logger.info("Completing form with data:", JSON.stringify(formData, null, 2));
      }
      
      const updatedForm = await storage.updateForm(formId, formData);
      
      // Log activity
      await storage.createActivity({
        userId: req.user.id,
        assessmentId: form.assessmentId,
        action: 'update_form',
        details: `Updated ${form.formType} form`
      });
      
      // If this form was completed, check if all forms are completed
      if (req.body.status === 'completed') {
        const allForms = await storage.getFormsByAssessment(form.assessmentId);
        const allCompleted = allForms.every(f => f.status === 'completed');
        
        // If all forms are completed, update assessment status to pre_assessment
        if (allCompleted && assessment.status === 'VerificationPending') {
          await storage.updateAssessment(assessment.id, { status: 'pre_assessment' });
          
          // Log status change
          await storage.createActivity({
            userId: req.user.id,
            assessmentId: form.assessmentId,
            action: 'update_status',
            details: `Assessment moved to Pre-Assessment phase`
          });
        }
      }
      
      res.json(updatedForm);
    } catch (error) {
      res.status(500).json({ message: "Failed to update form" });
    }
  });
  
  // Form Questions API
  app.get("/api/form-questions/:formType", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const { formType } = req.params;
      
      if (!formType || typeof formType !== 'string' || formType.trim() === '') {
        return res.status(400).json({ message: "Invalid form type" });
      }
      
      // Validate form type is one of the allowed types
      const validFormTypes = ['parent_under_16', 'assessee_over_16', 'school'];
      if (!validFormTypes.includes(formType)) {
        return res.status(400).json({ message: "Unknown form type" });
      }
      
      try {
        const questions = await storage.getFormQuestionsByType(formType);
        res.json(questions);
      } catch (error) {
        // If the database table doesn't exist, return an empty array
        if (error.message && error.message.includes("does not exist")) {
          logger.info("Form questions table doesn't exist yet - returning empty array");
          res.json([]);
        } else {
          throw error; // Rethrow for the outer catch block
        }
      }
    } catch (error) {
      logger.error("Error fetching form questions:", error);
      res.status(500).json({ message: "Failed to fetch form questions" });
    }
  });
  
  app.get("/api/form-questions/:formType/:section", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const { formType, section } = req.params;
      
      if (!formType || typeof formType !== 'string' || formType.trim() === '') {
        return res.status(400).json({ message: "Invalid form type" });
      }
      
      if (!section || typeof section !== 'string' || section.trim() === '') {
        return res.status(400).json({ message: "Invalid section" });
      }
      
      // Validate form type
      const validFormTypes = ['parent_under_16', 'assessee_over_16', 'school'];
      if (!validFormTypes.includes(formType)) {
        return res.status(400).json({ message: "Unknown form type" });
      }
      
      const questions = await storage.getFormQuestionsBySection(formType, section);
      res.json(questions);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch form questions" });
    }
  });
  
  // Only admin and assessor can create/update form questions
  app.post("/api/form-questions", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
      return res.status(403).json({ message: "Not authorized to create form questions" });
    }
    
    try {
      // Validate required fields
      const { questionKey, questionText, questionType, formType } = req.body;
      
      if (!questionKey || typeof questionKey !== 'string' || questionKey.trim() === '') {
        return res.status(400).json({ message: "Question key is required" });
      }
      
      if (!questionText || typeof questionText !== 'string' || questionText.trim() === '') {
        return res.status(400).json({ message: "Question text is required" });
      }
      
      if (!questionType || typeof questionType !== 'string' || questionType.trim() === '') {
        return res.status(400).json({ message: "Question type is required" });
      }
      
      if (!formType || typeof formType !== 'string' || formType.trim() === '') {
        return res.status(400).json({ message: "Form type is required" });
      }
      
      // Validate form type
      const validFormTypes = ['parent_under_16', 'assessee_over_16', 'school'];
      if (!validFormTypes.includes(formType)) {
        return res.status(400).json({ message: "Unknown form type" });
      }
      
      // Validate question type
      const validQuestionTypes = ['text', 'textarea', 'number', 'select', 'radio', 'checkbox', 'boolean', 'date'];
      if (!validQuestionTypes.includes(questionType)) {
        return res.status(400).json({ message: "Unknown question type" });
      }
      
      const question = await storage.createFormQuestion(req.body);
      res.status(201).json(question);
    } catch (error) {
      res.status(500).json({ message: "Failed to create form question" });
    }
  });
  
  app.post("/api/form-questions/bulk", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
      return res.status(403).json({ message: "Not authorized to create form questions" });
    }
    
    try {
      // Validate that we have an array of questions
      if (!Array.isArray(req.body)) {
        return res.status(400).json({ message: "Request body must be an array of questions" });
      }
      
      // Validate each question in the array
      const validFormTypes = ['parent_under_16', 'assessee_over_16', 'school'];
      const validQuestionTypes = ['text', 'textarea', 'number', 'select', 'radio', 'checkbox', 'boolean', 'date'];
      
      for (let i = 0; i < req.body.length; i++) {
        const question = req.body[i];
        const { questionKey, questionText, questionType, formType } = question;
        
        if (!questionKey || typeof questionKey !== 'string' || questionKey.trim() === '') {
          return res.status(400).json({ 
            message: `Question at index ${i} has an invalid questionKey`,
            index: i
          });
        }
        
        if (!questionText || typeof questionText !== 'string' || questionText.trim() === '') {
          return res.status(400).json({ 
            message: `Question at index ${i} has an invalid questionText`,
            index: i
          });
        }
        
        if (!questionType || typeof questionType !== 'string' || questionType.trim() === '') {
          return res.status(400).json({ 
            message: `Question at index ${i} has an invalid questionType`,
            index: i
          });
        }
        
        if (!validQuestionTypes.includes(questionType)) {
          return res.status(400).json({ 
            message: `Question at index ${i} has an unknown questionType: ${questionType}`,
            index: i
          });
        }
        
        if (!formType || typeof formType !== 'string' || formType.trim() === '') {
          return res.status(400).json({ 
            message: `Question at index ${i} has an invalid formType`,
            index: i
          });
        }
        
        if (!validFormTypes.includes(formType)) {
          return res.status(400).json({ 
            message: `Question at index ${i} has an unknown formType: ${formType}`,
            index: i
          });
        }
      }
      
      const questions = await storage.bulkCreateFormQuestions(req.body);
      res.status(201).json(questions);
    } catch (error) {
      res.status(500).json({ message: "Failed to create form questions" });
    }
  });
  
  // Form Responses API
  app.get("/api/form-responses/:formId", async (req, res) => {
    logger.info(`Form responses request for form ID: ${req.params.formId}`);
    logger.info(`Authentication status: ${req.isAuthenticated()}`);
    logger.info(`Session ID: ${req.sessionID}`);
    
    if (!req.isAuthenticated()) {
      logger.info('Unauthorized access attempt to form responses endpoint');
      return res.status(401).json({ message: "Authentication required" });
    }
    
    try {
      logger.info(`User authenticated: ${req.user.username}, ID: ${req.user.id}, Role: ${req.user.role}`);
      
      if (!req.params.formId) {
        logger.info('Missing form ID parameter');
        return res.status(400).json({ message: "Missing form ID" });
      }
      
      // Extract a numeric ID even if embedded in other characters
      const matches = req.params.formId.match(/(\d+)/);
      if (!matches || !matches[1]) {
        logger.info(`Invalid form ID format: ${req.params.formId}`);
        return res.status(400).json({ message: "The string did not match the expected pattern." });
      }
      
      const formId = parseInt(matches[1]);
      logger.info(`Parsed form ID: ${formId}`);
      
      const form = await storage.getForm(formId);
      
      if (!form) {
        logger.info(`Form not found with ID: ${formId}`);
        return res.status(404).json({ message: "Form not found" });
      }
      
      logger.info(`Form found, assessment ID: ${form.assessmentId}`);
      
      // Check authorization - same as checking form access
      const assessment = await storage.getAssessment(form.assessmentId);
      if (!assessment) {
        logger.info(`Associated assessment not found: ${form.assessmentId}`);
        return res.status(404).json({ message: "Associated assessment not found" });
      }
      
      logger.info(`Assessment found, checking user permissions for role: ${req.user.role}`);
      
      // Admin and assessor can access all forms
      if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
        logger.info(`User role requires additional permission check: ${req.user.role}`);
        // For other roles, check if they are related to this assessment
        const userAssessments = await storage.listAssessmentsByUser(req.user.id, req.user.role);
        logger.info(`User has access to ${userAssessments.length} assessments`);
        
        const hasAccess = userAssessments.find(a => a.id === form.assessmentId);
        if (!hasAccess) {
          logger.info(`Access denied: User does not have permission for assessment ${form.assessmentId}`);
          return res.status(403).json({ message: "Not authorized to view these responses" });
        }
        logger.info('User has permission to access form responses');
      } else {
        logger.info(`Admin/assessor role granted access: ${req.user.role}`);
      }
      
      const responses = await storage.getFormResponses(formId);
      logger.info(`Found ${responses.length} responses for form ID: ${formId}`);
      res.json(responses);
    } catch (error) {
      logger.error(`Error in /api/form-responses/:formId endpoint:`, error);
      res.status(500).json({ message: "Failed to fetch form responses" });
    }
  });
  
  // New endpoint to fetch form responses with a clearer URL pattern
  app.get('/api/form-responses/form/:formId', async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    try {
      logger.info(`Form responses by form ID request for form ID: ${req.params.formId}`);
      
      const formId = parseInt(req.params.formId);
      if (isNaN(formId)) {
        return res.status(400).json({ message: "Invalid form ID" });
      }
      
      // Get the form to check permissions
      const form = await storage.getForm(formId);
      if (!form) {
        return res.status(404).json({ message: "Form not found" });
      }
      
      // Get the assessment to verify permission
      const assessment = await storage.getAssessment(form.assessmentId);
      if (!assessment) {
        return res.status(404).json({ message: "Assessment not found" });
      }
      
      // Check permissions based on user role
      if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
        let hasPermission = false;
        
        if (req.user.role === 'parent' || req.user.role === 'assessee') {
          // Parents and assessees can only access their own forms
          hasPermission = assessment.assesseeId && await isUserRelatedToAssessee(req.user.id, assessment.assesseeId);
        } else if (req.user.role === 'school') {
          // Schools can only access forms for their assessees
          const assessee = await storage.getAssessee(assessment.assesseeId);
          hasPermission = assessee && assessee.schoolId === req.user.id;
        } else if (req.user.role === 'university') {
          // University users can access forms for assessments they referred
          const userAssessments = await storage.listAssessmentsByUser(req.user.id, req.user.role);
          hasPermission = userAssessments.some(a => a.id === assessment.id);
        }
        
        if (!hasPermission) {
          return res.status(403).json({ message: "Not authorized to view these responses" });
        }
      }
      
      try {
        // Try to fetch form responses, return empty array if table doesn't exist yet
        const responses = await storage.getFormResponses(formId);
        res.json(responses);
      } catch (error) {
        // For database errors related to missing tables, return empty array instead
        if (error.message && error.message.includes("does not exist")) {
          logger.info("Form responses table doesn't exist yet - returning empty array");
          res.json([]);
        } else {
          throw error; // Re-throw other errors to be caught by the outer catch block
        }
      }
    } catch (error) {
      logger.error(`Error in /api/form-responses/form/:formId endpoint:`, error);
      res.status(500).json({ message: "Failed to fetch form responses" });
    }
  });
  
  app.post("/api/form-responses", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const { formId, responses } = req.body;
      
      if (!formId || typeof formId !== 'number' || !Number.isInteger(formId) || formId <= 0) {
        return res.status(400).json({ message: "Invalid form ID format" });
      }
      
      if (!Array.isArray(responses)) {
        return res.status(400).json({ message: "Responses must be an array" });
      }
      
      // Check authorization
      const form = await storage.getForm(formId);
      if (!form) {
        return res.status(404).json({ message: "Form not found" });
      }
      
      // Check if user is authorized to update this form - same rules as form patch endpoint
      const assessment = await storage.getAssessment(form.assessmentId);
      if (!assessment) {
        return res.status(404).json({ message: "Associated assessment not found" });
      }
      
      // Admin can update any form
      if (req.user.role !== 'admin') {
        if (form.formType === 'school' && req.user.role !== 'school' && req.user.role !== 'university') {
          return res.status(403).json({ message: "Only schools or universities can update school forms" });
        } else if (form.formType === 'parent_under_16' && req.user.role !== 'parent') {
          return res.status(403).json({ message: "Only parents can update parent forms" });
        } else if (form.formType === 'assessee_over_16' && req.user.role !== 'assessee') {
          return res.status(403).json({ message: "Only assessees can update their own forms" });
        }
        
        // For parent/school/assessee, ensure they're connected to this assessment
        const userAssessments = await storage.listAssessmentsByUser(req.user.id, req.user.role);
        if (!userAssessments.find(a => a.id === form.assessmentId)) {
          return res.status(403).json({ message: "Not authorized to update this form" });
        }
      }
      
      // Add formId to each response if not present
      const processedResponses = responses.map(response => ({
        ...response,
        formId: formId
      }));
      
      const savedResponses = await storage.bulkUpsertFormResponses(processedResponses);
      
      // If the form is being marked as completed, update its status
      if (req.body.markComplete) {
        const formUpdate: any = {
          status: 'completed' as const,
          completedById: req.user.id,
          completedAt: new Date()
        };
        
        await storage.updateForm(formId, formUpdate);
        
        // Log activity
        await storage.createActivity({
          userId: req.user.id,
          assessmentId: form.assessmentId,
          action: 'complete_form',
          details: `Completed ${form.formType} form`
        });
        
        // Check if all forms are completed to update assessment status
        const allForms = await storage.getFormsByAssessment(form.assessmentId);
        const allCompleted = allForms.every(f => f.status === 'completed');
        
        if (allCompleted && assessment.status === 'VerificationPending') {
          await storage.updateAssessment(assessment.id, { status: 'pre_assessment' });
          
          // Log status change
          await storage.createActivity({
            userId: req.user.id,
            assessmentId: form.assessmentId,
            action: 'update_status',
            details: `Assessment moved to Pre-Assessment phase`
          });
        }
      } else {
        // Just update the form as in progress
        await storage.updateForm(formId, { status: 'in_progress' as const });
        
        // Log activity
        await storage.createActivity({
          userId: req.user.id,
          assessmentId: form.assessmentId,
          action: 'update_form',
          details: `Updated ${form.formType} form`
        });
      }
      
      res.status(201).json(savedResponses);
    } catch (error) {
      res.status(500).json({ message: "Failed to save form responses" });
    }
  });
  
  // Form Response Reports
  app.get("/api/reports/form-responses", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    // Only admin and assessor can access reports
    if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
      return res.status(403).json({ message: "Not authorized to view reports" });
    }
    
    try {
      const { questionId, formType, startDate, endDate } = req.query;
      
      // Execute report query based on parameters
      // This is a simplified example - a real implementation would be more complex
      if (questionId) {
        // Get all responses for a specific question
        const question = await storage.getFormQuestion(parseInt(questionId as string));
        
        if (!question) {
          return res.status(404).json({ message: "Question not found" });
        }
        
        // Here we would implement a custom SQL query to get all responses to this question
        // along with form and assessment data
        // This is just a placeholder
        const report = {
          question: question,
          responseCount: 0,
          // Other aggregated data would go here
        };
        
        res.json(report);
      } else if (formType) {
        // Report on a specific form type
        const questions = await storage.getFormQuestionsByType(formType as string);
        
        // Here we would implement custom SQL queries to get aggregated responses
        // for this form type
        const report = {
          formType: formType,
          questionCount: questions.length,
          sectionBreakdown: {},
          // Other aggregated data would go here
        };
        
        res.json(report);
      } else {
        // General report
        res.json({
          message: "Please specify report parameters"
        });
      }
    } catch (error) {
      res.status(500).json({ message: "Failed to generate report" });
    }
  });
  
  // Documents
  app.post("/api/documents", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      const data = insertDocumentSchema.parse(req.body);
      
      // Check permissions
      const assessment = await storage.getAssessment(data.assessmentId);
      if (!assessment) {
        return res.status(404).json({ message: "Assessment not found" });
      }
      
      // Only admin and assessor can upload documents
      if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
        return res.status(403).json({ message: "Not authorized to upload documents" });
      }
      
      const document = await storage.createDocument({
        ...data,
        uploadedById: req.user.id
      });
      
      // Log activity
      await storage.createActivity({
        userId: req.user.id,
        assessmentId: data.assessmentId,
        action: 'upload_document',
        details: `Uploaded document: ${data.name}`
      });
      
      res.status(201).json(document);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create document" });
    }
  });
  
  // Activities
  app.get("/api/activities", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      // Only admin and assessor can view all recent activities
      if (req.user.role !== 'admin' && req.user.role !== 'assessor') {
        return res.status(403).json({ message: "Not authorized to view all activities" });
      }
      
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      const activities = await storage.listRecentActivities(limit);
      
      // Expand user info for each activity
      const expandedActivities = await Promise.all(activities.map(async (activity) => {
        let user = null;
        if (activity.userId) {
          const userRecord = await storage.getUser(activity.userId);
          if (userRecord) {
            const { password, ...userWithoutPassword } = userRecord;
            user = userWithoutPassword;
          }
        }
        
        let assessment = null;
        if (activity.assessmentId) {
          assessment = await storage.getAssessment(activity.assessmentId);
          if (assessment) {
            const assessee = await storage.getAssessee(assessment.assesseeId);
            assessment = { ...assessment, assessee };
          }
        }
        
        return { ...activity, user, assessment };
      }));
      
      res.json(expandedActivities);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch activities" });
    }
  });
  
  // No change to the implementation, just moving the route
  
  // Recent Activities - Used by dashboard
  app.get("/api/activities/recent", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      // Get recent activities based on user role
      let activities = [];
      
      if (req.user.role === 'admin' || req.user.role === 'assessor') {
        activities = await storage.listRecentActivities(5);
      } else {
        // For other roles, get activities related to their assessments
        const assessments = await storage.listAssessmentsByUser(req.user.id, req.user.role);
        
        const allActivities = [];
        for (const assessment of assessments) {
          const assessmentActivities = await storage.listActivitiesByAssessment(assessment.id);
          allActivities.push(...assessmentActivities);
        }
        
        // Sort and limit
        activities = allActivities
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 5);
      }
      
      // Expand user info for each activity
      const expandedActivities = await Promise.all(activities.map(async (activity) => {
        let user = null;
        if (activity.userId) {
          const userRecord = await storage.getUser(activity.userId);
          if (userRecord) {
            const { password, ...userWithoutPassword } = userRecord;
            user = userWithoutPassword;
          }
        }
        
        let assessment = null;
        if (activity.assessmentId) {
          assessment = await storage.getAssessment(activity.assessmentId);
          if (assessment) {
            const assessee = await storage.getAssessee(assessment.assesseeId);
            assessment = { ...assessment, assessee };
          }
        }
        
        return { ...activity, user, assessment };
      }));
      
      res.json(expandedActivities);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch recent activities" });
    }
  });
  
  // Dashboard summary
  app.get("/api/dashboard", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);
    
    try {
      // Get assessments relevant to user role
      let assessments = [];
      
      if (req.user.role === 'admin' || req.user.role === 'assessor') {
        assessments = await storage.listAssessments();
      } else {
        assessments = await storage.listAssessmentsByUser(req.user.id, req.user.role);
      }
      
      // Calculate stats
      const stats = {
        total: assessments.length,
        VerificationPending: assessments.filter(a => a.status === 'VerificationPending').length,
        preAssessment: assessments.filter(a => a.status === 'pre_assessment').length,
        scheduled: assessments.filter(a => a.status === 'scheduled').length,
        reportWriting: assessments.filter(a => a.status === 'report_writing').length,
        completed: assessments.filter(a => a.status === 'completed').length,
        awaitingPayment: assessments.filter(a => a.paymentStatus !== 'fully_paid').length
      };
      
      // Get recent activities
      let activities = [];
      
      if (req.user.role === 'admin' || req.user.role === 'assessor') {
        activities = await storage.listRecentActivities(5);
      } else {
        // For other roles, get activities related to their assessments
        const allActivities = [];
        for (const assessment of assessments) {
          const assessmentActivities = await storage.listActivitiesByAssessment(assessment.id);
          allActivities.push(...assessmentActivities);
        }
        
        // Sort and limit
        activities = allActivities
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 5);
      }
      
      // Expand user info for each activity
      const expandedActivities = await Promise.all(activities.map(async (activity) => {
        let user = null;
        if (activity.userId) {
          const userRecord = await storage.getUser(activity.userId);
          if (userRecord) {
            const { password, ...userWithoutPassword } = userRecord;
            user = userWithoutPassword;
          }
        }
        
        let assessment = null;
        if (activity.assessmentId) {
          assessment = await storage.getAssessment(activity.assessmentId);
          if (assessment) {
            const assessee = await storage.getAssessee(assessment.assesseeId);
            assessment = { ...assessment, assessee };
          }
        }
        
        return { ...activity, user, assessment };
      }));
      
      res.json({
        stats,
        activities: expandedActivities,
        assessmentsByStatus: {
          VerificationPending: assessments.filter(a => a.status === 'VerificationPending').slice(0, 5),
          preAssessment: assessments.filter(a => a.status === 'pre_assessment').slice(0, 5),
          scheduled: assessments.filter(a => a.status === 'scheduled').slice(0, 5),
          reportWriting: [
            ...assessments.filter(a => a.status === 'report_writing'),
            ...assessments.filter(a => a.status === 'qa_review')
          ].slice(0, 5)
        }
      });
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch dashboard data" });
    }
  });

  // Create HTTP server
  // Public APIs (no authentication required)
  
  // Mount the debug email router
  app.use('/api/debug', debugEmailRouter);
  
  // Public university referral endpoint
  app.post("/api/public/university-referral", async (req, res) => {
    try {
      const { handlePublicUniversityReferral } = await import("./handlers/public-university-referral");
      await handlePublicUniversityReferral(req, res);
    } catch (error) {
      logger.error("Error handling university referral:", error);
      res.status(500).json({ message: "Internal server error processing referral" });
    }
  });
  
  // Public individual referral endpoint
  app.post("/api/public/individual-referral", async (req, res) => {
    try {
      const { handlePublicIndividualReferral } = await import("./handlers/public-individual-referral");
      await handlePublicIndividualReferral(req, res);
    } catch (error) {
      logger.error("Error handling individual referral:", error);
      res.status(500).json({ message: "Internal server error processing referral" });
    }
  });
  // Let Vite middleware handle client-side routing and module loading

  // Communication Center API Routes
  
  // Conversations
  app.get("/api/conversations", getConversations);
  app.get("/api/conversations/:id", getConversation);
  app.post("/api/conversations", createConversation);
  app.post("/api/conversations/:id/participants", addParticipant);
  app.delete("/api/conversations/:conversationId/participants/:userId", removeParticipant);
  
  // Messages
  app.post("/api/messages", sendMessage);
  app.put("/api/messages/:id", editMessage);
  
  // Notifications
  app.get("/api/notifications", getNotifications);
  app.get("/api/notifications/count", getUnreadNotificationCount);
  app.put("/api/notifications/:id/read", markNotificationAsRead);
  app.put("/api/notifications/read-all", markAllNotificationsAsRead);
  
  // Notification Preferences
  app.get("/api/notification-preferences", getNotificationPreferences);
  app.put("/api/notification-preferences", updateNotificationPreferences);
  
  // Test password reset email sending (debug endpoint)
  app.post("/api/debug/password-reset-email", async (req, res) => {
    try {
      const { email } = req.body;
      
      if (!email) {
        return res.status(400).json({ success: false, message: "Email address is required" });
      }
      
      logger.info(`Attempting to send password reset debug email to: ${email}`);
      
      // Check if user exists
      const user = await storage.getUserByEmail(email);
      if (!user) {
        return res.status(404).json({ 
          success: false, 
          message: "User not found with this email address. For testing purposes, we're returning an error.",
          details: { email }
        });
      }
      
      // Import password reset utilities and email templates
      const { generatePasswordResetToken } = await import('./utils/password-reset');
      const { createPasswordResetEmail } = await import('./utils/email-templates');
      const { sendEmail } = await import('./utils/sendgrid');
      
      // Generate token
      logger.info(`Generating password reset token for user ${user.id}`);
      const token = await generatePasswordResetToken(user.id);
      if (!token) {
        return res.status(500).json({ 
          success: false, 
          message: "Failed to generate password reset token",
          details: { userId: user.id }
        });
      }
      
      // Generate reset URL
      const baseUrl = getBaseUrlFromRequest(req);
      const resetLink = `${baseUrl}/reset-password/${token}`;
      logger.info(`Generated password reset link: ${resetLink}`);
      
      // Create email content
      const emailOptions = createPasswordResetEmail(user.email, user.fullName, resetLink);
      logger.info('Email options generated:', {
        to: emailOptions.to,
        from: emailOptions.from,
        subject: emailOptions.subject,
        hasText: !!emailOptions.text,
        hasHtml: !!emailOptions.html,
        textLength: emailOptions.text?.length || 0,
        htmlLength: emailOptions.html?.length || 0
      });
      
      // Send email
      logger.info(`Attempting to send password reset email to ${user.email}`);
      const emailSent = await sendEmail(emailOptions);
      
      logger.info(`Password reset DEBUG email sent to ${user.email}: ${emailSent ? 'Success' : 'Failed'}`);
      
      res.json({
        success: emailSent,
        message: emailSent ? 'Password reset email sent successfully' : 'Failed to send password reset email',
        details: {
          userId: user.id,
          email: user.email,
          fullName: user.fullName,
          tokenGenerated: !!token,
          emailGenerated: true,
          resetLink
        }
      });
    } catch (error: any) {
      logger.error('Error in debug password reset endpoint:', error);
      
      // Extract detailed SendGrid error if available
      let detailedError = null;
      if (error.response && error.response.body) {
        detailedError = JSON.stringify(error.response.body);
      }
      
      res.status(500).json({
        success: false,
        message: 'Error sending password reset email',
        error: error.message || 'Unknown error',
        detailedError: detailedError
      });
    }
  });
  
  // Debug email sending with comprehensive error reporting
  app.post("/api/debug/send-email", async (req, res) => {
    try {
      const { email } = req.body;
      
      if (!email) {
        return res.status(400).json({ success: false, message: "Email address is required" });
      }
      
      logger.info(`Attempting to send debug email to: ${email}`);
      logger.info('SendGrid API Key status:', process.env.SENDGRID_API_KEY ? 'Set (first 4 chars: ' + process.env.SENDGRID_API_KEY.substring(0, 4) + '...)' : 'Not set');
      logger.info('SendGrid FROM email:', process.env.SENDGRID_FROM_EMAIL);
      logger.info('SendGrid FROM name:', process.env.SENDGRID_FROM_NAME);
      
      // Import email utils
      const { sendEmail } = await import('./utils/sendgrid');
      
      const result = await sendEmail({
        to: email,
        subject: 'A2lexa Debug Test - ' + new Date().toISOString(),
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee;">
            <h1 style="color: #4a6cf7;">A2lexa Email Debug Test</h1>
            <p>This is a debug test email sent at: ${new Date().toISOString()}</p>
            <p>If you're receiving this email, it means the SendGrid email delivery is working correctly.</p>
            <p>Server information:</p>
            <ul>
              <li>Environment: ${process.env.NODE_ENV || 'development'}</li>
              <li>Host: ${req.get('host')}</li>
              <li>Protocol: ${req.protocol}</li>
            </ul>
          </div>
        `
      });
      
      res.json({
        success: result,
        message: result ? 'Debug email sent successfully' : 'Failed to send debug email',
        timestamp: new Date().toISOString(),
        details: {
          to: email,
          from: process.env.SENDGRID_FROM_EMAIL,
          fromName: process.env.SENDGRID_FROM_NAME,
          apiKeySet: !!process.env.SENDGRID_API_KEY
        }
      });
    } catch (error: any) {
      logger.error('Error in debug email endpoint:', error);
      
      // Extract detailed SendGrid error if available
      let detailedError = null;
      if (error.response && error.response.body) {
        detailedError = JSON.stringify(error.response.body);
      }
      
      res.status(500).json({
        success: false,
        message: 'Error sending debug email',
        error: error.message || 'Unknown error',
        detailedError: detailedError
      });
    }
  });

  // Mount the debug email router
  app.use('/api/debug', debugEmailRouter);
  
  // Admin routes
  app.get('/api/admin/pending-verifications', requireAdmin, getPendingVerifications);
  app.get('/api/admin/users', requireAdmin, getAllUsers);
  app.get('/api/admin/activity-logs', requireAdmin, getActivityLogs);
  
  // Test notification endpoint - development only
  app.post('/api/test-notification', sendTestNotification);
  
  // Test form submission endpoint - development only
  app.post('/api/test-form-submission', handleTestFormSubmission);

  const httpServer = createServer(app);

  return httpServer;
}
