import { logger } from './logger';
import { 
  users, assessees, schools, assessments, forms, documents, activities,
  referrals, adminActivityLogs, conversations, conversationParticipants,
  messages, messageRecipients, notifications, notificationPreferences,
  passwordResetTokens, formQuestions, formResponses, universities,
  publicTrackingRecords, issues, issueComments, assessmentNotes,
  type User, type InsertUser, type Assessee, type InsertAssessee,
  type School, type InsertSchool, type Assessment, type InsertAssessment,
  type Form, type InsertForm, type Document, type InsertDocument,
  type Activity, type InsertActivity, type Referral, type InsertReferral,
  type Conversation, type InsertConversation, type ConversationParticipant,
  type InsertConversationParticipant, type Message, type InsertMessage,
  type MessageRecipient, type InsertMessageRecipient, type Notification,
  type InsertNotification, type NotificationPreference, type InsertNotificationPreference,
  type FormQuestion, type InsertFormQuestion, type FormResponse, type InsertFormResponse,
  type University, type InsertUniversity, type PublicTrackingRecord,
  type InsertPublicTrackingRecord, type Issue, type InsertIssue, type IssueComment,
  type InsertIssueComment, type AssessmentNote, type InsertAssessmentNote
} from '@shared/schema';
import { db, pool } from "./db";
import { eq, and, desc, SQL, inArray, sql, isNull, gt } from "drizzle-orm";
import connectPg from "connect-pg-simple";
import session from "express-session";
import { IStorage } from "./storage";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";

const scryptAsync = promisify(scrypt);

// Password hashing function (extracted from auth.ts for reuse)
async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

const PostgresSessionStore = connectPg(session);

export class DatabaseStorage implements IStorage {
  sessionStore: any;

  constructor() {
    this.sessionStore = new PostgresSessionStore({ 
      pool,
      createTableIfMissing: true 
    });
  }

  // Users
  async getUser(id: number): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.id, id));
      if (user && !('status' in user)) {
        (user as any).status = 'active';
      }
      return user;
    } catch (error) {
      logger.error('Error in getUser:', error);

      try {
        // Use a parameterized query to avoid SQL injection
        const query =
          'SELECT id, email, username, password, full_name as "fullName", role, phone, organization, created_at as "createdAt" FROM users WHERE id = $1';
        const { rows } = await pool.query(query, [id]);

        if (rows.length > 0) {
          const user = rows[0];
          (user as any).status = 'active'; // Default status
          return user as User;
        }
      } catch (secondError) {
        logger.error('Second error in getUser:', secondError);
      }

      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.username, username));
      // Add status field if it doesn't exist in the database yet
      if (user && !('status' in user)) {
        (user as any).status = 'active';
      }
      return user;
    } catch (error) {
      logger.error('Error in getUserByUsername:', error);

      try {
        // Use parameterized query instead of manual escaping
        const query =
          'SELECT id, email, username, password, full_name as "fullName", role, phone, organization, created_at as "createdAt" FROM users WHERE username = $1';
        const { rows } = await pool.query(query, [username]);

        if (rows.length > 0) {
          const user = rows[0];
          (user as any).status = 'active'; // Default status
          return user as User;
        }
      } catch (secondError) {
        logger.error('Second error in getUserByUsername:', secondError);
      }

      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.email, email));
      if (user && !('status' in user)) {
        (user as any).status = 'active';
      }
      return user;
    } catch (error) {
      logger.error('Error in getUserByEmail:', error);

      try {
        // Use parameterized query instead of manual escaping
        const query =
          'SELECT id, email, username, password, full_name as "fullName", role, phone, organization, created_at as "createdAt" FROM users WHERE email = $1';
        const { rows } = await pool.query(query, [email]);

        if (rows.length > 0) {
          const user = rows[0];
          (user as any).status = 'active'; // Default status
          return user as User;
        }
      } catch (secondError) {
        logger.error('Second error in getUserByEmail:', secondError);
      }

      return undefined;
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      // Preserve the status field if it exists
      const userData = {
        ...insertUser,
        createdAt: new Date()
      };

      // Log the user creation with status
      logger.info(`Creating user with status: ${userData.status || 'active'}`);

      const [user] = await db.insert(users).values(userData).returning();

      // Make sure status is returned
      if (!user.status) {
        (user as any).status = userData.status || 'active';
      }

      logger.info(`User created with ID: ${user.id}, Status: ${user.status || userData.status || 'active'}`);
      return user;
    } catch (error) {
      logger.error('Error in createUser:', error);
      if ((error as any).message?.includes('column "status" does not exist')) {
        // Try without the status field
        const { status, ...userWithoutStatus } = insertUser as any;
        const query = `
          INSERT INTO users (
            email, username, password, full_name, role, phone, organization
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7
          ) RETURNING id, email, username, password, full_name as "fullName", role, phone, organization, created_at as "createdAt"
        `;

        const values = [
          userWithoutStatus.email,
          userWithoutStatus.username,
          userWithoutStatus.password,
          userWithoutStatus.fullName,
          userWithoutStatus.role,
          userWithoutStatus.phone || null,
          userWithoutStatus.organization || null
        ];

        const result = await db.execute(query, values);
        if (result.rows.length > 0) {
          const user = result.rows[0];
          (user as any).status = status || 'active';
          return user as User;
        }
      }
      throw error;
    }
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const [updatedUser] = await db
      .update(users)
      .set(userData)
      .where(eq(users.id, id))
      .returning();
    return updatedUser;
  }

  async listUsers(role?: string): Promise<User[]> {
    try {
      let query = db.select().from(users);

      // If role is specified, add WHERE clause
      if (role) {
        query = query.where(eq(users.role, role as any));
      }

      // Execute the query with a specific selection that includes status
      const usersList = await query;

      // Make sure status is properly handled
      return usersList.map(user => {
        // Only set default status if the field is missing or null
        if (!user.status) {
          (user as any).status = 'active';
        }

        // Ensure we don't modify existing status values
        logger.info(`User ${user.id} (${user.email}) has status: ${user.status || 'active'}`);
        return user;
      });
    } catch (error) {
      logger.error('Error in listUsers:', error);

      try {
        // Use parameterized query to avoid SQL injection
        const query = role
          ? `SELECT id, email, username, password, full_name as "fullName", role, phone, organization, status, created_at as "createdAt" FROM users WHERE role = $1`
          : `SELECT id, email, username, password, full_name as "fullName", role, phone, organization, status, created_at as "createdAt" FROM users`;

        const { rows } = role
          ? await pool.query(query, [role])
          : await pool.query(query);

        // Add default status only if missing or null
        return rows.map(user => {
          if (!user.status) {
            (user as any).status = 'active';
          }
          logger.info(`Fallback path: User ${user.id} (${user.email}) has status: ${user.status || 'active'}`);
          return user as User;
        });
      } catch (secondError) {
        logger.error('Second error in listUsers:', secondError);
        return [];
      }
    }
  }

  // Assessees
  async getAssessee(id: number): Promise<Assessee | undefined> {
    try {
      const [assessee] = await db.select().from(assessees).where(eq(assessees.id, id));

      if (assessee) {
      // Protection against contact data leakage - enhanced validation
      const needsDataCleanup = (
        // Case 1: Address equals fullName (primary issue)
        assessee.address === assessee.fullName || 
        // Case 2: Email equals fullName (potential issue)
        assessee.email === assessee.fullName ||
        // Case 3: Phone equals fullName (potential issue) 
        assessee.phone === assessee.fullName ||
        // Case 4: Address is invalid (too short, not a real address)
        (assessee.address && assessee.address.length < 10) ||
        // Case 5: Email is invalid (no @ character)
        (assessee.email && !assessee.email.includes('@'))
      );

      if (needsDataCleanup) {
        logger.info(`Fixing contact fields for assessee ${id} due to data validation issues`);

        // Fix in-memory object values
        if (assessee.address === assessee.fullName || (assessee.address && assessee.address.length < 10)) {
          assessee.address = null;
        }

        if (assessee.email === assessee.fullName || (assessee.email && !assessee.email.includes('@'))) {
          assessee.email = null;
        }

        if (assessee.phone === assessee.fullName) {
          assessee.phone = null;
        }

        // Also update the database record to fix it permanently
        try {
          await this.updateAssessee(id, { 
            address: assessee.address,
            email: assessee.email,
            phone: assessee.phone
          });
          logger.info(`Successfully fixed contact field values in database for assessee ${id}`);
        } catch (err) {
          logger.error(`Failed to update assessee ${id} data in database:`, err);
        }
      }

      return assessee;
      } else {
        return undefined;
      }
    } catch (error) {
      logger.error(`Error in getAssessee for id ${id}:`, error);
      throw error;
    }
  }

  async createAssessee(insertAssessee: InsertAssessee): Promise<Assessee> {
    // CRITICAL FIX: Create a clean copy of the data to prevent cross-field contamination
    // This prevents issues like fullName being copied to address
    const cleanedData = {
      fullName: insertAssessee.fullName,
      preferredName: insertAssessee.preferredName || null,
      dateOfBirth: insertAssessee.dateOfBirth,
      userId: insertAssessee.userId || null,

      // Explicitly set contact fields as null by default
      // This ensures they're only populated when explicitly provided
      address: null,
      email: null,
      phone: null,
      phoneWork: null,

      // Optional fields 
      courseProgram: insertAssessee.courseProgram || null,
      yearOfStudy: insertAssessee.yearOfStudy || null,
      schoolId: insertAssessee.schoolId || null,
      parentId: insertAssessee.parentId || null,
      hearAbout: insertAssessee.hearAbout || null,
      hearAboutOther: insertAssessee.hearAboutOther || null
    };

    // Only set contact fields if they contain valid data AND are different from fullName
    if (insertAssessee.address && 
        insertAssessee.address.length > 5 && 
        insertAssessee.address !== insertAssessee.fullName) {
      cleanedData.address = insertAssessee.address;
      logger.info(`Setting valid address for assessee ${cleanedData.fullName}`);
    } else {
      logger.info(`Skipping invalid address for assessee ${cleanedData.fullName}`);
    }

    // Validate email - must contain @ symbol to be valid
    if (insertAssessee.email && 
        insertAssessee.email.includes('@') && 
        insertAssessee.email !== insertAssessee.fullName) {
      cleanedData.email = insertAssessee.email;
      logger.info(`Setting valid email for assessee ${cleanedData.fullName}: ${cleanedData.email}`);
    } else if (insertAssessee.email) {
      logger.info(`Skipping invalid email for assessee ${cleanedData.fullName}: ${insertAssessee.email}`);
    }

    // Validate phone - must not be fullName and have reasonable length
    if (insertAssessee.phone &&
        insertAssessee.phone !== insertAssessee.fullName &&
        insertAssessee.phone.length >= 5) {
      cleanedData.phone = insertAssessee.phone;
      logger.info(`Setting valid phone for assessee ${cleanedData.fullName}: ${cleanedData.phone}`);
    } else if (insertAssessee.phone) {
      logger.info(`Skipping invalid phone for assessee ${cleanedData.fullName}: ${insertAssessee.phone}`);
    }

    if (insertAssessee.phoneWork &&
        insertAssessee.phoneWork !== insertAssessee.fullName &&
        insertAssessee.phoneWork.length >= 5) {
      cleanedData.phoneWork = insertAssessee.phoneWork;
    }

    logger.info(`Creating assessee record for ${cleanedData.fullName} with strict data validation`);
    const [assessee] = await db.insert(assessees).values(cleanedData).returning();
    return assessee;
  }

  async updateAssessee(id: number, assesseeData: Partial<Assessee>): Promise<Assessee | undefined> {
    // First get current assessee data
    const [currentAssessee] = await db.select().from(assessees).where(eq(assessees.id, id));
    if (!currentAssessee) {
      logger.error(`Cannot update assessee ${id}: not found`);
      return undefined;
    }

    // Create clean update object with validated data
    const cleanedData: Partial<Assessee> = {};

    // Copy non-contact fields without validation
    if (assesseeData.fullName !== undefined) cleanedData.fullName = assesseeData.fullName;
    if (assesseeData.preferredName !== undefined) cleanedData.preferredName = assesseeData.preferredName;
    if (assesseeData.dateOfBirth !== undefined) cleanedData.dateOfBirth = assesseeData.dateOfBirth;
    if (assesseeData.schoolId !== undefined) cleanedData.schoolId = assesseeData.schoolId;
    if (assesseeData.parentId !== undefined) cleanedData.parentId = assesseeData.parentId;
    if (assesseeData.userId !== undefined) cleanedData.userId = assesseeData.userId;
    if (assesseeData.courseProgram !== undefined) cleanedData.courseProgram = assesseeData.courseProgram;
    if (assesseeData.yearOfStudy !== undefined) cleanedData.yearOfStudy = assesseeData.yearOfStudy;

    // Apply same validation as in createAssessee for contact fields
    if (assesseeData.address !== undefined) {
      // If address is fullName or too short, set to null
      if (!assesseeData.address || 
          assesseeData.address === '' || 
          assesseeData.address === currentAssessee.fullName ||
          (assesseeData.fullName && assesseeData.address === assesseeData.fullName) ||
          assesseeData.address.length < 5) {
        cleanedData.address = null;
        logger.info(`Cleared invalid address for assessee ${id}`);
      } else {
        cleanedData.address = assesseeData.address;
        logger.info(`Updated address for assessee ${id}`);
      }
    }

    // Email must contain @ and not be fullName
    if (assesseeData.email !== undefined) {
      if (!assesseeData.email || 
          assesseeData.email === '' || 
          assesseeData.email === currentAssessee.fullName ||
          (assesseeData.fullName && assesseeData.email === assesseeData.fullName) ||
          !assesseeData.email.includes('@')) {
        cleanedData.email = null;
        logger.info(`Cleared invalid email for assessee ${id}`);
      } else {
        cleanedData.email = assesseeData.email;
        logger.info(`Updated email for assessee ${id}`);
      }
    }

    // Phone must not be fullName and have reasonable length
    if (assesseeData.phone !== undefined) {
      if (!assesseeData.phone ||
          assesseeData.phone === '' ||
          assesseeData.phone === currentAssessee.fullName ||
          (assesseeData.fullName && assesseeData.phone === assesseeData.fullName) ||
          assesseeData.phone.length < 5) {
        cleanedData.phone = null;
        logger.info(`Cleared invalid phone for assessee ${id}`);
      } else {
        cleanedData.phone = assesseeData.phone;
        logger.info(`Updated phone for assessee ${id}`);
      }
    }

    if (assesseeData.phoneWork !== undefined) {
      if (!assesseeData.phoneWork || assesseeData.phoneWork === '' || assesseeData.phoneWork === currentAssessee.fullName ||
          (assesseeData.fullName && assesseeData.phoneWork === assesseeData.fullName) || assesseeData.phoneWork.length < 5) {
        cleanedData.phoneWork = null;
      } else {
        cleanedData.phoneWork = assesseeData.phoneWork;
      }
    }

    if (assesseeData.hearAbout !== undefined) cleanedData.hearAbout = assesseeData.hearAbout;
    if (assesseeData.hearAboutOther !== undefined) cleanedData.hearAboutOther = assesseeData.hearAboutOther;

    // Only proceed with update if we have changes to make
    if (Object.keys(cleanedData).length === 0) {
      logger.info(`No valid changes to make for assessee ${id}`);
      return currentAssessee;
    }

    logger.info(`Updating assessee ${id} with validated data`);
    const [updatedAssessee] = await db
      .update(assessees)
      .set(cleanedData)
      .where(eq(assessees.id, id))
      .returning();
    return updatedAssessee;
  }

  async listAssesseesByParent(parentId: number): Promise<Assessee[]> {
    return db.select().from(assessees).where(eq(assessees.parentId, parentId));
  }

  async listAssesseesBySchool(schoolId: number): Promise<Assessee[]> {
    logger.info(`Fetching assessees for school ID: ${schoolId}`);
    const result = await db.select().from(assessees).where(eq(assessees.schoolId, schoolId));
    logger.info(`Found ${result.length} assessees for school ID: ${schoolId}`);
    return result;
  }

  /**
   * Get all assessees in the system (for admin/assessor)
   */
  async listAllAssessees(): Promise<Assessee[]> {
    logger.info('Fetching all assessees in the system');
    // Use fullName instead of lastName for ordering
    const result = await db.select().from(assessees).orderBy(assessees.fullName);
    logger.info(`Found ${result.length} total assessees`);
    return result;
  }

  // Schools
  async getSchool(id: number): Promise<School | undefined> {
    const [school] = await db.select().from(schools).where(eq(schools.id, id));
    return school;
  }

  async createSchool(insertSchool: InsertSchool): Promise<School> {
    const [school] = await db.insert(schools).values(insertSchool).returning();
    return school;
  }

  async updateSchool(id: number, schoolData: Partial<School>): Promise<School | undefined> {
    const [updatedSchool] = await db
      .update(schools)
      .set(schoolData)
      .where(eq(schools.id, id))
      .returning();
    return updatedSchool;
  }

  async listSchools(): Promise<School[]> {
    return db.select().from(schools);
  }

  // Assessments
  async getAssessment(id: number): Promise<Assessment | undefined> {
    const [assessment] = await db.select().from(assessments).where(eq(assessments.id, id));
    return assessment;
  }

  async createAssessment(insertAssessment: InsertAssessment): Promise<Assessment> {
    const [assessment] = await db.insert(assessments).values({
      ...insertAssessment,
      status: insertAssessment.status || 'VerificationPending',
      updatedAt: new Date()
    }).returning();
    return assessment;
  }

  async updateAssessment(id: number, assessmentData: Partial<Assessment>): Promise<Assessment | undefined> {
    const [updatedAssessment] = await db
      .update(assessments)
      .set({
        ...assessmentData,
        updatedAt: new Date()
      })
      .where(eq(assessments.id, id))
      .returning();
    return updatedAssessment;
  }

  async listAssessments(filters?: Partial<Assessment>): Promise<Assessment[]> {
    if (!filters) {
      return db.select().from(assessments).orderBy(desc(assessments.createdAt));
    }

    const conditions: SQL[] = [];

    // Process each filter
    for (const [key, value] of Object.entries(filters)) {
      if (value !== undefined && value !== null) {
        if (key === 'assesseeId') {
          conditions.push(eq(assessments.assesseeId, value as number));
        } else if (key === 'assessorId') {
          conditions.push(eq(assessments.assessorId, value as number));
        } else if (key === 'status') {
          conditions.push(eq(assessments.status, value as any));
        } else if (key === 'paymentStatus') {
          conditions.push(eq(assessments.paymentStatus, value as any));
        } else if (key === 'referralType') {
          conditions.push(eq(assessments.referralType, value as any));
        } else if (key === 'referringUserId') {
          conditions.push(eq(assessments.referringUserId, value as any));
        }
      }
    }

    if (conditions.length === 0) {
      return db.select().from(assessments).orderBy(desc(assessments.createdAt));
    }

    return db.select()
      .from(assessments)
      .where(and(...conditions))
      .orderBy(desc(assessments.createdAt));
  }

  async listAssessmentsByUser(userId: number, role: string): Promise<Assessment[]> {
    switch (role) {
      case 'assessor':
        return db.select()
          .from(assessments)
          .where(eq(assessments.assessorId, userId))
          .orderBy(desc(assessments.createdAt));

      case 'parent':
        const parentAssessees = await this.listAssesseesByParent(userId);
        if (parentAssessees.length === 0) return [];

        const parentAssesseeIds = parentAssessees.map(a => a.id);

        if (parentAssesseeIds.length === 1) {
          return db.select()
            .from(assessments)
            .where(eq(assessments.assesseeId, parentAssesseeIds[0]))
            .orderBy(desc(assessments.createdAt));
        } else {
          return db.select()
            .from(assessments)
            .where(inArray(assessments.assesseeId, parentAssesseeIds))
            .orderBy(desc(assessments.createdAt));
        }

      case 'school':
        logger.info(`Processing school role for user ID: ${userId}`);
        const [school] = await db.select()
          .from(schools)
          .where(eq(schools.userId, userId));

        if (!school) {
          logger.info(`No school found for user ID: ${userId}`);
          return [];
        }

        logger.info(`Found school: ${school.name} (ID: ${school.id})`);

        const schoolAssessees = await this.listAssesseesBySchool(school.id);
        if (schoolAssessees.length === 0) {
          logger.info(`No assessees found for school ID: ${school.id}`);
          return [];
        }

        logger.info(`Found ${schoolAssessees.length} assessees for school ID: ${school.id}`);
        const schoolAssesseeIds = schoolAssessees.map(a => a.id);
        logger.info(`Assessee IDs: ${schoolAssesseeIds.join(', ')}`);

        let results = [];
        if (schoolAssesseeIds.length === 1) {
          logger.info(`Querying for single assessee ID: ${schoolAssesseeIds[0]}`);
          results = await db.select()
            .from(assessments)
            .where(eq(assessments.assesseeId, schoolAssesseeIds[0]))
            .orderBy(desc(assessments.createdAt));
        } else {
          logger.info(`Querying for multiple assessee IDs: ${schoolAssesseeIds.join(', ')}`);
          results = await db.select()
            .from(assessments)
            .where(inArray(assessments.assesseeId, schoolAssesseeIds))
            .orderBy(desc(assessments.createdAt));
        }

        logger.info(`Found ${results.length} assessments for school's assessees`);
        return results;

      case 'university':
        logger.info(`Fetching assessments for university user id: ${userId}`);
        const universityAssessments = await db.select()
          .from(assessments)
          .where(eq(assessments.referringUserId, userId))
          .orderBy(desc(assessments.createdAt));

        logger.info(`University assessments found: ${universityAssessments.length}`);
        logger.info('Assessment data:', universityAssessments);

        return universityAssessments;

      case 'assessee':
        const [assessee] = await db.select()
          .from(assessees)
          .where(eq(assessees.userId, userId));

        if (!assessee) return [];

        return db.select()
          .from(assessments)
          .where(eq(assessments.assesseeId, assessee.id))
          .orderBy(desc(assessments.createdAt));

      default:
        return [];
    }
  }

  // Forms
  async getForm(id: number): Promise<Form | undefined> {
    const [form] = await db.select().from(forms).where(eq(forms.id, id));
    return form;
  }

  async getFormsByAssessment(assessmentId: number): Promise<Form[]> {
    try {
      // Check if we need to handle access_token column error - use parameterized query
      const columnCheckResult = await db.execute(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'forms' AND column_name = 'access_token'
      `);

      // If access_token column exists, proceed normally
      if (columnCheckResult.rows && columnCheckResult.rows.length > 0) {
        // Full select including access_token
        const result = await db.select()
          .from(forms)
          .where(eq(forms.assessmentId, assessmentId));
        return result;
      } else {
        // Do a more careful select without access_token
        logger.info('Forms table missing access_token column - using fallback query');
        try {
          // First check if the access_token column exists
          const columnCheckResult = await pool.query(
            `SELECT column_name FROM information_schema.columns WHERE table_name = 'forms' AND column_name = 'access_token'`
          );

          const hasAccessTokenColumn = columnCheckResult.rows.length > 0;

          // Construct query based on available columns
          let sql =
            'SELECT id, assessment_id, form_type, status, completed_at, completed_by_id, data, created_at, updated_at';

          // Add access token columns if they exist
          if (hasAccessTokenColumn) {
            sql += ', access_token, access_token_expires_at';
          }

          sql += ' FROM forms WHERE assessment_id = $1';

          const { rows } = await pool.query(sql, [assessmentId]);

          return rows.map(row => ({
            id: row.id,
            assessmentId: row.assessment_id,
            formType: row.form_type,
            status: row.status,
            completedAt: row.completed_at ? new Date(row.completed_at) : null,
            completedById: row.completed_by_id,
            data: row.data,
            // Use token fields if available, otherwise null
            accessToken: hasAccessTokenColumn ? row.access_token : null,
            accessTokenExpiresAt: hasAccessTokenColumn && row.access_token_expires_at ? new Date(row.access_token_expires_at) : null,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
          }));
        } catch (sqlError) {
          logger.error('Error in fallback SQL query:', sqlError);
          // Last resort - return empty array to avoid breaking form creation
          return [];
        }
      }
    } catch (error) {
      logger.error('Error in getFormsByAssessment:', error);
      // Don't let form checking completely fail
      return [];
    }
  }

  /**
   * Get all forms in the system
   */
  async getAllForms(): Promise<Form[]> {
    return db.select().from(forms);
  }

  /**
   * Get a form by its access token
   */
  async getFormByAccessToken(token: string): Promise<Form | undefined> {
    try {
      // First check if the accessToken column exists by querying the schema
      const columnCheckResult = await db.execute(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'forms' AND column_name = 'access_token'
      `);

      // If column doesn't exist, return undefined instead of causing an error
      if (!columnCheckResult.rows || columnCheckResult.rows.length === 0) {
        logger.info('The access_token column does not exist yet. This is expected if the schema has not been fully migrated.');
        return undefined;
      }

      const [form] = await db.select()
        .from(forms)
        .where(eq(forms.accessToken, token));
      return form;
    } catch (error) {
      logger.error('Error in getFormByAccessToken:', error);
      return undefined;
    }
  }

  async createForm(insertForm: InsertForm): Promise<Form> {
    const formWithDefaults = {
      ...insertForm,
      status: insertForm.status || 'not_started',
      data: insertForm.data || null,
      updatedAt: new Date()
    };

    const [form] = await db.insert(forms).values(formWithDefaults).returning();
    return form;
  }

  async updateForm(id: number, formData: Partial<Form>): Promise<Form | undefined> {
    const [updatedForm] = await db
      .update(forms)
      .set({
        ...formData,
        updatedAt: new Date()
      })
      .where(eq(forms.id, id))
      .returning();
    return updatedForm;
  }

  // Form Questions methods
  async getFormQuestion(id: number): Promise<FormQuestion | undefined> {
    const [question] = await db.select().from(formQuestions).where(eq(formQuestions.id, id));
    return question;
  }

  async getFormQuestionsByType(formType: string): Promise<FormQuestion[]> {
    return db.select()
      .from(formQuestions)
      .where(eq(formQuestions.formType, formType))
      .orderBy(formQuestions.section, formQuestions.order);
  }

  async getFormQuestionsBySection(formType: string, section: string): Promise<FormQuestion[]> {
    return db.select()
      .from(formQuestions)
      .where(and(
        eq(formQuestions.formType, formType),
        eq(formQuestions.section, section)
      ))
      .orderBy(formQuestions.order);
  }

  async createFormQuestion(question: InsertFormQuestion): Promise<FormQuestion> {
    const [newQuestion] = await db.insert(formQuestions).values(question).returning();
    return newQuestion;
  }

  async bulkCreateFormQuestions(questions: InsertFormQuestion[]): Promise<FormQuestion[]> {
    const newQuestions = await db.insert(formQuestions).values(questions).returning();
    return newQuestions;
  }

  // Form Responses methods
  async getFormResponse(id: number): Promise<FormResponse | undefined> {
    const [response] = await db.select().from(formResponses).where(eq(formResponses.id, id));
    return response;
  }

  async getFormResponseByQuestion(formId: number, questionId: number): Promise<FormResponse | undefined> {
    const [response] = await db.select()
      .from(formResponses)
      .where(and(
        eq(formResponses.formId, formId),
        eq(formResponses.questionId, questionId)
      ));
    return response;
  }

  async getFormResponses(formId: number): Promise<FormResponse[]> {
    return db.select()
      .from(formResponses)
      .where(eq(formResponses.formId, formId));
  }

  async createFormResponse(response: InsertFormResponse): Promise<FormResponse> {
    const [newResponse] = await db.insert(formResponses).values(response).returning();
    return newResponse;
  }

  async updateFormResponse(id: number, responseData: Partial<FormResponse>): Promise<FormResponse | undefined> {
    const [updatedResponse] = await db
      .update(formResponses)
      .set({
        ...responseData,
        updatedAt: new Date()
      })
      .where(eq(formResponses.id, id))
      .returning();
    return updatedResponse;
  }

  async bulkUpsertFormResponses(responses: any[]): Promise<FormResponse[]> {
    logger.info("Bulk upserting form responses:", JSON.stringify(responses, null, 2));

    // For each response, check if it exists and update, otherwise create
    const results: FormResponse[] = [];

    for (const response of responses) {
      try {
        // Handle different response formats
        if (response.questionKey && !response.questionId) {
          logger.info(`Processing response with questionKey: ${response.questionKey}`);

          // For forms without proper question IDs, store data directly in the form.data field
          const formId = response.formId;
          const [form] = await db.select().from(forms).where(eq(forms.id, formId));

          if (form) {
            // Get current form data or initialize empty object
            let formData = {};
            try {
              // Handle form data based on type
              if (form.data) {
                if (typeof form.data === 'string') {
                  formData = JSON.parse(form.data);
                  logger.info(`Successfully parsed form data from string for form ${formId}`);
                } else {
                  formData = form.data;
                  logger.info(`Using object form data for form ${formId}`);
                }
              }
            } catch (e) {
              logger.error(`Error parsing form data for form ${formId}:`, e);
              formData = {};
            }

            // Update with new value
            logger.info(`Setting form data field: ${response.questionKey} = ${response.value}`);
            formData[response.questionKey] = response.value;

            // Convert to JSON string before saving
            const formDataStr = JSON.stringify(formData);
            logger.info(`Saving form data (${formDataStr.length} chars) to form ${formId}`);

            // Update the form with properly stringified JSON
            await db.update(forms)
              .set({ 
                data: formDataStr,
                updatedAt: new Date()
              })
              .where(eq(forms.id, formId));

            // Add a placeholder result
            results.push({
              id: 0,
              formId: formId,
              questionId: 0,
              value: response.value,
              createdAt: new Date(),
              updatedAt: new Date()
            });
          }
        } 
        else {
          // Standard response with question ID
          const existing = await this.getFormResponseByQuestion(response.formId, response.questionId);

          if (existing) {
            // Update existing response
            const updated = await this.updateFormResponse(existing.id, response);
            if (updated) results.push(updated);
          } else {
            // Create new response
            const created = await this.createFormResponse(response);
            results.push(created);
          }
        }
      } catch (error) {
        logger.error("Error processing form response:", error);
        // Continue with next response instead of failing the whole batch
      }
    }

    return results;
  }

  // Documents
  async getDocument(id: number): Promise<Document | undefined> {
    const [document] = await db.select().from(documents).where(eq(documents.id, id));
    return document;
  }

  async getDocumentsByAssessment(assessmentId: number): Promise<Document[]> {
    try {
      logger.info(`Database query: fetching documents for assessment ${assessmentId}`);
      // Use raw SQL query as fallback to diagnose the issue
      const result = await db.execute(sql`SELECT * FROM documents WHERE assessment_id = ${assessmentId}`);
      logger.info(`Raw SQL query successful: found ${result.rows.length} documents`);
      // Transform raw result to match Document type
      return result.rows.map((row: any) => ({
        id: row.id,
        assessmentId: row.assessment_id,
        name: row.name,
        type: row.type,
        data: row.data,
        filePath: row.file_path,
        fileName: row.file_name,
        originalName: row.original_name,
        mimeType: row.mime_type,
        fileSize: row.file_size,
        uploadedById: row.uploaded_by_id,
        createdAt: row.created_at
      }));
    } catch (error) {
      logger.error(`Database error in getDocumentsByAssessment:`, error);
      logger.error(`Error message: ${error instanceof Error ? error.message : String(error)}`);
      logger.error(`Error stack: ${error instanceof Error ? error.stack : 'No stack'}`);
      // Return empty array as fallback
      return [];
    }
  }

  async createDocument(insertDocument: InsertDocument): Promise<Document> {
    try {
      logger.info(`Creating document with data:`, JSON.stringify(insertDocument, null, 2));

      // Use raw SQL query to avoid schema import issues
      const [document] = await db.insert(documents).values({
        assessmentId: insertDocument.assessmentId,
        name: insertDocument.name,
        type: insertDocument.type,
        data: insertDocument.data,
        filePath: insertDocument.filePath,
        fileName: insertDocument.fileName,
        originalName: insertDocument.originalName,
        mimeType: insertDocument.mimeType,
        fileSize: insertDocument.fileSize,
        uploadedById: insertDocument.uploadedById,
        createdAt: new Date()
      }).returning();

      if (!document) {
        throw new Error('Failed to create document');
      }

      logger.info(`Document created successfully with ID: ${document.id}`);
      return document;
    } catch (error) {
      logger.error(`Database error in createDocument:`, error);
      throw error;
    }
  }

  // Delete a document by ID
  async deleteDocument(documentId: number): Promise<void> {
    await db.delete(documents).where(eq(documents.id, documentId));
  }

  // Assessment Notes methods
  async createAssessmentNote(data: InsertAssessmentNote): Promise<AssessmentNote> {
    const [note] = await db.insert(assessmentNotes).values(data).returning();
    return note;
  }

  async getAssessmentNotes(assessmentId: number): Promise<(AssessmentNote & { user: { fullName: string; role: string } })[]> {
    const notes = await db
      .select({
        id: assessmentNotes.id,
        assessmentId: assessmentNotes.assessmentId,
        userId: assessmentNotes.userId,
        note: assessmentNotes.note,
        createdAt: assessmentNotes.createdAt,
        updatedAt: assessmentNotes.updatedAt,
        user: {
          fullName: users.fullName,
          role: users.role,
        },
      })
      .from(assessmentNotes)
      .leftJoin(users, eq(assessmentNotes.userId, users.id))
      .where(eq(assessmentNotes.assessmentId, assessmentId))
      .orderBy(desc(assessmentNotes.createdAt));

    return notes;
  }

  async updateAssessmentNote(noteId: number, data: Partial<InsertAssessmentNote>): Promise<AssessmentNote> {
    const [note] = await db
      .update(assessmentNotes)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(assessmentNotes.id, noteId))
      .returning();
    return note;
  }

  async deleteAssessmentNote(noteId: number): Promise<void> {
    await db.delete(assessmentNotes).where(eq(assessmentNotes.id, noteId));
  }

  // Activities
  async createActivity(insertActivity: InsertActivity): Promise<Activity> {
    const activityWithDefaults = {
      ...insertActivity,
      userId: insertActivity.userId || null,
      assessmentId: insertActivity.assessmentId || null,
      details: insertActivity.details || null
    };

    const [activity] = await db.insert(activities).values(activityWithDefaults).returning();
    return activity;
  }

  async listActivitiesByAssessment(assessmentId: number): Promise<Activity[]> {
    return db.select()
      .from(activities)
      .where(eq(activities.assessmentId, assessmentId))
      .orderBy(desc(activities.createdAt));
  }

  async listRecentActivities(limit: number): Promise<Activity[]> {
    return db.select()
      .from(activities)
      .orderBy(desc(activities.createdAt))
      .limit(limit);
  }

  // Admin activity logs
  async createAdminActivityLog(logData: { adminId: number, action: string, targetUserId: number | null, details: string, createdAt: Date }): Promise<void> {
    await db.insert(adminActivityLogs).values(logData);
  }

  async getAdminActivityLogs(limit = 100): Promise<any[]> {
    const logs = await db
      .select({
        id: adminActivityLogs.id,
        adminId: adminActivityLogs.adminId,
        targetUserId: adminActivityLogs.targetUserId,
        action: adminActivityLogs.action,
        details: adminActivityLogs.details,
        createdAt: adminActivityLogs.createdAt
      })
      .from(adminActivityLogs)
      .orderBy(desc(adminActivityLogs.createdAt))
      .limit(limit);

    return logs;
  }

  /**
   * Update a user's password with proper hashing
   */
  async updateUserPassword(userId: number, newPassword: string): Promise<boolean> {
    try {
      // Hash the new password
      const hashedPassword = await hashPassword(newPassword);

      // Update the user's password in the database
      const [updatedUser] = await db
        .update(users)
        .set({ password: hashedPassword })
        .where(eq(users.id, userId))
        .returning();

      // Log the password change (without the actual password)
      logger.info(`Password updated for user ID: ${userId}`);

      return !!updatedUser;
    } catch (error) {
      logger.error('Error updating user password:', error);
      return false;
    }
  }

  /**
   * Update a user's status
   */
  async updateUserStatus(userId: number, status: string): Promise<boolean> {
    try {
      await db
        .update(users)
        .set({ status: status as any })
        .where(eq(users.id, userId));

      logger.info(`Status updated for user ID: ${userId} to ${status}`);
      return true;
    } catch (error) {
      logger.error(`Error updating user status: ${error}`);
      return false;
    }
  }

  // Conversations
  async getConversation(id: number): Promise<Conversation | undefined> {
    try {
      const [conversation] = await db.select().from(conversations).where(eq(conversations.id, id));
      return conversation;
    } catch (error) {
      logger.error('Error in getConversation:', error);
      return undefined;
    }
  }

  async getConversationWithParticipants(id: number): Promise<(Conversation & { participants: User[] }) | undefined> {
    try {
      const conversation = await this.getConversation(id);
      if (!conversation) return undefined;

      // Get all participants for this conversation
      const participants = await db
        .select({
          user: users
        })
        .from(conversationParticipants)
        .innerJoin(users, eq(conversationParticipants.userId, users.id))
        .where(eq(conversationParticipants.conversationId, id))
        .then(rows => rows.map(row => row.user));

      return {
        ...conversation,
        participants
      };
    } catch (error) {
      logger.error('Error in getConversationWithParticipants:', error);
      return undefined;
    }
  }

  async createConversation(conversation: InsertConversation): Promise<Conversation> {
    try {
      const [newConversation] = await db
        .insert(conversations)
        .values({
          ...conversation,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      return newConversation;
    } catch (error) {
      logger.error('Error in createConversation:', error);
      throw error;
    }
  }

  async updateConversation(id: number, conversationData: Partial<Conversation>): Promise<Conversation | undefined> {
    try {
      const [updatedConversation] = await db
        .update(conversations)
        .set({
          ...conversationData,
          updatedAt: new Date()
        })
        .where(eq(conversations.id, id))
        .returning();
      return updatedConversation;
    } catch (error) {
      logger.error('Error in updateConversation:', error);
      return undefined;
    }
  }

  async getConversationsByUser(userId: number): Promise<Conversation[]> {
    try {
      // Get all conversations where the user is a participant
      const result = await db
        .select({
          conversation: conversations
        })
        .from(conversationParticipants)
        .innerJoin(conversations, eq(conversationParticipants.conversationId, conversations.id))
        .where(eq(conversationParticipants.userId, userId))
        .orderBy(desc(conversations.updatedAt));

      return result.map(r => r.conversation);
    } catch (error) {
      logger.error('Error in getConversationsByUser:', error);
      return [];
    }
  }

  async getConversationsByAssessment(assessmentId: number): Promise<Conversation[]> {
    try {
      return db
        .select()
        .from(conversations)
        .where(eq(conversations.assessmentId, assessmentId))
        .orderBy(desc(conversations.updatedAt));
    } catch (error) {
      logger.error('Error in getConversationsByAssessment:', error);
      return [];
    }
  }

  async addParticipantToConversation(conversationId: number, userId: number): Promise<ConversationParticipant> {
    try {
      // Check if already a participant
      const [existingParticipant] = await db
        .select()
        .from(conversationParticipants)
        .where(
          and(
            eq(conversationParticipants.conversationId, conversationId),
            eq(conversationParticipants.userId, userId)
          )
        );

      if (existingParticipant) {
        // If the participant left before, update the leftAt to null
        if (existingParticipant.leftAt) {
          const [updatedParticipant] = await db
            .update(conversationParticipants)
            .set({ leftAt: null })
            .where(eq(conversationParticipants.id, existingParticipant.id))
            .returning();
          return updatedParticipant;
        }
        return existingParticipant;
      }

      // Otherwise create a new participant record
      const [newParticipant] = await db
        .insert(conversationParticipants)
        .values({
          conversationId,
          userId,
          joinedAt: new Date()
        })
        .returning();
      return newParticipant;
    } catch (error) {
      logger.error('Error in addParticipantToConversation:', error);
      throw error;
    }
  }

  async removeParticipantFromConversation(conversationId: number, userId: number): Promise<boolean> {
    try {
      // Set leftAt timestamp instead of removing the record
      await db
        .update(conversationParticipants)
        .set({ leftAt: new Date() })
        .where(
          and(
            eq(conversationParticipants.conversationId, conversationId),
            eq(conversationParticipants.userId, userId)
          )
        );
      return true;
    } catch (error) {
      logger.error('Error in removeParticipantFromConversation:', error);
      return false;
    }
  }

  // Messages
  async getMessage(id: number): Promise<Message | undefined> {
    try {
      const [message] = await db.select().from(messages).where(eq(messages.id, id));
      return message;
    } catch (error) {
      logger.error('Error in getMessage:', error);
      return undefined;
    }
  }

  async getMessageWithSender(id: number): Promise<(Message & { sender: User }) | undefined> {
    try {
      const message = await this.getMessage(id);
      if (!message) return undefined;

      const [sender] = await db.select().from(users).where(eq(users.id, message.senderId));
      if (!sender) return undefined;

      return {
        ...message,
        sender
      };
    } catch (error) {
      logger.error('Error in getMessageWithSender:', error);
      return undefined;
    }
  }

  async createMessage(message: InsertMessage): Promise<Message> {
    try {
      const [newMessage] = await db
        .insert(messages)
        .values({
          ...message,
          createdAt: new Date()
        })
        .returning();

      // Update the conversation's updatedAt timestamp
      await db
        .update(conversations)
        .set({ updatedAt: new Date() })
        .where(eq(conversations.id, message.conversationId));

      // Add recipients for all active participants in the conversation
      const participants = await db
        .select()
        .from(conversationParticipants)
        .where(
          and(
            eq(conversationParticipants.conversationId, message.conversationId),
            eq(conversationParticipants.leftAt, null)
          )
        );

      // Create a recipient record for each participant (except the sender)
      for (const participant of participants) {
        if (participant.userId !== message.senderId) {
          await db.insert(messageRecipients).values({
            messageId: newMessage.id,
            userId: participant.userId,
            status: 'unread'
          });
        }
      }

      return newMessage;
    } catch (error) {
      logger.error('Error in createMessage:', error);
      throw error;
    }
  }

  async updateMessage(id: number, messageData: Partial<Message>): Promise<Message | undefined> {
    try {
      const [updatedMessage] = await db
        .update(messages)
        .set({
          ...messageData,
          updatedAt: new Date(),
          editedAt: new Date()
        })
        .where(eq(messages.id, id))
        .returning();
      return updatedMessage;
    } catch (error) {
      logger.error('Error in updateMessage:', error);
      return undefined;
    }
  }

  async getMessagesByConversation(conversationId: number): Promise<Message[]> {
    try {
      return db
        .select()
        .from(messages)
        .where(eq(messages.conversationId, conversationId))
        .orderBy(messages.createdAt);
    } catch (error) {
      logger.error('Error in getMessagesByConversation:', error);
      return [];
    }
  }

  async getMessagesByUser(userId: number): Promise<Message[]> {
    try {
      return db
        .select()
        .from(messages)
        .where(eq(messages.senderId, userId))
        .orderBy(desc(messages.createdAt));
    } catch (error) {
      logger.error('Error in getMessagesByUser:', error);
      return [];
    }
  }

  async markMessageAsRead(messageId: number, userId: number): Promise<boolean> {
    try {
      const [recipient] = await db
        .select()
        .from(messageRecipients)
        .where(
          and(
            eq(messageRecipients.messageId, messageId),
            eq(messageRecipients.userId, userId)
          )
        );

      if (!recipient) return false;

      await db
        .update(messageRecipients)
        .set({
          status: 'read',
          readAt: new Date()
        })
        .where(eq(messageRecipients.id, recipient.id));

      return true;
    } catch (error) {
      logger.error('Error in markMessageAsRead:', error);
      return false;
    }
  }

  // Notifications
  async getNotification(id: number): Promise<Notification | undefined> {
    try {
      const [notification] = await db.select().from(notifications).where(eq(notifications.id, id));
      return notification;
    } catch (error) {
      logger.error('Error in getNotification:', error);
      return undefined;
    }
  }

  async createNotification(notification: InsertNotification): Promise<Notification> {
    try {
      const [newNotification] = await db
        .insert(notifications)
        .values({
          ...notification,
          createdAt: new Date()
        })
        .returning();
      return newNotification;
    } catch (error) {
      logger.error('Error in createNotification:', error);
      throw error;
    }
  }

  async getUnreadNotificationCount(userId: number): Promise<number> {
    try {
      const result = await db
        .select({ count: sql`COUNT(*)` })
        .from(notifications)
        .where(
          and(
            eq(notifications.userId, userId),
            eq(notifications.status, 'unread')
          )
        );

      // Handle case where result[0] might be undefined
      if (!result || !result[0]) {
        return 0;
      }

      return Number(result[0].count);
    } catch (error) {
      logger.error('Error in getUnreadNotificationCount:', error);
      return 0;
    }
  }

  async getNotificationsByUser(userId: number, limit = 20, includeRead = false): Promise<Notification[]> {
    try {
      let whereClause = eq(notifications.userId, userId);

      // If we should only include unread notifications, add that to the where clause
      if (!includeRead) {
        whereClause = and(whereClause, eq(notifications.status, 'unread'));
      }

      const result = await db
        .select()
        .from(notifications)
        .where(whereClause)
        .orderBy(desc(notifications.createdAt))
        .limit(limit);

      logger.info(`Retrieved ${result.length} notifications for user ${userId}, includeRead=${includeRead}`);

      return result;
    } catch (error) {
      logger.error('Error in getNotificationsByUser:', error);
      return [];
    }
  }

  async markNotificationAsRead(notificationId: number): Promise<boolean> {
    try {
      await db
        .update(notifications)
        .set({
          status: 'read',
          readAt: new Date()
        })
        .where(eq(notifications.id, notificationId));
      return true;
    } catch (error) {
      logger.error('Error in markNotificationAsRead:', error);
      return false;
    }
  }

  async markAllNotificationsAsRead(userId: number): Promise<boolean> {
    try {
      await db
        .update(notifications)
        .set({
          status: 'read',
          readAt: new Date()
        })
        .where(
          and(
            eq(notifications.userId, userId),
            eq(notifications.status, 'unread')
          )
        );
      return true;
    } catch (error) {
      logger.error('Error in markAllNotificationsAsRead:', error);
      return false;
    }
  }

  // Notification Preferences
  async getNotificationPreferences(userId: number): Promise<NotificationPreference | undefined> {
    try {
      const [prefs] = await db
        .select()
        .from(notificationPreferences)
        .where(eq(notificationPreferences.userId, userId));
      return prefs;
    } catch (error) {
      logger.error('Error in getNotificationPreferences:', error);
      return undefined;
    }
  }

  async createOrUpdateNotificationPreferences(userId: number, preferences: any): Promise<NotificationPreference> {
    try {
      // Check if preferences already exist
      const existingPrefs = await this.getNotificationPreferences(userId);

      if (existingPrefs) {
        // Update existing preferences
        const [updatedPrefs] = await db
          .update(notificationPreferences)
          .set({
            preferences,
            updatedAt: new Date()
          })
          .where(eq(notificationPreferences.id, existingPrefs.id))
          .returning();
        return updatedPrefs;
      } else {
        // Create new preferences
        const [newPrefs] = await db
          .insert(notificationPreferences)
          .values({
            userId,
            preferences,
            updatedAt: new Date()
          })
          .returning();
        return newPrefs;
      }
    } catch (error) {
      logger.error('Error in createOrUpdateNotificationPreferences:', error);
      throw error;
    }
  }

  // Referrals
  async getReferral(id: number): Promise<Referral | undefined> {
    const [referral] = await db
      .select({
        id: referrals.id,
        trackingId: referrals.trackingId,
        referralType: referrals.referralType,
        status: referrals.status,
        referringUserId: referrals.referringUserId,
        assesseeId: referrals.assesseeId,
        assessmentId: referrals.assessmentId,
        schoolName: referrals.schoolName,
        schoolEmail: referrals.schoolEmail,
        schoolPhone: referrals.schoolPhone,
        department: referrals.department,
        staffName: referrals.staffName,
        staffPosition: referrals.staffPosition,
        parentName: referrals.parentName,
        parentEmail: referrals.parentEmail,
        parentPhone: referrals.parentPhone,
        relationship: referrals.relationship,
        assesseeFullName: referrals.assesseeFullName,
        assesseeEmail: referrals.assesseeEmail,
        assesseePhone: referrals.assesseePhone,
        dateOfBirth: referrals.dateOfBirth,
        assesseeYear: referrals.assesseeYear,
        assesseeCourse: referrals.assesseeCourse,
        assessmentConcerns: referrals.assessmentConcerns,
        reasonForReferral: referrals.reasonForReferral,
        previousAssessment: referrals.previousAssessment,
        previousAssessmentDetails: referrals.previousAssessmentDetails,
        additionalNotes: referrals.additionalNotes,
        schoolContactConsent: referrals.schoolContactConsent,
        createdAt: referrals.createdAt,
        updatedAt: referrals.updatedAt
      })
      .from(referrals)
      .where(eq(referrals.id, id));
    // referral may not include assessmentConcerns in older database versions
    return referral as Referral | undefined;
  }

  async getReferralByTrackingId(trackingId: string): Promise<Referral | undefined> {
    const [referral] = await db
      .select({
        id: referrals.id,
        trackingId: referrals.trackingId,
        referralType: referrals.referralType,
        status: referrals.status,
        referringUserId: referrals.referringUserId,
        assesseeId: referrals.assesseeId,
        assessmentId: referrals.assessmentId,
        schoolName: referrals.schoolName,
        schoolEmail: referrals.schoolEmail,
        schoolPhone: referrals.schoolPhone,
        department: referrals.department,
        staffName: referrals.staffName,
        staffPosition: referrals.staffPosition,
        parentName: referrals.parentName,
        parentEmail: referrals.parentEmail,
        parentPhone: referrals.parentPhone,
        relationship: referrals.relationship,
        assesseeFullName: referrals.assesseeFullName,
        assesseeEmail: referrals.assesseeEmail,
        assesseePhone: referrals.assesseePhone,
        dateOfBirth: referrals.dateOfBirth,
        assesseeYear: referrals.assesseeYear,
        assesseeCourse: referrals.assesseeCourse,
        assessmentConcerns: referrals.assessmentConcerns,
        reasonForReferral: referrals.reasonForReferral,
        previousAssessment: referrals.previousAssessment,
        previousAssessmentDetails: referrals.previousAssessmentDetails,
        additionalNotes: referrals.additionalNotes,
        schoolContactConsent: referrals.schoolContactConsent,
        createdAt: referrals.createdAt,
        updatedAt: referrals.updatedAt
      })
      .from(referrals)
      .where(eq(referrals.trackingId, trackingId));
    // referral may not include assessmentConcerns in older database versions
    return referral as Referral | undefined;
  }

  async getReferralByAssessmentId(assessmentId: number): Promise<Referral | undefined> {
    const [referral] = await db
      .select({
        id: referrals.id,
        trackingId: referrals.trackingId,
        referralType: referrals.referralType,
        status: referrals.status,
        referringUserId: referrals.referringUserId,
        assesseeId: referrals.assesseeId,
        assessmentId: referrals.assessmentId,
        schoolName: referrals.schoolName,
        schoolEmail: referrals.schoolEmail,
        schoolPhone: referrals.schoolPhone,
        department: referrals.department,
        staffName: referrals.staffName,
        staffPosition: referrals.staffPosition,
        parentName: referrals.parentName,
        parentEmail: referrals.parentEmail,
        parentPhone: referrals.parentPhone,
        relationship: referrals.relationship,
        assesseeFullName: referrals.assesseeFullName,
        assesseeEmail: referrals.assesseeEmail,
        assesseePhone: referrals.assesseePhone,
        dateOfBirth: referrals.dateOfBirth,
        assesseeYear: referrals.assesseeYear,
        assesseeCourse: referrals.assesseeCourse,
        assessmentConcerns: referrals.assessmentConcerns,
        reasonForReferral: referrals.reasonForReferral,
        previousAssessment: referrals.previousAssessment,
        previousAssessmentDetails: referrals.previousAssessmentDetails,
        additionalNotes: referrals.additionalNotes,
        schoolContactConsent: referrals.schoolContactConsent,
        createdAt: referrals.createdAt,
        updatedAt: referrals.updatedAt
      })
      .from(referrals)
      .where(eq(referrals.assessmentId, assessmentId));
    // referral may not include assessmentConcerns in older database versions
    return referral as Referral | undefined;
  }

  async createReferral(insertReferral: InsertReferral): Promise<Referral> {
    try {
      const [referral] = await db
        .insert(referrals)
        .values(insertReferral)
        .returning();
      return referral;
    } catch (error) {
      logger.error('Error in createReferral:', error);
      throw error;
    }
  }

  async updateReferral(id: number, referralData: Partial<Referral>): Promise<Referral | undefined> {
    try {
      const [referral] = await db
        .update(referrals)
        ```text
.set({
          ...referralData,
          updatedAt: new Date()
        })
        .where(eq(referrals.id, id))
        .returning();
      return referral;
    } catch (error) {
      logger.error('Error in updateReferral:', error);
      throw error;
    }
  }

  // Password Reset Tokens
  async storePasswordResetToken(userId: number, token: string, expires: Date): Promise<boolean> {
    try {
      // First check if there's an existing token for this user and invalidate it
      await db
        .update(passwordResetTokens)
        .set({ usedAt: new Date() })
        .where(and(
          eq(passwordResetTokens.userId, userId),
          isNull(passwordResetTokens.usedAt)
        ));

      // Create a new token
      await db
        .insert(passwordResetTokens)
        .values({
          userId,
          token,
          expires,
          createdAt: new Date()
        });

      return true;
    } catch (error) {
      logger.error('Error storing password reset token:', error);
      return false;
    }
  }

  async getPasswordResetToken(token: string): Promise<{ userId: number, expires: Date } | null> {
    try {
      const [resetToken] = await db
        .select()
        .from(passwordResetTokens)
        .where(and(
          eq(passwordResetTokens.token, token),
          isNull(passwordResetTokens.usedAt),
          gt(passwordResetTokens.expires, new Date())
        ));

      if (!resetToken) {
        return null;
      }

      return {
        userId: resetToken.userId,
        expires: resetToken.expires
      };
    } catch (error) {
      logger.error('Error retrieving password reset token:', error);
      return null;
    }
  }

  async invalidatePasswordResetToken(token: string): Promise<boolean> {
    try {
      await db
        .update(passwordResetTokens)
        .set({ usedAt: new Date() })
        .where(eq(passwordResetTokens.token, token));

      return true;
    } catch (error) {
      logger.error('Error invalidating password reset token:', error);
      return false;
    }
  }

  // Issues management methods
  async getIssue(id: number): Promise<Issue | undefined> {
    const [issue] = await db
      .select()
      .from(issues)
      .where(eq(issues.id, id));
    return issue || undefined;
  }

  async createIssue(insertIssue: InsertIssue): Promise<Issue> {
    const [issue] = await db
      .insert(issues)
      .values({
        ...insertIssue,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();
    return issue;
  }

  async updateIssue(id: number, issueData: Partial<Issue>): Promise<Issue | undefined> {
    const [updatedIssue] = await db
      .update(issues)
      .set({
        ...issueData,
        updatedAt: new Date()
      })
      .where(eq(issues.id, id))
      .returning();
    return updatedIssue || undefined;
  }

  async listIssues(filters?: { status?: string; priority?: string; category?: string; reportedById?: number }): Promise<(Issue & { reportedBy: User; assignedTo?: User })[]> {
    // Build where conditions array
    const whereConditions = [];
    if (filters?.status) {
      whereConditions.push(eq(issues.status, filters.status));
    }
    if (filters?.priority) {
      whereConditions.push(eq(issues.priority, filters.priority));
    }
    if (filters?.category) {
      whereConditions.push(eq(issues.category, filters.category));
    }
    if (filters?.reportedById) {
      whereConditions.push(eq(issues.reportedById, filters.reportedById));
    }

    // Build the base query
    let query = db
      .select({
        id: issues.id,
        title: issues.title,
        description: issues.description,
        priority: issues.priority,
        status: issues.status,
        category: issues.category,
        reportedById: issues.reportedById,
        assignedToId: issues.assignedToId,
        attachments: issues.attachments,
        metadata: issues.metadata,
        resolvedAt: issues.resolvedAt,
        resolvedById: issues.resolvedById,
        resolutionNotes: issues.resolutionNotes,
        createdAt: issues.createdAt,
        updatedAt: issues.updatedAt,
        reportedBy: {
          id: users.id,
          email: users.email,
          username: users.username,
          fullName: users.fullName,
          role: users.role,
          status: users.status,
          phone: users.phone,
          organization: users.organization,
          position: users.position,
          department: users.department,
          createdAt: users.createdAt
        }
      })
      .from(issues)
      .innerJoin(users, eq(issues.reportedById, users.id))
      .orderBy(desc(issues.createdAt));

    // Apply where conditions if any exist
    if (whereConditions.length > 0) {
      if (whereConditions.length === 1) {
        query = query.where(whereConditions[0]);
      } else {
        query = query.where(and(...whereConditions));
      }
    }

    const result = await query;

    // Add assignedTo property as null for now (can be enhanced later)
    return result.map(issue => ({
      ...issue,
      assignedTo: null
    })) as any;
  }

  async resolveIssue(id: number, resolvedById: number, resolutionNotes?: string): Promise<Issue | undefined> {
    const [resolvedIssue] = await db
      .update(issues)
      .set({
        status: 'resolved',
        resolvedAt: new Date(),
        resolvedById,
        resolutionNotes,
        updatedAt: new Date()
      })
      .where(eq(issues.id, id))
      .returning();
    return resolvedIssue || undefined;
  }

  async assignIssue(id: number, assignedToId: number): Promise<Issue | undefined> {
    const [assignedIssue] = await db
      .update(issues)
      .set({
        assignedToId,
        updatedAt: new Date()
      })
      .where(eq(issues.id, id))
      .returning();
    return assignedIssue || undefined;
  }

  // Issue Comments methods
  async createIssueComment(comment: InsertIssueComment): Promise<IssueComment> {
    const [newComment] = await db
      .insert(issueComments)
      .values({
        ...comment,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();
    return newComment;
  }

  async getIssueComments(issueId: number): Promise<(IssueComment & { user: User })[]> {
    const result = await db
      .select({
        id: issueComments.id,
        issueId: issueComments.issueId,
        userId: issueComments.userId,
        comment: issueComments.comment,
        commentType: issueComments.commentType,
        metadata: issueComments.metadata,
        createdAt: issueComments.createdAt,
        updatedAt: issueComments.updatedAt,
        user: {
          id: users.id,
          fullName: users.fullName,
          email: users.email,
          role: users.role
        }
      })
      .from(issueComments)
      .innerJoin(users, eq(issueComments.userId, users.id))
      .where(eq(issueComments.issueId, issueId))
      .orderBy(desc(issueComments.createdAt)); // Most recent first

    return result as (IssueComment & { user: User })[];
  }

  async updateIssueComment(id: number, commentData: Partial<IssueComment>): Promise<IssueComment | undefined> {
    const [updatedComment] = await db
      .update(issueComments)
      .set({
        ...commentData,
        updatedAt: new Date()
      })
      .where(eq(issueComments.id, id))
      .returning();
    return updatedComment || undefined;
  }
}