import { logger } from '../logger';
import { Request, Response } from 'express';
import { storage } from '../storage';
import { users } from '../../shared/schema';
import { eq } from 'drizzle-orm';
import { z } from 'zod';

/**
 * Get all users (admin only)
 */
export async function getAllUsers(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }
  
  try {
    const allUsers = await storage.listUsers();
    res.json(allUsers);
  } catch (error) {
    logger.error('Error fetching users:', error);
    res.status(500).json({ message: "Failed to fetch users" });
  }
}

/**
 * Update user information (admin only)
 */
export async function updateUser(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }
  
  try {
    const userId = parseInt(req.params.id);
    const userData = req.body;
    
    // Only allow certain fields to be updated
    const allowedFields = ['fullName', 'email', 'phone', 'organization', 'position', 'department', 'status'];
    const sanitizedData: Record<string, any> = {};
    
    for (const field of allowedFields) {
      if (field in userData) {
        // Convert camelCase to snake_case for database fields
        const dbField = field.replace(/([A-Z])/g, '_$1').toLowerCase();
        sanitizedData[dbField] = userData[field];
      }
    }
    
    // Check if the user exists
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    
    // Update the user in the database
    const updatedUser = await storage.updateUser(userId, sanitizedData);
    
    // Log the activity
    await storage.createAdminActivityLog({
      adminId: req.user.id,
      action: 'update_user',
      targetUserId: userId,
      details: `Updated user: ${user.email}`,
      createdAt: new Date()
    });
    
    res.json(updatedUser);
  } catch (error) {
    logger.error('Error updating user:', error);
    res.status(500).json({ message: "Failed to update user" });
  }
}