import { logger } from '../logger';
import { Request, Response } from "express";
import { storage } from "../storage";
import { 
  insertConversationSchema, 
  insertMessageSchema, 
  insertNotificationSchema,
  insertConversationParticipantSchema
} from "@shared/schema";
import { z } from "zod";

// Conversation handlers
export async function getConversations(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }

    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "User ID not found" });
    }

    const conversations = await storage.getConversationsByUser(userId);
    
    // Enhanced conversations with additional data
    const enhancedConversations = await Promise.all(
      conversations.map(async (conversation) => {
        const fullConversation = await storage.getConversationWithParticipants(conversation.id);
        const messages = await storage.getMessagesByConversation(conversation.id);
        const latestMessage = messages.length > 0 ? messages[messages.length - 1] : null;
        
        // Get unread count for this user
        let unreadCount = 0;
        if (latestMessage) {
          const allMessages = await storage.getMessagesByConversation(conversation.id);
          // Check if message is unread by looking for recipient status
          unreadCount = allMessages.filter(m => 
            m.senderId !== userId
          ).length;
        }
        
        return {
          ...conversation,
          participants: fullConversation?.participants || [],
          latestMessage,
          unreadCount
        };
      })
    );
    
    return res.status(200).json(enhancedConversations);
  } catch (error) {
    logger.error("Error fetching conversations:", error);
    return res.status(500).json({ message: "Failed to fetch conversations" });
  }
}

export async function getConversation(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const conversationId = parseInt(req.params.id);
    if (isNaN(conversationId)) {
      return res.status(400).json({ message: "Invalid conversation ID" });
    }
    
    const conversation = await storage.getConversationWithParticipants(conversationId);
    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }
    
    // Check if the user is a participant
    const userId = req.user?.id;
    const isParticipant = conversation.participants.some(p => p.id === userId);
    if (!isParticipant && req.user?.role !== 'admin') {
      return res.status(403).json({ message: "You don't have access to this conversation" });
    }
    
    // Get messages for this conversation
    const basicMessages = await storage.getMessagesByConversation(conversationId);
    
    // Mark messages as read for this user and enrich with sender info
    const messages = await Promise.all(basicMessages.map(async (message) => {
      if (message.senderId !== userId) {
        await storage.markMessageAsRead(message.id, userId!);
      }
      
      // Get sender information
      const sender = await storage.getUser(message.senderId);
      return {
        ...message,
        sender
      };
    }));
    
    return res.status(200).json({ conversation, messages });
  } catch (error) {
    logger.error("Error fetching conversation:", error);
    return res.status(500).json({ message: "Failed to fetch conversation" });
  }
}

export async function createConversation(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    logger.info("Creating conversation with body:", req.body);
    logger.info("Current authenticated user:", req.user?.id);
    
    // Ensure createdById matches authenticated user - security measure
    if (req.body.createdById && req.body.createdById !== req.user?.id) {
      logger.info("Security: createdById in body doesn't match authenticated user");
      req.body.createdById = req.user?.id;
    } else if (!req.body.createdById) {
      logger.info("No createdById in request body, adding it");
      req.body.createdById = req.user?.id;
    }
    
    const parseResult = insertConversationSchema.safeParse(req.body);
    if (!parseResult.success) {
      logger.info("Validation error:", parseResult.error.format());
      return res.status(400).json({ errors: parseResult.error.format() });
    }
    
    const conversationData = parseResult.data;
    logger.info("Validated conversation data:", conversationData);
    
    // Create the conversation
    const conversation = await storage.createConversation(conversationData);
    logger.info("Created conversation:", conversation);
    
    // Add the creator as a participant
    await storage.addParticipantToConversation(conversation.id, req.user?.id!);
    
    // Add all other participants
    if (req.body.participantIds && Array.isArray(req.body.participantIds)) {
      for (const participantId of req.body.participantIds) {
        if (participantId !== req.user?.id) {
          await storage.addParticipantToConversation(conversation.id, participantId);
        }
      }
    }
    
    return res.status(201).json(conversation);
  } catch (error) {
    logger.error("Error creating conversation:", error);
    return res.status(500).json({ message: "Failed to create conversation" });
  }
}

export async function addParticipant(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const conversationId = parseInt(req.params.id);
    if (isNaN(conversationId)) {
      return res.status(400).json({ message: "Invalid conversation ID" });
    }
    
    const { userId } = req.body;
    if (!userId) {
      return res.status(400).json({ message: "User ID is required" });
    }
    
    // Verify conversation exists
    const conversation = await storage.getConversation(conversationId);
    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }
    
    // Verify current user has permission (creator or admin)
    if (conversation.createdById !== req.user?.id && req.user?.role !== 'admin') {
      return res.status(403).json({ message: "You don't have permission to add participants" });
    }
    
    // Verify the user to add exists
    const userToAdd = await storage.getUser(userId);
    if (!userToAdd) {
      return res.status(404).json({ message: "User not found" });
    }
    
    // Add the participant
    const participant = await storage.addParticipantToConversation(conversationId, userId);
    
    // Create notification for the added user
    await storage.createNotification({
      userId,
      type: 'message',
      title: 'New Conversation',
      content: `You were added to a conversation: ${conversation.title}`,
      sourceId: conversation.id.toString(),
      sourceType: 'conversation',
      status: 'unread',
    });
    
    return res.status(200).json(participant);
  } catch (error) {
    logger.error("Error adding participant:", error);
    return res.status(500).json({ message: "Failed to add participant" });
  }
}

export async function removeParticipant(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const conversationId = parseInt(req.params.conversationId);
    const userId = parseInt(req.params.userId);
    if (isNaN(conversationId) || isNaN(userId)) {
      return res.status(400).json({ message: "Invalid IDs" });
    }
    
    // Verify conversation exists
    const conversation = await storage.getConversation(conversationId);
    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }
    
    // Verify current user has permission (creator, self-removal, or admin)
    if (
      conversation.createdById !== req.user?.id && 
      userId !== req.user?.id && 
      req.user?.role !== 'admin'
    ) {
      return res.status(403).json({ message: "You don't have permission to remove this participant" });
    }
    
    const result = await storage.removeParticipantFromConversation(conversationId, userId);
    return res.status(200).json({ success: result });
  } catch (error) {
    logger.error("Error removing participant:", error);
    return res.status(500).json({ message: "Failed to remove participant" });
  }
}

// Message handlers
export async function sendMessage(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    logger.info("Sending message with body:", req.body);
    logger.info("Current authenticated user:", req.user?.id);
    
    // Create a new object with the correct structure
    const messageData = {
      conversationId: req.body.conversationId,
      senderId: req.user?.id, // Always use the authenticated user's ID
      content: req.body.content,
      attachmentId: req.body.attachmentId || null
    };
    
    logger.info("Prepared message data:", messageData);
    
    // Verify conversation exists
    const conversation = await storage.getConversation(messageData.conversationId);
    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }
    
    // Verify user is a participant
    const conversationWithParticipants = await storage.getConversationWithParticipants(messageData.conversationId);
    const isParticipant = conversationWithParticipants?.participants.some(p => p.id === req.user?.id);
    if (!isParticipant && req.user?.role !== 'admin') {
      return res.status(403).json({ message: "You are not a participant in this conversation" });
    }
    
    // Create the message
    const message = await storage.createMessage(messageData);
    logger.info("Created message:", message);
    
    // Get the sender (current user) to include with the response
    const sender = await storage.getUser(req.user?.id!);
    logger.info("Sender info to attach to message:", sender);
    
    // Create notifications for all other participants
    const otherParticipants = conversationWithParticipants?.participants.filter(p => p.id !== req.user?.id) || [];
    for (const participant of otherParticipants) {
      await storage.createNotification({
        userId: participant.id,
        type: 'message',
        title: `Message from ${sender?.fullName || 'Unknown User'}`,
        content: message.content.length > 40 ? `${message.content.substring(0, 40)}...` : message.content,
        sourceId: conversation.id.toString(),
        sourceType: 'conversation',
        status: 'unread',
      });
    }
    
    // Prepare the response data with sender information
    const responseData = {
      ...message,
      sender
    };
    logger.info("Sending message response with sender:", responseData);
    
    // Return the message with sender information
    return res.status(201).json(responseData);
  } catch (error) {
    logger.error("Error sending message:", error);
    return res.status(500).json({ message: "Failed to send message" });
  }
}

export async function editMessage(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const messageId = parseInt(req.params.id);
    if (isNaN(messageId)) {
      return res.status(400).json({ message: "Invalid message ID" });
    }
    
    const { content } = req.body;
    if (!content) {
      return res.status(400).json({ message: "Content is required" });
    }
    
    // Verify message exists
    const message = await storage.getMessage(messageId);
    if (!message) {
      return res.status(404).json({ message: "Message not found" });
    }
    
    // Verify user is the sender or admin
    if (message.senderId !== req.user?.id && req.user?.role !== 'admin') {
      return res.status(403).json({ message: "You can only edit your own messages" });
    }
    
    // Edit the message
    const updatedMessage = await storage.updateMessage(messageId, { content });
    return res.status(200).json(updatedMessage);
  } catch (error) {
    logger.error("Error editing message:", error);
    return res.status(500).json({ message: "Failed to edit message" });
  }
}

// Notification handlers
export async function getNotifications(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const userId = req.user?.id!;
    const limit = parseInt(req.query.limit as string) || 20;
    // By default, include read notifications
    const includeRead = req.query.includeRead !== 'false';
    
    logger.info(`Getting notifications for user ${userId}, includeRead=${includeRead}, limit=${limit}`);
    
    const notifications = await storage.getNotificationsByUser(userId, limit, includeRead);
    logger.info(`Returning ${notifications.length} notifications to client`);
    
    return res.status(200).json(notifications);
  } catch (error) {
    logger.error("Error fetching notifications:", error);
    return res.status(500).json({ message: "Failed to fetch notifications" });
  }
}

export async function getUnreadNotificationCount(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const userId = req.user?.id!;
    
    // Get total unread count from database
    const count = await storage.getUnreadNotificationCount(userId);
    logger.info(`Unread notification count for user ${userId}:`, count);
    
    // Removed: No longer auto-create notifications when count is 0
    // This was causing duplication issues when marking notifications as read
    
    return res.status(200).json({ count });
  } catch (error) {
    logger.error("Error fetching notification count:", error);
    return res.status(500).json({ message: "Failed to fetch notification count" });
  }
}

export async function markNotificationAsRead(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const notificationId = parseInt(req.params.id);
    if (isNaN(notificationId)) {
      return res.status(400).json({ message: "Invalid notification ID" });
    }
    
    // Verify notification exists and belongs to user
    const notification = await storage.getNotification(notificationId);
    if (!notification) {
      return res.status(404).json({ message: "Notification not found" });
    }
    
    if (notification.userId !== req.user?.id && req.user?.role !== 'admin') {
      return res.status(403).json({ message: "You don't have permission to mark this notification" });
    }
    
    const result = await storage.markNotificationAsRead(notificationId);
    return res.status(200).json({ success: result });
  } catch (error) {
    logger.error("Error marking notification as read:", error);
    return res.status(500).json({ message: "Failed to mark notification as read" });
  }
}

export async function markAllNotificationsAsRead(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const userId = req.user?.id!;
    const result = await storage.markAllNotificationsAsRead(userId);
    return res.status(200).json({ success: result });
  } catch (error) {
    logger.error("Error marking all notifications as read:", error);
    return res.status(500).json({ message: "Failed to mark all notifications as read" });
  }
}

export async function getNotificationPreferences(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const userId = req.user?.id!;
    const preferences = await storage.getNotificationPreferences(userId);
    
    // If no preferences, return defaults
    if (!preferences) {
      const defaultPreferences = {
        email: {
          assessment: true,
          message: true,
          form: true,
          system: true,
          payment: true,
          report: true
        },
        inApp: {
          assessment: true,
          message: true,
          form: true,
          system: true,
          payment: true,
          report: true
        }
      };
      return res.status(200).json(defaultPreferences);
    }
    
    return res.status(200).json(preferences.preferences);
  } catch (error) {
    logger.error("Error fetching notification preferences:", error);
    return res.status(500).json({ message: "Failed to fetch notification preferences" });
  }
}

export async function updateNotificationPreferences(req: Request, res: Response) {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Authentication required" });
    }
    
    const userId = req.user?.id!;
    const { preferences } = req.body;
    
    if (!preferences) {
      return res.status(400).json({ message: "Preferences are required" });
    }
    
    const updatedPreferences = await storage.createOrUpdateNotificationPreferences(userId, preferences);
    return res.status(200).json(updatedPreferences);
  } catch (error) {
    logger.error("Error updating notification preferences:", error);
    return res.status(500).json({ message: "Failed to update notification preferences" });
  }
}