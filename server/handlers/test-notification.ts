import { logger } from '../logger';
/**
 * Test handler for creating notifications
 * This is for testing purposes only and should not be accessible in production
 */
import { Request, Response } from 'express';
import { storage } from '../storage';

export async function sendTestNotification(req: Request, res: Response) {
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({ message: 'Test endpoints are disabled in production' });
  }

  try {
    // Get admin users
    const adminUsers = await storage.listUsers('admin');
    
    if (adminUsers.length === 0) {
      return res.status(404).json({ message: 'No admin users found' });
    }
    
    // Check what type of test notification to send
    const notificationType = req.query.type || 'default';
    
    // Create a test notification for each admin
    const notifications = [];
    for (const admin of adminUsers) {
      let notification;
      
      if (notificationType === 'referral') {
        // Create a referral notification
        notification = await storage.createNotification({
          userId: admin.id,
          type: 'assessment',
          title: 'New University Referral',
          content: 'New university referral from Oxford University for <PERSON> requires verification.',
          status: 'unread',
          url: '/admin/verify-referral/999',
          sourceId: '999',
          sourceType: 'assessment',
          data: {
            assessmentId: 999,
            referralType: 'university',
            university: 'Oxford University',
            studentName: 'John Smith'
          }
        });
      } else {
        // Default test notification
        notification = await storage.createNotification({
          userId: admin.id,
          type: 'assessment',
          title: 'Test Notification',
          content: 'This is a test notification for the admin notification system.',
          status: 'unread',
          url: '/admin/dashboard',
          sourceId: '0',
          sourceType: 'test',
          data: {
            testId: Date.now(),
            message: 'Test notification created by the test endpoint'
          }
        });
      }
      
      notifications.push({
        adminId: admin.id,
        adminEmail: admin.email,
        notificationId: notification.id
      });
      
      logger.info(`Created ${notificationType} test notification for admin ${admin.email} (ID: ${admin.id})`);
    }
    
    res.status(201).json({
      message: `Created ${notifications.length} test notifications (type: ${notificationType})`,
      notifications
    });
  } catch (error) {
    logger.error('Error creating test notifications:', error);
    res.status(500).json({ message: 'Error creating test notifications' });
  }
}