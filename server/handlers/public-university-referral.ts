import { logger } from '../logger';
import { Request, Response } from "express";
import { storage } from "../storage";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { verifyRecaptchaV3 } from '../utils/recaptcha-v3-verification';

// Define the public university referral schema
const publicUniversityReferSchema = z.object({
  // University Information
  universityName: z.string().min(1, "University name is required"),
  universityEmail: z.string().email("Please enter a valid university email address"),
  universityPhone: z.string().min(1, "University phone number is required"),
  department: z.string().min(1, "Department is required"),
  staffName: z.string().min(1, "Staff member name is required"),
  staffPosition: z.string().min(1, "Staff position is required"),

  // Assessee Information
  assesseeFullName: z.string().min(1, "<PERSON><PERSON><PERSON>'s name is required"),
  assesseeEmail: z.string().email("Please enter a valid email address"),
  assesseePhone: z.string().min(1, "<PERSON><PERSON><PERSON>'s phone number is required"),
  dateOfBirth: z.string()
    .min(1, "Date of birth is required")
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear() - 
                 (today.getMonth() < birthDate.getMonth() || 
                 (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate()) ? 1 : 0);
      return age >= 16;
    }, "Student must be at least 16 years old for university referrals")
    .transform((date) => new Date(date)),
  assesseeCourse: z.string().min(1, "Student's course is required"),
  assesseeYear: z.string().min(1, "Year of study is required"),
  reasonForReferral: z.string().min(1, "Reason for assessment is required"),
  assessmentConcerns: z.array(z.string()).optional(),
  previousAssessment: z.enum(["yes", "no"]),
  previousAssessmentDetails: z.string().optional(),
  additionalNotes: z.string().optional(),
});

/**
 * Handle a public university referral submission.
 * This function creates various records in the database to track the referral:
 * 1. Creates temporary user record for the university staff if they don't exist
 * 2. Creates an assessee record for the student
 * 3. Creates an assessment record
 * 4. Generates a tracking ID for the referral
 */
export async function handlePublicUniversityReferral(req: Request, res: Response) {
  try {
    logger.info('🔍 Processing university referral submission');
    logger.info('🔍 Request body keys:', Object.keys(req.body));
    
    // Validate request body
    const validationResult = publicUniversityReferSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: "Invalid referral data", 
        errors: validationResult.error.errors 
      });
    }
    
    const referralData = validationResult.data;
    
    // Debug: Log the previousAssessmentDetails field specifically
    logger.info('🔍 Previous assessment data:', {
      previousAssessment: referralData.previousAssessment,
      previousAssessmentDetails: referralData.previousAssessmentDetails,
      hasDetails: !!referralData.previousAssessmentDetails,
      detailsLength: referralData.previousAssessmentDetails?.length || 0
    });
    
    // Extract domain from university email to help identify the university
    const emailDomain = referralData.universityEmail.split('@')[1];
    
    // In this phase, we don't have the universities table yet, so we'll temporarily store this info
    // in the user organization field
    
    // Check if this staff member already exists
    const staffEmail = referralData.universityEmail;
    let referringUser = await storage.getUserByEmail(staffEmail);
    let referringUserId = null;
    
    // If staff doesn't exist, create a temporary user record
    if (!referringUser) {
      referringUser = await storage.createUser({
        username: staffEmail,
        email: staffEmail,
        password: uuidv4(), // Generate a random password - they'll need to set a real one later
        fullName: referralData.staffName,
        role: 'university',
        phone: referralData.universityPhone,
        organization: referralData.universityName,
        position: referralData.staffPosition, // Store the staff position
        department: referralData.department  // Store department directly in user record
      });
      referringUserId = referringUser.id;
      
      logger.info(`Created new university staff user: ${referringUser.fullName} with ID ${referringUser.id}`);
    } else {
      referringUserId = referringUser.id;
      logger.info(`Found existing university staff user: ${referringUser.fullName} with ID ${referringUser.id}`);
    }
    
    // Create an assessee record
    const assessee = await storage.createAssessee({
      fullName: referralData.assesseeFullName,
      dateOfBirth: referralData.dateOfBirth,
      email: referralData.assesseeEmail,
      phone: referralData.assesseePhone,
      address: null,
      courseProgram: referralData.assesseeCourse, // Save course/program information
      yearOfStudy: referralData.assesseeYear,     // Save year of study information
      schoolId: null,
      parentId: null,
      userId: null // We'll set this after using our utility function
    });
    
    logger.info(`Created assessee record for student: ${assessee.fullName} with ID ${assessee.id}`);
    
    // Import and use the utility to create a user account if over 16
    const { createUserForAssesseeIfOver16 } = await import('../utils/assessee-user-creation');
    // Pass the request object to ensure correct URL generation in emails
    const userId = await createUserForAssesseeIfOver16(assessee.id, req);
    
    // Create an assessment record with pending verification status
    let assessment;
    try {
      assessment = await storage.createAssessment({
        assesseeId: assessee.id,
        referringUserId: referringUserId,
        referralType: 'university',
        status: 'VerificationPending',
        paymentStatus: 'unpaid',
        notes: null
      });
    } catch (err) {
      // Fallback for environments where the VerificationPending enum value does not exist yet
      logger.warn('Falling back to Enquiry status due to enum mismatch');
      assessment = await storage.createAssessment({
        assesseeId: assessee.id,
        referringUserId: referringUserId,
        referralType: 'university',
        status: 'Enquiry',
        paymentStatus: 'unpaid',
        notes: null
      });
    }
    
    logger.info(`Created assessment record with ID ${assessment.id}`);
    
    // Generate a tracking ID
    const trackingId = `UNI-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    // Store referral details in dedicated table
    await storage.createReferral({
      trackingId,
      referralType: 'university',
      status: 'submitted',
      referringUserId,
      assesseeId: assessee.id,
      assessmentId: assessment.id,
      schoolName: referralData.universityName,
      schoolEmail: referralData.universityEmail,
      schoolPhone: referralData.universityPhone,
      department: referralData.department,
      staffName: referralData.staffName,
      staffPosition: referralData.staffPosition,
      parentName: null,
      parentEmail: null,
      parentPhone: null,
      relationship: null,
      assesseeFullName: referralData.assesseeFullName,
      assesseeEmail: referralData.assesseeEmail,
      assesseePhone: referralData.assesseePhone,
      dateOfBirth: referralData.dateOfBirth,
      assesseeYear: referralData.assesseeYear,
      assesseeCourse: referralData.assesseeCourse,
      assessmentConcerns: referralData.assessmentConcerns
        ? JSON.stringify(referralData.assessmentConcerns)
        : null,
      reasonForReferral: referralData.reasonForReferral,
      previousAssessment: referralData.previousAssessment,
      previousAssessmentDetails: referralData.previousAssessmentDetails || null,
      additionalNotes: referralData.additionalNotes || null
    });
    
    // Create activity log
    await storage.createActivity({
      assessmentId: assessment.id,
      userId: referringUserId,
      action: 'university_referral_created',
      details: `Referral created by ${referralData.staffName} (${referralData.staffPosition}) from ${referralData.universityName}, Department: ${referralData.department}`
    });
    
    // Send notification to all admin users about the new referral
    try {
      // Get all admin users
      const adminUsers = await storage.listUsers('admin');
      
      // Create a notification for each admin
      for (const admin of adminUsers) {
        await storage.createNotification({
          userId: admin.id,
          type: 'assessment',
          title: 'New University Referral',
          content: `New university referral from ${referralData.universityName} for ${referralData.assesseeFullName} (Ref: ${trackingId})`,
          status: 'unread',
          url: `/admin/verify-referral/${assessment.id}`,
          sourceId: assessment.id.toString(),
          sourceType: 'assessment',
          data: {
            assessmentId: assessment.id,
            trackingId: trackingId,
            referralType: 'university',
            university: referralData.universityName,
            studentName: referralData.assesseeFullName
          }
        });
        
        logger.info(`Sent referral notification to admin: ${admin.email} (ID: ${admin.id})`);
      }
    } catch (notifyError) {
      // Log error but don't block the creation process
      logger.error('Error sending admin notifications for new university referral:', notifyError);
    }

    // Return success response with tracking ID
    res.status(201).json({
      message: "Referral submitted successfully",
      trackingId,
      assessmentId: assessment.id
    });
    
  } catch (error) {
    logger.error("Error processing university referral:", error);
    
    // Send appropriate error response
    if (error instanceof Error) {
      res.status(500).json({ message: `Error processing referral: ${error.message}` });
    } else {
      res.status(500).json({ message: "An unknown error occurred while processing your referral" });
    }
  }
}