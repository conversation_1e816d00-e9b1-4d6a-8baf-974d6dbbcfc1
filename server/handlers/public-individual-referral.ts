import { logger } from '../logger';
import { Request, Response } from "express";
import { storage } from "../storage";
import { User } from "@shared/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";

// Define the public individual referral schema
const publicIndividualReferSchema = z.object({
  // School Information
  schoolName: z.string().optional(),
  schoolEmail: z.preprocess(
    (val) => (val === '' ? undefined : val),
    z.string().email("Please enter a valid school email address").optional()
  ),
  schoolPhone: z.string().optional(),
  department: z.string().optional(),
  staffName: z.string().optional(),
  staffPosition: z.string().optional(),

  // Parent/Guardian Information
  parentName: z.string().optional(),
  parentEmail: z.preprocess(
    (val) => (val === '' ? undefined : val),
    z.string().email("Please enter a valid email address").optional()
  ),
  parentPhone: z.string().optional(),
  relationshipWithAssessee: z.string().optional(),

  // Assessee Information
  assesseeFullName: z.string().min(1, "<PERSON><PERSON><PERSON>'s name is required"),
  assesseeEmail: z.preprocess(
    (val) => (val === '' ? undefined : val),
    z.string().email("Please enter a valid email address").optional()
  ),
  assesseePhone: z.string().optional(),
  dateOfBirth: z.string()
    .min(1, "Date of birth is required")
    .transform((date) => new Date(date)),
  studentYear: z.string().optional(),
  reasonForReferral: z.string().min(1, "Reason for assessment is required"),
  assessmentConcerns: z.array(z.string()).optional(),
  previousAssessment: z.enum(["yes", "no"]),
  previousAssessmentDetails: z.string().optional(),
  additionalNotes: z.string().optional(),
  schoolContactConsent: z.boolean().optional(),
}).superRefine((data, ctx) => {
  const ageDiff = Date.now() - data.dateOfBirth.getTime();
  const age = Math.floor(ageDiff / (1000 * 60 * 60 * 24 * 365.25));
  if (age < 16) {
    if (!data.parentName) {
      ctx.addIssue({ code: 'custom', message: 'Parent/guardian name is required', path: ['parentName'] });
    }
    if (!data.parentEmail) {
      ctx.addIssue({ code: 'custom', message: 'Parent/guardian email is required', path: ['parentEmail'] });
    }
    if (!data.parentPhone) {
      ctx.addIssue({ code: 'custom', message: 'Parent/guardian phone number is required', path: ['parentPhone'] });
    }
    if (!data.relationshipWithAssessee) {
      ctx.addIssue({ code: 'custom', message: 'Relationship with assessee is required', path: ['relationshipWithAssessee'] });
    }
    if (!data.schoolName) {
      ctx.addIssue({ code: 'custom', message: 'School name is required', path: ['schoolName'] });
    }
    if (!data.schoolEmail) {
      ctx.addIssue({ code: 'custom', message: 'School email is required', path: ['schoolEmail'] });
    }
    if (!data.schoolPhone) {
      ctx.addIssue({ code: 'custom', message: 'School phone number is required', path: ['schoolPhone'] });
    }
    if (!data.department) {
      ctx.addIssue({ code: 'custom', message: 'Department is required', path: ['department'] });
    }
    if (!data.staffName) {
      ctx.addIssue({ code: 'custom', message: 'Staff member name is required', path: ['staffName'] });
    }
    if (!data.staffPosition) {
      ctx.addIssue({ code: 'custom', message: 'Staff position is required', path: ['staffPosition'] });
    }
    if (!data.schoolContactConsent) {
      ctx.addIssue({ code: 'custom', message: 'Consent to contact school is required', path: ['schoolContactConsent'] });
    }
  }
});

/**
 * Handle a public individual referral submission.
 * This function creates various records in the database to track the referral:
 * 1. Creates temporary user record for the school staff if they don't exist
 * 2. Creates a parent user if they don't exist
 * 3. Creates an assessee record
 * 4. Creates an assessment record
 * 5. Generates a tracking ID for the referral
 */
export async function handlePublicIndividualReferral(req: Request, res: Response) {
  try {
    // Validate request body
    const validationResult = publicIndividualReferSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: "Invalid referral data", 
        errors: validationResult.error.errors 
      });
    }
    
    const referralData = validationResult.data;
    
    // Check if the school staff already exists
    let referringUserId = null;
    let referringUser: User | null = null;
    if (referralData.schoolEmail) {
      const staffEmail = referralData.schoolEmail;
      referringUser = await storage.getUserByEmail(staffEmail);

      // If staff doesn't exist, create a temporary user record
      if (!referringUser) {
        referringUser = await storage.createUser({
          username: staffEmail,
          email: staffEmail,
          password: uuidv4(), // Generate a random password - they'll need to set a real one later
          fullName: referralData.staffName || '',
          role: 'school',
          phone: referralData.schoolPhone || null,
          organization: referralData.schoolName || null
        });
        referringUserId = referringUser.id;

        logger.info(`Created new school staff user: ${referringUser.fullName} with ID ${referringUser.id}`);

        // Create a school record linked to this user
        const school = await storage.createSchool({
          name: referralData.schoolName || null,
          address: null,
          contactPerson: referralData.staffName || null,
          contactEmail: referralData.schoolEmail,
          contactPhone: referralData.schoolPhone || null,
          userId: referringUser.id
        });

        logger.info(`Created new school record: ${school.name} with ID ${school.id} for user ${referringUser.id}`);
      } else {
        referringUserId = referringUser.id;
        logger.info(`Found existing school staff user: ${referringUser.fullName} with ID ${referringUser.id}`);

        // Find if there's already a school associated with this user
        const schools = await storage.listSchools();
        const existingSchool = schools.find(s => s.userId === referringUser.id);

        // If no school exists for this user, create one
        if (!existingSchool) {
          const school = await storage.createSchool({
            name: referralData.schoolName || null,
            address: null,
            contactPerson: referralData.staffName || null,
            contactEmail: referralData.schoolEmail,
            contactPhone: referralData.schoolPhone || null,
            userId: referringUser.id
          });

          logger.info(`Created new school record: ${school.name} with ID ${school.id} for existing user ${referringUser.id}`);
        } else {
          logger.info(`Found existing school record: ${existingSchool.name} with ID ${existingSchool.id} for user ${referringUser.id}`);
        }
      }
    }
    
    // Check if parent already exists
    let parentId = null;
    if (referralData.parentEmail) {
      const parentEmail = referralData.parentEmail;
      let parentUser = await storage.getUserByEmail(parentEmail);

      // If parent doesn't exist, create a user record
      if (!parentUser) {
        parentUser = await storage.createUser({
          username: parentEmail,
          email: parentEmail,
          password: uuidv4(), // Generate a random password - they'll need to set a real one later
          fullName: referralData.parentName || '',
          role: 'parent',
          phone: referralData.parentPhone || null,
          organization: null
        });
        parentId = parentUser.id;

        logger.info(`Created new parent user: ${parentUser.fullName} with ID ${parentUser.id}`);
      } else {
        parentId = parentUser.id;
        logger.info(`Found existing parent user: ${parentUser.fullName} with ID ${parentUser.id}`);
      }
    }
    
    // We'll use our utility function to create user accounts for assessees over 16
    // The utility can determine age and only create accounts when appropriate
    
    // Get the school ID to associate with the assessee
    let schoolId = null;
    const schools = await storage.listSchools();
    
    // referringUser should exist at this point, but add a safety check
    if (referringUser && referringUser.id) {
      const schoolRecord = schools.find(s => s.userId === referringUser.id);
      
      if (schoolRecord) {
        schoolId = schoolRecord.id;
        logger.info(`Found school record with ID ${schoolId} to associate with assessee`);
      } else {
        logger.info('No school record found to associate with assessee');
      }
    } else {
      logger.info('No referring user found to look up school record');
    }
    
    // Create an assessee record for the assessee
    const assessee = await storage.createAssessee({
      fullName: referralData.assesseeFullName,
      dateOfBirth: referralData.dateOfBirth,
      email: referralData.assesseeEmail || null,
      phone: referralData.assesseePhone || null,
      address: null,
      schoolId, // Link the school ID
      parentId: parentId,
      userId: null // We'll set this after using our utility function
    });
    
    logger.info(`Created assessee record: ${assessee.fullName} with ID ${assessee.id}`);
    
    // Import and use the utility to create a user account if over 16
    const { createUserForAssesseeIfOver16 } = await import('../utils/assessee-user-creation');
    // Pass the request object to ensure correct URL generation in emails
    await createUserForAssesseeIfOver16(assessee.id, req);
    
    // Create an assessment record with pending verification status
    let assessment;
    try {
      assessment = await storage.createAssessment({
        assesseeId: assessee.id,
        referringUserId: referringUserId,
        referralType: 'individual',
        status: 'VerificationPending',
        paymentStatus: 'unpaid',
        notes: null
      });
    } catch (err) {
      logger.warn('Falling back to Enquiry status due to enum mismatch');
      assessment = await storage.createAssessment({
        assesseeId: assessee.id,
        referringUserId: referringUserId,
        referralType: 'individual',
        status: 'Enquiry',
        paymentStatus: 'unpaid',
        notes: null
      });
    }
    
    logger.info(`Created assessment record with ID ${assessment.id}`);
    
    // Generate a tracking ID
    const trackingId = `IND-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    // Store referral details in dedicated table
    await storage.createReferral({
      trackingId,
      referralType: 'individual',
      status: 'submitted',
      referringUserId,
      assesseeId: assessee.id,
      assessmentId: assessment.id,
      schoolName: referralData.schoolName || null,
      schoolEmail: referralData.schoolEmail || null,
      schoolPhone: referralData.schoolPhone || null,
      department: referralData.department || null,
      staffName: referralData.staffName || null,
      staffPosition: referralData.staffPosition || null,
      parentName: referralData.parentName || null,
      parentEmail: referralData.parentEmail || null,
      parentPhone: referralData.parentPhone || null,
      relationship: referralData.relationshipWithAssessee || null,
      assesseeFullName: referralData.assesseeFullName,
      assesseeEmail: referralData.assesseeEmail || null,
      assesseePhone: referralData.assesseePhone || null,
      dateOfBirth: referralData.dateOfBirth,
      assesseeYear: referralData.studentYear || null,
      assessmentConcerns: referralData.assessmentConcerns
        ? JSON.stringify(referralData.assessmentConcerns)
        : null,
      studentCourse: null,
      reasonForReferral: referralData.reasonForReferral,
      previousAssessment: referralData.previousAssessment,
      previousAssessmentDetails: referralData.previousAssessmentDetails || null,
      additionalNotes: referralData.additionalNotes || null,
      schoolContactConsent: referralData.schoolContactConsent ?? false
    });

    // Create activity log
    await storage.createActivity({
      assessmentId: assessment.id,
      userId: referringUserId,
      action: 'individual_referral_created',
      details: `Individual referral created by ${referralData.staffName} (${referralData.staffPosition}) from ${referralData.schoolName}, Department: ${referralData.department}. Parent/Guardian: ${referralData.parentName}`
    });
    
    // Send notification to all admin users about the new referral
    try {
      // Get all admin users
      const adminUsers = await storage.listUsers('admin');
      
      // Create a notification for each admin
      for (const admin of adminUsers) {
        await storage.createNotification({
          userId: admin.id,
          type: 'assessment',
          title: 'New Individual Referral',
          content: `New individual referral from ${referralData.schoolName} for ${referralData.assesseeFullName} requires verification.`,
          status: 'unread',
          url: `/admin/verify-referral/${assessment.id}`,
          sourceId: assessment.id.toString(),
          sourceType: 'assessment',
          data: {
            assessmentId: assessment.id,
            referralType: 'individual',
            school: referralData.schoolName,
            studentName: referralData.assesseeFullName
          }
        });
        
        logger.info(`Sent referral notification to admin: ${admin.email} (ID: ${admin.id})`);
      }
    } catch (notifyError) {
      // Log error but don't block the creation process
      logger.error('Error sending admin notifications for new individual referral:', notifyError);
    }

    // Return success response with tracking ID
    res.status(201).json({
      message: "Referral submitted successfully",
      trackingId,
      assessmentId: assessment.id
    });
    
  } catch (error) {
    logger.error("Error processing individual referral:", error);
    
    // Send appropriate error response
    if (error instanceof Error) {
      res.status(500).json({ message: `Error processing referral: ${error.message}` });
    } else {
      res.status(500).json({ message: "An unknown error occurred while processing your referral" });
    }
  }
}