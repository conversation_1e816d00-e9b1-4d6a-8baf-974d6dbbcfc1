import { logger } from '../logger';
import { Request, Response } from "express";
import { db } from "../db";
import { formResponses, forms, formQuestions } from "@shared/schema";
import { v4 as uuidv4 } from "uuid";

/**
 * Handle the test form submission and save the data to the database
 */
export async function handleTestFormSubmission(req: Request, res: Response) {
  try {
    logger.info("Received test form submission:", req.body);

    const { name, age, previousAssessments, learningChallenges, additionalInfo } = req.body;

    if (!name || !age || !learningChallenges) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    // First, check if we have test questions in the database
    let questionIds = [];
    
    // Look for existing test questions
    const existingQuestions = await db.select().from(formQuestions).where(
      (formQuestion) => formQuestion.formType.equals("test")
    );
    
    // If no test questions exist, create them
    if (existingQuestions.length === 0) {
      logger.info("Creating test form questions");
      
      // Create test form questions with correct schema
      const questionsToCreate = [
        {
          formType: "test",
          section: "basic",
          questionKey: "name",
          questionText: "Full Name",
          questionType: "text",
          required: true,
          order: 1,
          options: null
        },
        {
          formType: "test",
          section: "basic",
          questionKey: "age",
          questionText: "Age",
          questionType: "text",
          required: true,
          order: 2,
          options: null
        },
        {
          formType: "test",
          section: "assessment",
          questionKey: "prev_assessments",
          questionText: "Previous SpLD Assessments",
          questionType: "textarea",
          required: false,
          order: 3,
          options: null
        },
        {
          formType: "test",
          section: "challenges",
          questionKey: "learning_challenges",
          questionText: "Learning Challenges",
          questionType: "textarea",
          required: true,
          order: 4,
          options: null
        },
        {
          formType: "test",
          section: "other",
          questionKey: "additional_info",
          questionText: "Additional Information",
          questionType: "textarea",
          required: false,
          order: 5,
          options: null
        }
      ];
      
      // Insert the questions
      const insertedQuestions = await db.insert(formQuestions).values(questionsToCreate).returning();
      questionIds = insertedQuestions.map(q => q.id);
    } else {
      // Use existing question IDs
      questionIds = existingQuestions.map(q => q.id);
    }
    
    // Create a dummy assessment ID or use null with proper typing
    // This is just for testing - in a real app this would be a valid assessment ID
    let assessmentId = null;
    try {
      // Try to get the first assessment from the database
      const [firstAssessment] = await db.select({ id: forms.id }).from(forms).limit(1);
      if (firstAssessment) {
        assessmentId = firstAssessment.id;
      }
    } catch (e) {
      logger.info("No existing assessments found, using null for test form");
    }
    
    // Create a test form with the access token
    const accessToken = uuidv4();
    const [form] = await db.insert(forms).values({
      assessmentId: 43, // Using a valid assessment ID for testing, could be any valid ID
      formType: "test",
      status: "completed",
      accessToken,
      completedAt: new Date()
    }).returning();
    
    logger.info("Created test form with ID:", form.id);
    
    // Map our form responses to the questions
    const formData = {
      name,
      age,
      previousAssessments: previousAssessments || "",
      learningChallenges,
      additionalInfo: additionalInfo || ""
    };
    
    // Array to store our responses
    const responses = [];
    
    // For each question, create a response
    for (let i = 0; i < questionIds.length; i++) {
      const questionId = questionIds[i];
      let responseText = "";
      
      // Map the right value to each question
      switch (i) {
        case 0: responseText = formData.name; break;
        case 1: responseText = formData.age; break;
        case 2: responseText = formData.previousAssessments; break;
        case 3: responseText = formData.learningChallenges; break;
        case 4: responseText = formData.additionalInfo; break;
      }
      
      // Add to our responses array using the correct field name
      responses.push({
        formId: form.id,
        questionId,
        responseText
      });
    }
    
    // Insert all form responses
    const insertedResponses = await db.insert(formResponses).values(responses).returning();
    logger.info(`Inserted ${insertedResponses.length} form responses`);
    
    // Return success
    return res.status(200).json({ 
      success: true, 
      message: "Test form submitted successfully",
      formId: form.id,
      responseCount: insertedResponses.length
    });
    
  } catch (error) {
    logger.error("Error handling test form submission:", error);
    return res.status(500).json({ 
      error: "Failed to process form submission", 
      details: error instanceof Error ? error.message : String(error) 
    });
  }
}