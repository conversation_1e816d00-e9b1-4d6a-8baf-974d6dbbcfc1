import { logger } from '../logger';
import { Request, Response } from "express";
import { storage } from "../storage";

/**
 * Get all assessments regardless of status
 * This is an admin-only endpoint used for the All Referrals view
 */
export async function getAllAssessments(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }

  try {
    // Get all assessments from the database without filtering
    const allAssessments = await storage.listAssessments();
    
    // Enhance the assessments with assessee and referring user details
    const enhancedAssessments = await Promise.all(allAssessments.map(async (assessment) => {
      const assessee = await storage.getAssessee(assessment.assesseeId);
      
      // Provide a fallback assessee object if not found
      const safeAssessee = assessee || {
        id: assessment.assesseeId,
        fullName: 'Unknown Assessee',
        dateOfBirth: '',
        email: null,
        phone: null,
        address: null,
        parentId: null,
        schoolId: null,
        courseProgram: null,
        yearOfStudy: null,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      let referringUser = null;
      if (assessment.referringUserId) {
        const user = await storage.getUser(assessment.referringUserId);
        if (user) {
          const { password, ...userWithoutPassword } = user;
          referringUser = userWithoutPassword;
        }
      }
      
      return {
        ...assessment,
        assessee: safeAssessee,
        referringUser
      };
    }));
    
    // Sort by most recent first
    enhancedAssessments.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
      const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
      return dateB.getTime() - dateA.getTime();
    });
    
    res.json(enhancedAssessments);
  } catch (error) {
    logger.error('Error fetching all assessments:', error);
    res.status(500).json({ message: "Failed to fetch assessments" });
  }
}

/**
 * Reset an assessment's status back to VerificationPending
 * This removes any approval or rejection markers
 */
export async function resetAssessmentStatus(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }

  try {
    const assessmentId = parseInt(req.params.id);
    const assessment = await storage.getAssessment(assessmentId);
    
    if (!assessment) {
      return res.status(404).json({ message: "Assessment not found" });
    }
    
    // Reset status to VerificationPending
    let notes = assessment.notes || '';
    
    // Remove any APPROVED or REJECTED prefixes from the notes
    // This handles both markers at the beginning of notes
    if (notes.startsWith('APPROVED:')) {
      notes = notes.replace(/^APPROVED:\s*/, '');
    } else if (notes.startsWith('REJECTED:')) {
      notes = notes.replace(/^REJECTED:\s*/, '');
    }
    
    // Update the assessment status
    const updatedAssessment = await storage.updateAssessment(assessmentId, {
      status: 'VerificationPending' as 'VerificationPending',
      notes
    });
    
    // Log the activity
    await storage.createActivity({
      userId: req.user.id,
      assessmentId,
      action: 'reset_status',
      details: `Reset assessment status to VerificationPending`
    });
    
    await storage.createAdminActivityLog({
      adminId: req.user.id,
      action: 'reset_assessment',
      targetUserId: assessment.referringUserId || req.user.id,
      details: `Reset assessment status for ID: ${assessmentId}`,
      createdAt: new Date()
    });
    
    res.json(updatedAssessment);
  } catch (error) {
    logger.error('Error resetting assessment status:', error);
    res.status(500).json({ message: "Failed to reset assessment status" });
  }
}