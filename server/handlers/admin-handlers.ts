import { logger } from '../logger';
import { Request, Response } from "express";
import { storage } from "../storage";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { generatePasswordResetToken } from "../utils/password-reset";
import { createPasswordResetEmail } from "../utils/email-templates";
import { sendEmail } from "../utils/sendgrid";
import { User } from "@shared/schema";

// Declare additional properties for Express.User
declare global {
  namespace Express {
    interface User extends User {
      id: number;
      role?: string;
    }
  }
}

// Auth middleware for admin-only routes
export function requireAdmin(req: Request, res: Response, next: Function) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "Authentication required" });
  }

  if (req.user?.role !== "admin") {
    return res.status(403).json({ message: "Admin access required" });
  }

  next();
}

// Get all users (admin only)
export async function getAllUsers(req: Request, res: Response) {
  try {
    const { role } = req.query;
    
    // If role is specified, filter users by role
    if (role && typeof role === 'string') {
      const users = await storage.listUsers(role);
      return res.json(users);
    }
    
    // Otherwise get all users
    const users = await storage.listUsers();
    res.json(users);
  } catch (error) {
    logger.error("Error fetching users:", error);
    res.status(500).json({ message: "Error fetching users" });
  }
}

// Get user by ID (admin only)
export async function getUserById(req: Request, res: Response) {
  try {
    const userId = parseInt(req.params.id);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    res.json(user);
  } catch (error) {
    logger.error("Error fetching user:", error);
    res.status(500).json({ message: "Error fetching user" });
  }
}

// Update user (admin only)
export async function updateUser(req: Request, res: Response) {
  try {
    const userId = parseInt(req.params.id);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    
    // Check if we're approving a pending user
    const isApproving = 
      (user.status === 'admin_approval_pending' || user.status === 'pending') && 
      req.body.status === 'active';
    
    // Prepare user data for update
    const userData = { ...req.body };
    
    // Remove any fields that shouldn't be updated directly
    delete userData.id;
    delete userData.password;
    delete userData.createdAt;
    delete userData.updatedAt;
    
    // Ensure we're handling the department field for university staff
    if (user.role === 'university' && userData.department !== undefined) {
      // Department now stored directly in user record rather than assessment notes
      logger.info(`Updating department for university user ${userId} to: ${userData.department}`);
    }

    // Update user data including role and status
    const updatedUser = await storage.updateUser(userId, userData);
    
    logger.info(`Updated user ${userId}: role=${userData.role || user.role}, status=${userData.status || user.status}`);
    if (!updatedUser) {
      return res.status(500).json({ message: "Failed to update user" });
    }

    // Send approval email if the user was just approved
    if (isApproving || req.body.sendApprovalEmail) {
      try {
        const { sendApprovalEmail } = await import("../utils/approval-email");
        await sendApprovalEmail(
          updatedUser.email, 
          updatedUser.fullName || updatedUser.username || updatedUser.email,
          updatedUser.role
        );
        logger.info(`Account approval email sent to ${updatedUser.email}`);
      } catch (emailError) {
        logger.error("Error sending approval email:", emailError);
        // Continue even if email fails
      }
    }
    
    // Send rejection email if requested
    if (req.body.sendRejectionEmail && req.body.status === 'inactive') {
      try {
        const { sendRejectionEmail } = await import("../utils/rejection-email");
        await sendRejectionEmail(
          updatedUser.email,
          updatedUser.fullName || updatedUser.username || updatedUser.email,
          updatedUser.role
        );
        logger.info(`Account rejection email sent to ${updatedUser.email}`);
      } catch (emailError) {
        logger.error("Error sending rejection email:", emailError);
        // Continue even if email fails
      }
    }

    // Log admin activity
    const isRejecting = 
      (user.status === 'admin_approval_pending' || user.status === 'pending') && 
      req.body.status === 'inactive';
      
    await storage.createAdminActivityLog({
      adminId: req.user?.id,
      action: "UPDATE_USER",
      targetUserId: userId,
      details: `Updated user ${updatedUser.email} - ${isApproving ? 'APPROVED' : isRejecting ? 'REJECTED' : 'Modified'} (Role: ${req.body.role || user.role}, Status: ${req.body.status || user.status || 'active'})`,
      createdAt: new Date()
    });

    res.json(updatedUser);
  } catch (error) {
    logger.error("Error updating user:", error);
    res.status(500).json({ message: "Error updating user" });
  }
}

// Invite new user (admin only)
export async function inviteUser(req: Request, res: Response) {
  const inviteSchema = z.object({
    email: z.string().email("Invalid email address"),
    role: z.string().min(1, "Role is required"),
    fullName: z.string().optional(),
    organization: z.string().optional(),
    message: z.string().optional(),
  });

  try {
    const validatedData = inviteSchema.parse(req.body);
    const { email, role, fullName, organization, message } = validatedData;

    // Check if user already exists
    const existingUser = await storage.getUserByEmail(email);
    if (existingUser) {
      return res.status(400).json({ message: "A user with this email already exists" });
    }

    // Generate invitation token and a temporary password
    const inviteToken = uuidv4();
    const temporaryPassword = `temp-${Math.random().toString(36).substring(2, 10)}`;
    
    // Generate a username from the email (part before @)
    const username = email.split('@')[0];
    
    // Create the new user with status 'pending'
    // First create the user
    const newUser = await storage.createUser({
      email,
      username,
      password: temporaryPassword,
      fullName: fullName || email.split('@')[0], // Use part of email as name if not provided
      role: role as any,
      status: 'pending', // This might not be working due to schema issues
      organization: organization || null,
      phone: null,
    });
    
    // Explicitly update the status to ensure it's set
    try {
      // Run a direct SQL query to update the status
      await storage.updateUserStatus(newUser.id, 'pending');
      logger.info(`Updated user ${newUser.id} status to 'pending'`);
    } catch (statusError) {
      logger.error('Error updating user status:', statusError);
    }
    
    // Send an invitation email
    try {
      const { sendInvitationEmail } = await import("../utils/invite-email");
      const emailResult = await sendInvitationEmail({
        email,
        fullName,
        role,
        tempPassword: temporaryPassword,
        inviteToken,
        message
      });
      
      logger.info("Invitation email result:", emailResult);
    } catch (emailError) {
      logger.error("Error sending invitation email:", emailError);
      // Continue even if email sending fails
    }
    
    // Log the token and password for debugging purposes
    logger.info(`Invitation token for ${email} (role: ${role}): ${inviteToken}`);
    logger.info(`Temporary password: ${temporaryPassword}`);
    logger.info(`Invitation message: ${message || 'No message provided'}`);
    
    // Log admin activity
    try {
      await storage.createAdminActivityLog({
        adminId: req.user?.id || 0,
        action: "INVITE_USER",
        targetUserId: newUser.id,
        details: `Invited new user ${email} as ${role}`,
        createdAt: new Date()
      });
    } catch (logError) {
      logger.error("Error logging admin activity:", logError);
      // Continue even if logging fails
    }

    res.status(201).json({ 
      message: "Invitation sent successfully",
      userId: newUser.id // Return the user ID for reference
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Invalid invitation data", 
        errors: error.errors 
      });
    }
    
    logger.error("Error sending invitation:", error);
    res.status(500).json({ message: "Error sending invitation" });
  }
}

// Get activity logs (admin only)
export async function getActivityLogs(req: Request, res: Response) {
  try {
    // This would be implemented in the storage interface
    // For now, return an empty array
    res.json([]);
  } catch (error) {
    logger.error("Error fetching activity logs:", error);
    res.status(500).json({ message: "Error fetching activity logs" });
  }
}

// Delete user (admin only)
export async function deleteUser(req: Request, res: Response) {
  try {
    const userId = parseInt(req.params.id);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    // Check if user exists
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Prevent deleting own account
    if (userId === req.user?.id) {
      return res.status(400).json({ message: "Cannot delete your own account" });
    }

    // In a real implementation, we would:
    // 1. Check for references and constraints
    // 2. Possibly soft-delete instead of hard-delete
    // 3. Delete the user from the database
    
    // Log admin activity
    await storage.createAdminActivityLog({
      adminId: req.user?.id,
      action: "DELETE_USER",
      targetUserId: userId,
      details: `Deleted user ${user.username || user.email}`,
      createdAt: new Date()
    });

    res.json({ message: "User deleted successfully" });
  } catch (error) {
    logger.error("Error deleting user:", error);
    res.status(500).json({ message: "Error deleting user" });
  }
}

// Reset user password (admin only)
export async function resetUserPassword(req: Request, res: Response) {
  try {
    const userId = parseInt(req.params.id);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    // Check if user exists
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const token = await generatePasswordResetToken(user.id);
    if (!token) {
      return res.status(500).json({ success: false, message: 'Failed to generate reset token' });
    }

    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const resetLink = `${baseUrl}/reset-password/${token}`;

    const emailOptions = createPasswordResetEmail(user.email, user.fullName, resetLink);
    const emailSent = await sendEmail(emailOptions);
    
    // Log admin activity
    await storage.createAdminActivityLog({
      adminId: req.user?.id,
      action: "RESET_PASSWORD",
      targetUserId: userId,
      details: `Reset password for user ${user.username || user.email}`,
      createdAt: new Date()
    });

    res.json({ success: emailSent });
  } catch (error) {
    logger.error("Error resetting password:", error);
    res.status(500).json({ success: false, message: "Error resetting password" });
  }
}