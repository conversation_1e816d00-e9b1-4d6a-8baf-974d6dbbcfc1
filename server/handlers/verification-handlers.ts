import { logger } from '../logger';
import { Request, Response } from "express";
import { storage } from "../storage";
import { requireAdmin } from "./admin-handlers";
import { z } from "zod";
import { Referral } from "@shared/schema";

/**
 * Get all assessments pending verification
 */
export async function getPendingVerifications(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }

  try {
    // Retrieve all assessments and filter for pending statuses. This covers
    // deployments where the new "VerificationPending" enum value might not yet
    // exist and referrals are stored with the legacy "Enquiry" status.
    const allAssessments = await storage.listAssessments();
    const allPendingAssessments = allAssessments.filter(a =>
      a.status === 'VerificationPending' || a.status === 'Enquiry'
    );
    logger.info(
      `Found ${allPendingAssessments.length} assessments pending verification`
    );
    
    // Filter out the ones that have been rejected or approved
    const pendingAssessments = allPendingAssessments.filter(assessment =>
      !(assessment.notes && assessment.notes.startsWith('REJECTED:')) && 
      !(assessment.notes && assessment.notes.startsWith('APPROVED:'))
    );
    
    logger.info(`After filtering rejected/approved, ${pendingAssessments.length} assessments remain pending verification`);
    
    // Enhance the assessments with assessee and referring user details
    const enhancedAssessments = await Promise.all(pendingAssessments.map(async (assessment) => {
      try {
        const assessee = await storage.getAssessee(assessment.assesseeId);
        
        let referringUser = null;
        if (assessment.referringUserId) {
          const user = await storage.getUser(assessment.referringUserId);
          if (user) {
            const { password, ...userWithoutPassword } = user;
            referringUser = userWithoutPassword;
          }
        }
        
        return {
          ...assessment,
          assessee,
          referringUser
        };
      } catch (enhanceError) {
        logger.error(`Error enhancing assessment ${assessment.id}:`, enhanceError);
        logger.error(`Assessment details: assesseeId=${assessment.assesseeId}, referringUserId=${assessment.referringUserId}`);
        // Return the assessment without enhancement rather than failing the whole request
        return assessment;
      }
    }));
    
    res.json(enhancedAssessments);
  } catch (error) {
    logger.error('Error fetching pending verifications:', error);
    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';
    console.error('Detailed error message:', errorMessage);
    console.error('Error stack:', errorStack);
    res.status(500).json({ message: "Failed to fetch pending verifications", details: errorMessage });
  }
}

/**
 * Approve a referral that's pending verification
 */
export async function approveReferral(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }

  try {
    const assessmentId = parseInt(req.params.id);
    const assessment = await storage.getAssessment(assessmentId);
    
    if (!assessment) {
      return res.status(404).json({ message: "Assessment not found" });
    }
    
    // Check if assessment is pending verification (either VerificationPending or legacy Enquiry status)
    if (assessment.status !== 'VerificationPending' && assessment.status !== 'Enquiry') {
      return res.status(400).json({ message: "This assessment is not pending verification" });
    }
    
    // Additional check: don't allow approval if already approved or rejected
    if (assessment.notes && (assessment.notes.startsWith('APPROVED:') || assessment.notes.startsWith('REJECTED:'))) {
      return res.status(400).json({ message: "This assessment has already been processed" });
    }

    // Change the status to pre_assessment which is the next step after verification
    const updatedAssessment = await storage.updateAssessment(assessmentId, { 
      status: 'pre_assessment' as 'pre_assessment',
      notes: `APPROVED: ${assessment.notes || ''}`
    });
    
    // Log the activity
    await storage.createActivity({
      userId: req.user.id,
      assessmentId,
      action: 'approve_referral',
      details: `Approved referral and moved to Pre-Assessment phase`
    });
    
    await storage.createAdminActivityLog({
      adminId: req.user.id,
      action: 'approve_referral',
      targetUserId: assessment.referringUserId || req.user.id,
      details: `Approved referral ID: ${assessmentId}`,
      createdAt: new Date()
    });
    
    // Send email notifications and create required forms
    try {
      // Get referring user and assessee details
      const expandedAssessment = await storage.getAssessment(assessmentId);
      if (!expandedAssessment) {
        throw new Error('Assessment data not found');
      }
      
      // Get additional details about the assessee
      const assessee = await storage.getAssessee(expandedAssessment.assesseeId);
      if (!assessee) {
        throw new Error('Assessee data not found');
      }
      
      // Get referring details based on referral type
      let referringUser = null;
      let referringSchool = null;
      
      // Check for referring user (individual or university referral)
      if (expandedAssessment.referringUserId) {
        referringUser = await storage.getUser(expandedAssessment.referringUserId);
      }
      
      // For school referrals, we need to get the school data
      if (expandedAssessment.referralType === 'school' && assessee.schoolId) {
        referringSchool = await storage.getSchool(assessee.schoolId);
        logger.info('School referral found:', referringSchool?.name);
      }
      
      // Import the email utility and assessee user creation utility
      const { sendAssessmentStatusEmail } = await import('../utils/assessment-status-email');
      const { createUserForAssesseeIfOver16 } = await import('../utils/assessee-user-creation');
      const { createFormsForAssessment } = await import('../utils/assessment-form-creation');
      
      // Create a user account for the assessee if they're over 16
      // Pass the request object to ensure correct URL generation
      await createUserForAssesseeIfOver16(assessee.id, req);
      
      // Create the appropriate forms for the assessment
      const formCreationResult = await createFormsForAssessment(assessmentId, req);
      logger.info(`Form creation result: ${formCreationResult.message}, ${formCreationResult.createdForms.length} forms created`);
      
      // Get base URL from request
      const protocol = req.headers['x-forwarded-proto'] || req.protocol;
      const host = req.headers.host || 'localhost:3000';
      const baseUrl = `${protocol}://${host}`;
      
      // Create a fresh, empty object for form links with appropriate typing
      const formLinks: {[key: string]: string} = {};
      
      // Explicitly extract tokens and prepare secure form links
      // This approach avoids potential issues with object references
      for (const form of formCreationResult.createdForms) {
        if (form.accessToken) {
          const linkUrl = `${baseUrl}/forms/access/${form.accessToken}`;
          formLinks[form.formType] = linkUrl;
          logger.info(`DEBUG: Added form link for type ${form.formType}: ${linkUrl}`);
        }
      }
      
      // Debug the formLinks object in detail
      logger.info(`DEBUG: formLinks object (stringified):`, JSON.stringify(formLinks, null, 2));
      logger.info(`DEBUG: formLinks object (direct):`, formLinks);
      logger.info(`DEBUG: formLinks type:`, typeof formLinks);
      logger.info(`DEBUG: formLinks has keys:`, Object.keys(formLinks).length > 0);
      logger.info(`DEBUG: formLinks keys:`, Object.keys(formLinks));
      logger.info(`DEBUG: formLinks values:`, Object.values(formLinks));
      
      // Make a deep copy of the formLinks object using JSON methods
      // This ensures we have a completely separate object with no references to the original
      // and protects against potential issues with proxy objects or getters/setters
      const formLinksForEmail = Object.keys(formLinks).length > 0 ? JSON.parse(JSON.stringify(formLinks)) : {};
      
      // Verify the copied object after cloning and add additional debugging
      logger.info(`DEBUG: formLinksForEmail after deep clone (stringified):`, JSON.stringify(formLinksForEmail, null, 2));
      logger.info(`DEBUG: formLinksForEmail after deep clone (direct):`, formLinksForEmail);
      logger.info(`DEBUG: formLinksForEmail type:`, typeof formLinksForEmail);
      logger.info(`DEBUG: formLinksForEmail has keys:`, Object.keys(formLinksForEmail).length > 0);
      logger.info(`DEBUG: formLinksForEmail keys:`, Object.keys(formLinksForEmail));
      logger.info(`DEBUG: formLinksForEmail values:`, Object.values(formLinksForEmail));
      
      // Send notification to the referring user if available
      if (referringUser && (referringUser as any).email) {
        await sendAssessmentStatusEmail(
          (referringUser as any).email,
          {
            assessmentId,
            assesseeName: assessee.fullName,
            oldStatus: 'VerificationPending',
            newStatus: 'pre_assessment',
            updatedBy: (req.user as any)?.email || 'Administrator',
            notes: 'Your referral has been approved and will now move to the Pre-Assessment phase. The secure links to complete the required forms are included below.',
            formLinks: formLinksForEmail
          },
          true // Mark as university/referring party email
        );
        logger.info(`Approval notification email sent to referring user: ${(referringUser as any).email}`);
      }
      
      // Send notification to the school if this is a school referral
      if (referringSchool?.contactEmail) {
        await sendAssessmentStatusEmail(
          referringSchool.contactEmail,
          {
            assessmentId,
            assesseeName: assessee.fullName,
            oldStatus: 'VerificationPending',
            newStatus: 'pre_assessment',
            updatedBy: (req.user as any)?.email || 'Administrator',
            notes: `Your school referral for ${assessee.fullName} has been approved and will now move to the Pre-Assessment phase. The secure links to complete the required forms are included below.`,
            formLinks: formLinksForEmail
          },
          true // Mark as school/referring party email
        );
        logger.info(`Approval notification email sent to school: ${referringSchool.contactEmail} (${referringSchool.name})`);
      }
      
      // Check if assessee is under 16 based on their date of birth
      const currentDate = new Date();
      const birthDate = new Date(assessee.dateOfBirth);
      const ageInMilliseconds = currentDate.getTime() - birthDate.getTime();
      const ageInYears = ageInMilliseconds / (1000 * 60 * 60 * 24 * 365.25);
      const isUnder16 = ageInYears < 16;
      
      if (isUnder16 && assessee.parentId) {
        // If under 16, send email to parent instead of assessee
        const parent = await storage.getUser(assessee.parentId);
        
        if ((parent as any)?.email) {
          await sendAssessmentStatusEmail(
            (parent as any).email,
            {
              assessmentId,
              assesseeName: assessee.fullName,
              oldStatus: 'VerificationPending',
              newStatus: 'pre_assessment',
              updatedBy: (req.user as any)?.email || 'Administrator',
              notes: `The assessment referral for ${assessee.fullName} has been approved. The assessment process will now begin. The secure links to complete the required forms are included below.`,
              formLinks: formLinksForEmail
            }
          );
          logger.info(`Approval notification email sent to parent: ${(parent as any).email} for assessee: ${assessee.fullName}`);
        }
      } else if (assessee.email) {
        // If 16 or older, send email directly to assessee
        await sendAssessmentStatusEmail(
          assessee.email,
          {
            assessmentId,
            assesseeName: assessee.fullName,
            oldStatus: 'VerificationPending',
            newStatus: 'pre_assessment',
            updatedBy: (req.user as any)?.email || 'Administrator',
            notes: 'Your assessment referral has been approved. The assessment process will now begin. The secure links to complete the required forms are included below.',
            formLinks: formLinksForEmail
          }
        );
        logger.info(`Approval notification email sent to assessee: ${assessee.email}`);
      }
    } catch (emailError) {
      // Log error but don't fail the whole process
      logger.error('Error sending approval notification emails or creating forms:', emailError);
    }
    
    res.json(updatedAssessment);
  } catch (error) {
    logger.error('Error approving referral:', error);
    res.status(500).json({ message: "Failed to approve referral" });
  }
}

/**
 * Reject a referral that's pending verification
 */
export async function rejectReferral(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }

  try {
    const assessmentId = parseInt(req.params.id);
    const assessment = await storage.getAssessment(assessmentId);
    
    if (!assessment) {
      return res.status(404).json({ message: "Assessment not found" });
    }
    
    // Check if assessment is pending verification (either VerificationPending or legacy Enquiry status)
    if (assessment.status !== 'VerificationPending' && assessment.status !== 'Enquiry') {
      return res.status(400).json({ message: "This assessment is not pending verification" });
    }
    
    // Additional check: don't allow rejection if already approved or rejected
    if (assessment.notes && (assessment.notes.startsWith('APPROVED:') || assessment.notes.startsWith('REJECTED:'))) {
      return res.status(400).json({ message: "This assessment has already been processed" });
    }

    // Since there's no 'Rejected' status in the enum, keep the current status
    // but mark it clearly in the notes field for filtering
    await storage.updateAssessment(assessmentId, {
      status: 'VerificationPending',
      notes: `REJECTED: ${assessment.notes || ''}`
    });
    
    // Log the activity
    await storage.createActivity({
      userId: req.user.id,
      assessmentId,
      action: 'reject_referral',
      details: `Rejected referral`
    });
    
    await storage.createAdminActivityLog({
      adminId: req.user.id,
      action: 'reject_referral',
      targetUserId: assessment.referringUserId || req.user.id,
      details: `Rejected referral ID: ${assessmentId}`,
      createdAt: new Date()
    });
    
    // Send email notifications for rejection
    try {
      // Get referring user and assessee details
      const expandedAssessment = await storage.getAssessment(assessmentId);
      if (!expandedAssessment) {
        throw new Error('Assessment data not found');
      }
      
      // Get additional details about the assessee
      const assessee = await storage.getAssessee(expandedAssessment.assesseeId);
      if (!assessee) {
        throw new Error('Assessee data not found');
      }
      
      // Get referring details based on referral type
      let referringUser = null;
      let referringSchool = null;
      
      // Check for referring user (individual or university referral)
      if (expandedAssessment.referringUserId) {
        referringUser = await storage.getUser(expandedAssessment.referringUserId);
      }
      
      // For school referrals, we need to get the school data
      if (expandedAssessment.referralType === 'school' && assessee.schoolId) {
        referringSchool = await storage.getSchool(assessee.schoolId);
        logger.info('School referral found:', referringSchool?.name);
      }
      
      // Import the email utilities
      const { sendAssessmentStatusEmail } = await import('../utils/assessment-status-email');
      
      // Send notification to the referring user if available
      if (referringUser && (referringUser as any).email) {
        await sendAssessmentStatusEmail(
          (referringUser as any).email,
          {
            assessmentId,
            assesseeName: assessee.fullName,
            oldStatus: 'VerificationPending',
            newStatus: 'Rejected',
            updatedBy: (req.user as any)?.email || 'Administrator',
            notes: 'Your referral has been reviewed and has not been approved at this time. Please contact us if you need further information.'
          },
          true // Mark as university/referring party email
        );
        logger.info(`Rejection notification email sent to referring user: ${(referringUser as any).email}`);
      }
      
      // Send notification to the school if this is a school referral
      if (referringSchool?.contactEmail) {
        await sendAssessmentStatusEmail(
          referringSchool.contactEmail,
          {
            assessmentId,
            assesseeName: assessee.fullName,
            oldStatus: 'VerificationPending',
            newStatus: 'Rejected',
            updatedBy: (req.user as any)?.email || 'Administrator',
            notes: `Your school referral for ${assessee.fullName} has been reviewed and has not been approved at this time. Please contact us if you need further information.`
          },
          true // Mark as school/referring party email
        );
        logger.info(`Rejection notification email sent to school: ${referringSchool.contactEmail} (${referringSchool.name})`);
      }
      
      // Check if assessee is under 16 based on their date of birth
      const currentDate = new Date();
      const birthDate = new Date(assessee.dateOfBirth);
      const ageInMilliseconds = currentDate.getTime() - birthDate.getTime();
      const ageInYears = ageInMilliseconds / (1000 * 60 * 60 * 24 * 365.25);
      const isUnder16 = ageInYears < 16;
      
      if (isUnder16 && assessee.parentId) {
        // If under 16, send email to parent instead of assessee
        const parent = await storage.getUser(assessee.parentId);
        
        if ((parent as any)?.email) {
          await sendAssessmentStatusEmail(
            (parent as any).email,
            {
              assessmentId,
              assesseeName: assessee.fullName,
              oldStatus: 'VerificationPending',
              newStatus: 'Rejected',
              updatedBy: (req.user as any)?.email || 'Administrator',
              notes: `The assessment referral for ${assessee.fullName} has been reviewed and has not been approved at this time. Please contact us if you need further information.`
            }
          );
          logger.info(`Rejection notification email sent to parent: ${(parent as any).email} for assessee: ${assessee.fullName}`);
        }
      } else if (assessee.email) {
        // If 16 or older, send email directly to assessee
        await sendAssessmentStatusEmail(
          assessee.email,
          {
            assessmentId,
            assesseeName: assessee.fullName,
            oldStatus: 'VerificationPending',
            newStatus: 'Rejected',
            updatedBy: (req.user as any)?.email || 'Administrator',
            notes: 'The assessment referral has been reviewed and has not been approved at this time. Please contact us if you need further information.'
          }
        );
        logger.info(`Rejection notification email sent to assessee: ${assessee.email}`);
      }
    } catch (emailError) {
      // Log error but don't fail the whole process
      logger.error('Error sending rejection notification emails:', emailError);
    }
    
    res.json({ message: "Referral rejected successfully" });
  } catch (error) {
    logger.error('Error rejecting referral:', error);
    res.status(500).json({ message: "Failed to reject referral" });
  }
}

/**
 * Update assessment notes (form data) via admin
 */
export async function updateAssessmentNotes(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }

  const updateAssessmentSchema = z.object({
    notes: z.string()
  });

  try {
    const { notes } = updateAssessmentSchema.parse(req.body);
    const assessmentId = parseInt(req.params.id);
    const assessment = await storage.getAssessment(assessmentId);
    
    if (!assessment) {
      return res.status(404).json({ message: "Assessment not found" });
    }
    
    // Update just the notes field
    const updatedAssessment = await storage.updateAssessment(assessmentId, { notes });
    
    // Log the activity
    await storage.createActivity({
      userId: req.user.id,
      assessmentId,
      action: 'update_form_data',
      details: `Updated form data for assessment`
    });
    
    await storage.createAdminActivityLog({
      adminId: req.user.id,
      action: 'update_form_data',
      targetUserId: assessment.referringUserId || req.user.id,
      details: `Updated form data for assessment ID: ${assessmentId}`,
      createdAt: new Date()
    });
    
    res.json(updatedAssessment);
  } catch (error) {
    logger.error('Error updating assessment notes:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Invalid input data", errors: error.errors });
    }
    res.status(500).json({ message: "Failed to update assessment notes" });
  }
}

/**
 * Get a single assessment with all related details
 * This endpoint provides comprehensive assessment data for the detailed view
 */
export async function getAssessmentDetails(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }

  try {
    const assessmentId = parseInt(req.params.id);
    const assessment = await storage.getAssessment(assessmentId);
    
    if (!assessment) {
      return res.status(404).json({ message: "Assessment not found" });
    }
    
    // Get assessee details
    const assessee = await storage.getAssessee(assessment.assesseeId);
    if (!assessee) {
      return res.status(404).json({ message: "Assessee not found" });
    }
    
    // Get parent details if applicable
    let parent = null;
    if (assessee.parentId) {
      const parentUser = await storage.getUser(assessee.parentId);
      if (parentUser) {
        const { password, ...parentWithoutPassword } = parentUser;
        parent = parentWithoutPassword;
      }
    }
    
    // Add parent to assessee
    const assesseeWithParent = {
      ...assessee,
      parent
    };
    
    // Get referring user if available
    let referringUser = null;
    if (assessment.referringUserId) {
      const user = await storage.getUser(assessment.referringUserId);
      if (user) {
        const { password, ...userWithoutPassword } = user;
        referringUser = userWithoutPassword;
      }
    }
    
    // Get school details if it's a school referral
    let referringSchool = null;
    if (assessment.referralType === 'school' && assessee.schoolId) {
      referringSchool = await storage.getSchool(assessee.schoolId);
    }


    // Retrieve the referral record linked to this assessment. If this fails for
    // any reason we log the error but continue so that the rest of the
    // assessment details can still be shown to the admin user.
    let referral = null;
    try {
      referral = await storage.getReferralByAssessmentId(assessment.id);
    } catch (referralError) {
      logger.error('Error fetching referral for assessment', assessment.id, referralError);
    }

    // Combine all the data
    const completeAssessment = {
      ...assessment,
      assessee: assesseeWithParent,
      referringUser,
      referringSchool,
      referral
    };
    
    res.json(completeAssessment);
  } catch (error) {
    logger.error('Error fetching assessment details:', error);
    res.status(500).json({ message: "Failed to fetch assessment details" });
  }
}

/**
 * Update an assessee's information
 */
export async function updateAssessee(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: "Unauthorized" });
  }

  try {
    const assesseeId = parseInt(req.params.id);
    const assesseeData = {...req.body};
    
    // Handle date of birth conversion - it MUST be a Date object or null
    if (assesseeData.dateOfBirth) {
      // Delete dateOfBirth if it can't be parsed, as this is causing the 500 error
      try {
        // Make sure we have a valid date string
        const dateStr = typeof assesseeData.dateOfBirth === 'string' 
          ? assesseeData.dateOfBirth 
          : assesseeData.dateOfBirth.toString();
        
        const parsedDate = new Date(dateStr);
        
        // Check if we have a valid date (isNaN check is critical here)
        if (isNaN(parsedDate.getTime())) {
          logger.error('Invalid date format detected:', assesseeData.dateOfBirth);
          delete assesseeData.dateOfBirth; // Remove invalid dateOfBirth to avoid 500 error
        } else {
          assesseeData.dateOfBirth = parsedDate;
          logger.info('Successfully parsed date of birth:', parsedDate);
        }
      } catch (dateError) {
        logger.error('Error parsing date of birth:', dateError, assesseeData.dateOfBirth);
        delete assesseeData.dateOfBirth; // Remove if we can't parse it
        return res.status(400).json({ message: "Invalid date format for date of birth" });
      }
    }
    
    const assessee = await storage.getAssessee(assesseeId);
    if (!assessee) {
      return res.status(404).json({ message: "Assessee not found" });
    }
    
    // Update the assessee data
    const updatedAssessee = await storage.updateAssessee(assesseeId, assesseeData);
    
    // Log the activity
    await storage.createAdminActivityLog({
      adminId: req.user.id,
      action: 'update_assessee',
      targetUserId: null,
      details: `Updated information for assessee ID: ${assesseeId}`,
      createdAt: new Date()
    });
    
    res.json(updatedAssessee);
  } catch (error) {
    logger.error('Error updating assessee:', error);
    res.status(500).json({ message: "Failed to update assessee" });
  }
}

/**
 * Update a referral record
 */
export async function updateReferral(req: Request, res: Response) {
  if (!req.isAuthenticated() || req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Unauthorized' });
  }

  try {
    const referralId = parseInt(req.params.id);
    const existingReferral = await storage.getReferral(referralId);

    if (!existingReferral) {
      return res.status(404).json({ message: 'Referral not found' });
    }

    const referralData: Partial<Referral> = { ...req.body };

    const updatedReferral = await storage.updateReferral(referralId, referralData);

    await storage.createAdminActivityLog({
      adminId: req.user.id,
      action: 'update_referral',
      targetUserId: existingReferral.referringUserId || null,
      details: `Updated referral ID: ${referralId}`,
      createdAt: new Date()
    });

    res.json(updatedReferral);
  } catch (error) {
    logger.error('Error updating referral:', error);
    res.status(500).json({ message: 'Failed to update referral' });
  }
}
