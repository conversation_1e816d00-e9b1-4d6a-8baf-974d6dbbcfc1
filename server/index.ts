// Load environment variables from .env file
import dotenv from 'dotenv';
dotenv.config();

console.log('TEST_SECRET:', process.env.TEST_SECRET || 'Missing');
import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { logger } from "./logger";
import * as path from "path";
import { setupSecurityMiddleware, csrfProtection, handleCSRFError } from "./security-middleware";
import { saveOriginalUrl } from "./middleware/redirect-after-login";

console.log("GOOGLE_APPLICATION_CREDENTIALS:", process.env.GOOGLE_APPLICATION_CREDENTIALS ? "Present" : "Missing");
try {
  const json = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS || "");
  console.log("Parsed JSON keys:", Object.keys(json));
} catch (err) {
  console.log("JSON parse error:", err);
}

const app = express();
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: false, limit: '50mb' }));

// Apply security middleware before handling routes
setupSecurityMiddleware(app);

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Log reset password request details but let Vite handle the response
  app.use('/reset-password*', (req, res, next) => {
    logger.info("🔒 Reset password request detected:");
    logger.info(`  - Path: ${req.path}`);
    logger.info(`  - Query params: ${JSON.stringify(req.query)}`);
    logger.info(`  - Full URL: ${req.protocol}://${req.get('host')}${req.originalUrl}`);
    next();
  });
  
  const server = await registerRoutes(app);

  // Handle CSRF errors first
  app.use(handleCSRFError);
  
  // Handle other errors
  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    logger.error(err);
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Use PORT from environment or default to 5000 for local development
  const port = parseInt(process.env.PORT || '5000');
  const host = process.env.NODE_ENV === 'production' ? "0.0.0.0" : "0.0.0.0";

  server.listen(port, host, () => {
    log(`serving on ${host}:${port}`);
  });
})();
