import { 
  User, InsertUser, 
  University, InsertUniversity,
  Assessee, InsertAssessee, 
  School, InsertSchool, 
  Assessment, InsertAssessment, 
  Form, InsertForm,
  FormQuestion, InsertFormQuestion,
  FormResponse, InsertFormResponse, 
  Document, InsertDocument,
  Activity, InsertActivity,
  PublicTrackingRecord, InsertPublicTrackingRecord,
  Conversation, InsertConversation,
  ConversationParticipant, InsertConversationParticipant,
  Message, InsertMessage,
  MessageRecipient, InsertMessageRecipient,
  Notification, InsertNotification,
  NotificationPreference, InsertNotificationPreference,
  Referral, InsertReferral
} from "@shared/schema";

export interface IStorage {
  // Universities
  getUniversity(id: number): Promise<University | undefined>;
  getUniversityByEmailDomain(emailDomain: string): Promise<University | undefined>;
  createUniversity(university: InsertUniversity): Promise<University>;
  updateUniversity(id: number, university: Partial<University>): Promise<University | undefined>;
  listUniversities(): Promise<University[]>;
  getAllUniversitiesWithAdminInfo(): Promise<(University & { admins: User[] })[]>;
  getUniversityWithStaff(id: number): Promise<(University & { staff: User[] }) | undefined>;
  getUniversityAdmins(universityId: number): Promise<User[]>;
  
  // Public Tracking
  getPublicTrackingRecord(trackingId: string): Promise<PublicTrackingRecord | undefined>;
  createPublicTrackingRecord(record: InsertPublicTrackingRecord): Promise<PublicTrackingRecord>;
  getAssessmentByTrackingId(trackingId: string): Promise<Assessment | undefined>;
  
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<User>): Promise<User | undefined>;
  listUsers(role?: string): Promise<User[]>;
  listUsersByUniversity(universityId: number): Promise<User[]>;

  // Password Reset Tokens
  storePasswordResetToken(userId: number, token: string, expires: Date): Promise<boolean>;
  getPasswordResetToken(token: string): Promise<{ userId: number, expires: Date } | null>;
  invalidatePasswordResetToken(token: string): Promise<boolean>;
  
  // Assessees
  getAssessee(id: number): Promise<Assessee | undefined>;
  createAssessee(assessee: InsertAssessee): Promise<Assessee>;
  updateAssessee(id: number, assessee: Partial<Assessee>): Promise<Assessee | undefined>;
  listAssesseesByParent(parentId: number): Promise<Assessee[]>;
  listAssesseesBySchool(schoolId: number): Promise<Assessee[]>;
  listAllAssessees(): Promise<Assessee[]>;
  
  // Schools
  getSchool(id: number): Promise<School | undefined>;
  createSchool(school: InsertSchool): Promise<School>;
  updateSchool(id: number, school: Partial<School>): Promise<School | undefined>;
  listSchools(): Promise<School[]>;
  
  // Assessments
  getAssessment(id: number): Promise<Assessment | undefined>;
  createAssessment(assessment: InsertAssessment): Promise<Assessment>;
  updateAssessment(id: number, assessment: Partial<Assessment>): Promise<Assessment | undefined>;
  listAssessments(filters?: Partial<Assessment>): Promise<Assessment[]>;
  listAssessmentsByUser(userId: number, role: string): Promise<Assessment[]>;
  getAssessmentsByUniversity(universityId: number): Promise<Assessment[]>;
  getAssessmentsByReferringUser(userId: number): Promise<Assessment[]>;

  // Referrals
  getReferral(id: number): Promise<Referral | undefined>;
  getReferralByTrackingId(trackingId: string): Promise<Referral | undefined>;
  getReferralByAssessmentId(assessmentId: number): Promise<Referral | undefined>;
  createReferral(referral: InsertReferral): Promise<Referral>;
  updateReferral(id: number, referral: Partial<Referral>): Promise<Referral | undefined>;
  
  // Forms
  getForm(id: number): Promise<Form | undefined>;
  getFormsByAssessment(assessmentId: number): Promise<Form[]>;
  createForm(form: InsertForm): Promise<Form>;
  updateForm(id: number, form: Partial<Form>): Promise<Form | undefined>;
  
  // Form Questions
  getFormQuestion(id: number): Promise<FormQuestion | undefined>;
  getFormQuestionsByType(formType: string): Promise<FormQuestion[]>;
  getFormQuestionsBySection(formType: string, section: string): Promise<FormQuestion[]>;
  createFormQuestion(question: InsertFormQuestion): Promise<FormQuestion>;
  bulkCreateFormQuestions(questions: InsertFormQuestion[]): Promise<FormQuestion[]>;
  
  // Form Responses
  getFormResponse(id: number): Promise<FormResponse | undefined>;
  getFormResponseByQuestion(formId: number, questionId: number): Promise<FormResponse | undefined>;
  getFormResponses(formId: number): Promise<FormResponse[]>;
  createFormResponse(response: InsertFormResponse): Promise<FormResponse>;
  updateFormResponse(id: number, response: Partial<FormResponse>): Promise<FormResponse | undefined>;
  bulkUpsertFormResponses(responses: InsertFormResponse[]): Promise<FormResponse[]>;
  
  // Documents
  getDocument(id: number): Promise<Document | undefined>;
  getDocumentsByAssessment(assessmentId: number): Promise<Document[]>;
  createDocument(document: InsertDocument): Promise<Document>;
  
  // Activities
  createActivity(activity: InsertActivity): Promise<Activity>;
  listActivitiesByAssessment(assessmentId: number): Promise<Activity[]>;
  listRecentActivities(limit: number): Promise<Activity[]>;
  
  // Admin Activity Logs
  createAdminActivityLog(logData: { adminId: number, action: string, targetUserId: number | null, details: string, createdAt: Date }): Promise<void>;
  getAdminActivityLogs(limit?: number): Promise<any[]>;
  
  // Conversations
  getConversation(id: number): Promise<Conversation | undefined>;
  getConversationWithParticipants(id: number): Promise<(Conversation & { participants: User[] }) | undefined>;
  createConversation(conversation: InsertConversation): Promise<Conversation>;
  updateConversation(id: number, conversation: Partial<Conversation>): Promise<Conversation | undefined>;
  getConversationsByUser(userId: number): Promise<Conversation[]>;
  getConversationsByAssessment(assessmentId: number): Promise<Conversation[]>;
  addParticipantToConversation(conversationId: number, userId: number): Promise<ConversationParticipant>;
  removeParticipantFromConversation(conversationId: number, userId: number): Promise<boolean>;
  
  // Messages
  getMessage(id: number): Promise<Message | undefined>;
  getMessageWithSender(id: number): Promise<(Message & { sender: User }) | undefined>;
  createMessage(message: InsertMessage): Promise<Message>;
  updateMessage(id: number, message: Partial<Message>): Promise<Message | undefined>;
  getMessagesByConversation(conversationId: number): Promise<Message[]>;
  getMessagesByUser(userId: number): Promise<Message[]>;
  markMessageAsRead(messageId: number, userId: number): Promise<boolean>;
  
  // Notifications
  getNotification(id: number): Promise<Notification | undefined>;
  createNotification(notification: InsertNotification): Promise<Notification>;
  getUnreadNotificationCount(userId: number): Promise<number>;
  getNotificationsByUser(userId: number, limit?: number, includeRead?: boolean): Promise<Notification[]>;
  markNotificationAsRead(notificationId: number): Promise<boolean>;
  markAllNotificationsAsRead(userId: number): Promise<boolean>;
  
  // Notification Preferences
  getNotificationPreferences(userId: number): Promise<NotificationPreference | undefined>;
  createOrUpdateNotificationPreferences(userId: number, preferences: any): Promise<NotificationPreference>;
  
  // Session Store
  sessionStore: any;
  
  // Password management
  updateUserPassword(userId: number, newPassword: string): Promise<boolean>;
  
  // Status management
  updateUserStatus(userId: number, status: string): Promise<boolean>;
}

// Import DatabaseStorage from separate file to avoid TypeScript issues
import { DatabaseStorage } from "./db-storage";

// Use DatabaseStorage for persistent database storage
export const storage = new DatabaseStorage();