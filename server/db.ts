// Load environment variables from .env file
import dotenv from 'dotenv';
dotenv.config();

import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";
import { logger } from './logger';

neonConfig.webSocketConstructor = ws;
neonConfig.pipelineConnect = false;

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

const pool = new Pool({ 
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 60000, // 60 seconds
  connectionTimeoutMillis: 10000, // 10 seconds
  keepAlive: true,
  keepAliveInitialDelayMillis: 10000,
});

// Handle pool errors
pool.on('error', (err) => {
  logger.error('Database pool error:', err);
});

// Graceful connection handling
pool.on('connect', () => {
  logger.info('Database connected successfully');
});

pool.on('remove', () => {
  logger.info('Database connection removed from pool');
});

export const db = drizzle(pool, { schema });
export { pool };