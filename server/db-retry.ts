import { logger } from './logger';

export interface RetryOptions {
  maxRetries?: number;
  delay?: number;
  backoff?: boolean;
}

export async function withDatabaseRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const { maxRetries = 3, delay = 1000, backoff = true } = options;
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      
      // Check if this is a connection-related error that should be retried
      const isRetryableError = 
        error?.message?.includes('Connection terminated unexpectedly') ||
        error?.message?.includes('connection timeout') ||
        error?.message?.includes('ECONNRESET') ||
        error?.message?.includes('ENOTFOUND') ||
        error?.code === 'ECONNRESET' ||
        error?.code === 'ETIMEDOUT';

      if (!isRetryableError || attempt === maxRetries) {
        logger.error(`Database operation failed after ${attempt} attempts:`, error);
        throw error;
      }

      const waitTime = backoff ? delay * Math.pow(2, attempt - 1) : delay;
      logger.warn(`Database connection error (attempt ${attempt}/${maxRetries}), retrying in ${waitTime}ms:`, error.message);
      
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  throw lastError;
}

export function wrapWithRetry<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  options?: RetryOptions
) {
  return async (...args: T): Promise<R> => {
    return withDatabaseRetry(() => fn(...args), options);
  };
}