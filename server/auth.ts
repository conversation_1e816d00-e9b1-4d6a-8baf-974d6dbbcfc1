import { logger } from './logger';
import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Express } from "express";
import session from "express-session";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import { storage } from "./storage";
import { User } from "@shared/schema";
import { saveOriginalUrl } from "./middleware/redirect-after-login";

declare global {
  namespace Express {
    interface User extends User {}
  }
}

const scryptAsync = promisify(scrypt);

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

async function comparePasswords(supplied: string, stored: string) {
  logger.info("Comparing passwords:");
  logger.info("Supplied password length:", supplied.length);
  logger.info("Stored password format:", stored.includes(".") ? "valid" : "invalid");
  
  const [hashed, salt] = stored.split(".");
  if (!hashed || !salt) {
    logger.info("ERROR: Invalid stored password format, missing hash or salt");
    return false;
  }
  
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  
  const isEqual = timingSafeEqual(hashedBuf, suppliedBuf);
  logger.info("Password match result:", isEqual);
  return isEqual;
}

export function setupAuth(app: Express) {
  const sessionSecret = process.env.SESSION_SECRET;
  if (!sessionSecret) {
    if (app.get('env') === 'production') {
      throw new Error('SESSION_SECRET must be set in production');
    }
    logger.warn('SESSION_SECRET not set, using insecure default');
  }

  const sessionSettings: session.SessionOptions = {
    secret: sessionSecret || 'spld-assessment-secret',
    resave: true, // Changed to true to ensure session is saved on each request
    saveUninitialized: true, // Changed to true to create session for all requests
    store: storage.sessionStore,
    name: "spld.session",
    cookie: {
      secure: false, // Set to false for development, true for production
      maxAge: 1000 * 60 * 60 * 24 * 7, // 1 week
      httpOnly: true,
      sameSite: 'lax',
      path: '/'
    }
  };

  app.set("trust proxy", 1);
  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());
  
  // Add the save original URL middleware after authentication is set up
  app.use(saveOriginalUrl);

  passport.use(
    new LocalStrategy(
      {
        usernameField: "username",
        passwordField: "password"
      },
      async (username, password, done) => {
        try {
          logger.info(`Attempting login for username/email: ${username}`);
          
          // Try to find by username first
          let user = await storage.getUserByUsername(username);
          
          // If username lookup fails, try email lookup
          if (!user) {
            logger.info(`User not found by username, trying email lookup: ${username}`);
            user = await storage.getUserByEmail(username);
          }
          
          if (!user) {
            logger.info(`User not found by username or email: ${username}`);
            return done(null, false);
          }
          
          logger.info(`User found: ${user.username}, id: ${user.id}, role: ${user.role}`);
          logger.info(`Stored password sample: ${user.password.substring(0, 20)}...`);
          
          const passwordValid = await comparePasswords(password, user.password);
          
          if (!passwordValid) {
            logger.info(`Password validation failed for user: ${user.username}`);
            return done(null, false);
          }
          
          // Check user status - only allow login for active users
          if (user.status !== 'active') {
            logger.info(`Login rejected - user ${user.username} has status: ${user.status}`);
            return done(null, false, { message: "Account is pending admin approval" });
          }
          
          logger.info(`Login successful for: ${user.username}`);
          return done(null, user);
        } catch (error) {
          logger.error(`Login error for ${username}:`, error);
          return done(error);
        }
      }
    )
  );

  passport.serializeUser((user, done) => {
    logger.info(`Serializing user: ${user.username}, id: ${user.id}`);
    done(null, user.id);
  });
  
  passport.deserializeUser(async (id: number, done) => {
    try {
      logger.info(`Deserializing user with id: ${id}`);
      const user = await storage.getUser(id);
      
      if (!user) {
        logger.info(`User not found during deserialization. User ID: ${id}`);
        return done(null, false);
      }
      
      logger.info(`User deserialized successfully: ${user.username}, role: ${user.role}`);
      done(null, user);
    } catch (error) {
      logger.error(`Error deserializing user with id ${id}:`, error);
      done(error);
    }
  });

  app.post("/api/register", async (req, res, next) => {
    try {
      // Check if user already exists
      const existingUser = await storage.getUserByEmail(req.body.email);
      if (existingUser) {
        return res.status(400).json({ message: "Email already in use" });
      }

      // Create new user with pending status
      const user = await storage.createUser({
        ...req.body,
        status: 'admin_approval_pending' // Set status to pending admin approval
      });
      
      // Explicitly update status to ensure it's set correctly in the database
      await storage.updateUserStatus(user.id, 'admin_approval_pending');
      
      // Send registration confirmation email to user
      try {
        const { sendRegistrationEmail } = await import("./utils/registration-email");
        await sendRegistrationEmail(user.email, user.fullName || user.username);
        logger.info(`Registration confirmation email sent to ${user.email}`);
      } catch (emailError) {
        logger.error("Error sending registration confirmation email:", emailError);
        // Continue even if email fails
      }
      
      // Notify all admin users about the new registration
      try {
        logger.info(`Starting admin notification process for new user: ${user.email}`);
        const { notifyAdminsOfNewRegistration, getBaseUrlFromRequest } = await import("./utils/admin-notification-service");
        const baseUrl = getBaseUrlFromRequest(req);
        logger.info(`Base URL for admin notifications: ${baseUrl}`);
        
        const notificationSuccess = await notifyAdminsOfNewRegistration(user, baseUrl);
        
        if (notificationSuccess) {
          logger.info(`Admin notification emails sent successfully for new user: ${user.email}`);
        } else {
          logger.warn(`Failed to send admin notification emails for new user: ${user.email}`);
        }
      } catch (adminNotificationError) {
        logger.error("Error sending admin notification emails:", adminNotificationError);
        logger.error("Admin notification error details:", JSON.stringify(adminNotificationError, null, 2));
        // Continue even if admin notification fails - don't block user registration
      }
      
      // Return user info but don't auto-login
      const { password, ...userWithoutPassword } = user;
      res.status(201).json({
        ...userWithoutPassword,
        message: "Your account is pending admin approval. You'll be notified by email when your account is activated."
      });
    } catch (error) {
      next(error);
    }
  });

  app.post("/api/login", (req, res, next) => {
    logger.info("Login request received:", { 
      username: req.body.username,
      passwordLength: req.body.password ? req.body.password.length : 0
    });
    
    // Check if session exists
    logger.info("Session before login:", {
      id: req.sessionID,
      cookie: req.session.cookie,
    });
    
    passport.authenticate("local", (err, user, info) => {
      if (err) {
        logger.error("Authentication error:", err);
        return next(err);
      }
      
      if (!user) {
        logger.info("Authentication failed - no user returned");
        return res.status(401).json({ message: "Invalid username or password" });
      }
      
      logger.info(`Authentication successful for user ${user.username}, establishing session`);
      
      // Force session regeneration to avoid session fixation
      req.session.regenerate((err) => {
        if (err) {
          logger.error("Session regeneration error:", err);
          return next(err);
        }
        
        // Now login the user
        req.login(user, (err) => {
          if (err) {
            logger.error("Session creation error:", err);
            return next(err);
          }
          
          // Save the session to ensure the store is updated
          req.session.save((err) => {
            if (err) {
              logger.error("Session save error:", err);
              return next(err);
            }
            
            logger.info("Session after login:", {
              id: req.sessionID,
              cookie: req.session.cookie,
              authenticated: req.isAuthenticated(),
              returnTo: req.session.returnTo
            });
            
            // Remove password from response
            const { password, ...userWithoutPassword } = user;
            
            // Check if we should redirect after login
            if (req.session && req.session.returnTo) {
              const returnTo = req.session.returnTo;
              delete req.session.returnTo;
              
              logger.info(`Login successful, redirecting to: ${returnTo}`);
              return res.json({ 
                ...userWithoutPassword,
                redirect: returnTo 
              });
            } else {
              // Regular API login - just send the user data
              logger.info("Login successful, sending user data without redirect");
              res.status(200).json(userWithoutPassword);
            }
          });
        });
      });
    })(req, res, next);
  });

  app.post("/api/logout", (req, res, next) => {
    logger.info(`Logout request received - session ID: ${req.sessionID}`);
    
    if (req.isAuthenticated()) {
      logger.info(`Logging out user: ${req.user.username}, id: ${req.user.id}`);
    } else {
      logger.info('Logout called but no authenticated user found');
    }
    
    // First, call logout to clear the login session
    req.logout((err) => {
      if (err) {
        logger.error('Error during logout:', err);
        return next(err);
      }
      
      // Then destroy the session completely
      req.session.destroy((err) => {
        if (err) {
          logger.error('Error destroying session:', err);
          return next(err);
        }
        
        logger.info('User successfully logged out, session destroyed');
        res.clearCookie('spld.session');
        res.sendStatus(200);
      });
    });
  });

  app.get("/api/user", (req, res) => {
    logger.info(`/api/user called - session ID: ${req.sessionID}`);
    logger.info(`Authentication status: ${req.isAuthenticated()}`);
    
    if (!req.isAuthenticated()) {
      logger.info('Unauthorized access attempt to /api/user');
      logger.info(`Session data:`, req.session);
      return res.status(401).json({ message: "Authentication required" });
    }
    
    logger.info(`User authenticated: ${req.user.username}, ID: ${req.user.id}, Role: ${req.user.role}`);
    // Remove password from response
    const { password, ...userWithoutPassword } = req.user as User;
    logger.info(`Returning user data`);
    res.json(userWithoutPassword);
  });
}
