import { logger } from '../../logger';
import express from 'express';
import { requireAdmin, getAllUsers, getUserById, updateUser, inviteUser, getActivityLogs, deleteUser, resetUserPassword } from '../../handlers/admin-handlers';
import { getPendingVerifications, approveReferral, rejectReferral, updateAssessmentNotes, getAssessmentDetails, updateAssessee, updateReferral } from '../../handlers/verification-handlers';
import { getAllAssessments, resetAssessmentStatus } from '../../handlers/all-assessments';
import { storage } from '../../storage';

const router = express.Router();

// Admin routes
router.get('/api/admin/users', requireAdmin, getAllUsers);
router.get('/api/admin/users/:id', requireAdmin, getUserById);
router.patch('/api/admin/users/:id', requireAdmin, updateUser);
router.post('/api/admin/invite', requireAdmin, inviteUser);
router.get('/api/admin/activity-logs', requireAdmin, getActivityLogs);
router.delete('/api/admin/users/:id', requireAdmin, deleteUser);
router.post('/api/admin/users/:id/reset-password', requireAdmin, resetUserPassword);

// Verification queue endpoints
router.get('/api/admin/pending-verifications', requireAdmin, getPendingVerifications);

// New endpoints for all referrals page
router.get('/api/admin/all-assessments', requireAdmin, getAllAssessments);
router.patch('/api/admin/reset-referral/:id', requireAdmin, resetAssessmentStatus);
router.get('/api/admin/assessments/:id', requireAdmin, getAssessmentDetails);
router.patch('/api/admin/update-assessee/:id', requireAdmin, updateAssessee);
router.patch('/api/admin/approve-referral/:id', requireAdmin, approveReferral);
router.patch('/api/admin/reject-referral/:id', requireAdmin, rejectReferral);
router.patch('/api/admin/update-assessment-notes/:id', requireAdmin, updateAssessmentNotes);
router.patch('/api/admin/update-referral/:id', requireAdmin, updateReferral);
router.patch('/api/admin/update-user/:id', requireAdmin, updateUser);

// Email functionality test route - admin only
router.post('/api/admin/test-email', requireAdmin, async (req, res) => {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({ message: 'Email address is required' });
    }
    const { testBasicTemplateEmail } = await import('../../utils/email-test');
    const result = await testBasicTemplateEmail(email);
    await storage.createAdminActivityLog({
      adminId: req.user?.id || 0,
      action: 'test_email',
      targetUserId: req.user?.id || null,
      details: `Sent test email to ${email}`,
      createdAt: new Date()
    });
    res.json(result);
  } catch (error) {
    logger.error('Error sending test email:', error);
    res.status(500).json({
      success: false,
      message: `Failed to send test email: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  }
});

// Public test email
router.post('/api/public-test-email', async (req, res) => {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({ success: false, message: 'Email address is required' });
    }
    const { sendEmail } = await import('../../utils/sendgrid');
    const emailSent = await sendEmail({
      to: email,
      subject: 'A2lexa Test Email',
      html: `
          <div style="font-family: Arial, sans-serif; padding: 20px;">
            <h1 style="color: #4a6cf7;">Test Email</h1>
            <p>This is a test email from the A2lexa platform.</p>
            <p>Time sent: ${new Date().toISOString()}</p>
          </div>
        `
    });
    logger.info(`Public test email to ${email} result: ${emailSent ? 'Success' : 'Failed'}`);
    res.json({
      success: emailSent,
      message: emailSent ? 'Test email sent successfully!' : 'Failed to send test email'
    });
  } catch (error) {
    logger.error('Error sending public test email:', error);
    res.status(500).json({ success: false, message: 'Error sending test email' });
  }
});

export default router;
