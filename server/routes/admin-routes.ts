import { Router } from 'express';
import { requireAdmin } from '../handlers/admin-handlers';
import { 
  getPendingVerifications,
  approveReferral,
  rejectReferral
} from '../handlers/verification-handlers';
import {
  getAllUsers,
  getUserById,
  updateUser,
  inviteUser,
  getActivityLogs
} from '../handlers/admin-handlers';

const router = Router();

// Verification routes
router.get('/pending-verifications', requireAdmin, getPendingVerifications);
router.post('/approve-referral/:id', requireAdmin, approveReferral);
router.post('/reject-referral/:id', requireAdmin, rejectReferral);

// User management routes
router.get('/users', requireAdmin, getAllUsers);
router.get('/users/:id', requireAdmin, getUserById);
router.patch('/users/:id', requireAdmin, updateUser);
router.post('/invite-user', requireAdmin, inviteUser);

// Activity logs
router.get('/activity-logs', requireAdmin, getActivityLogs);

export default router;
