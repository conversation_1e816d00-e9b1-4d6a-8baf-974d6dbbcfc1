import { logger } from '../logger';
import express from 'express';
import { storage } from '../storage';

// Helper to get base URL from request object
function getBaseUrlFromRequest(req: express.Request): string {
  // Default protocol is http, but use https if secure or if x-forwarded-proto is set
  const protocol = (req.secure || req.headers['x-forwarded-proto'] === 'https') ? 'https' : 'http';
  // Get hostname from header or request (includes port in development)
  const host = req.headers.host || req.hostname || 'localhost:5000';
  return `${protocol}://${host}`;
}

// Auth middleware
function requireAdmin(req: express.Request, res: express.Response, next: express.NextFunction) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "Authentication required" });
  }
  
  if (req.user?.role !== 'admin') {
    return res.status(403).json({ message: "Admin permission required" });
  }
  
  next();
}

const router = express.Router();

// Test email route - accessible by everyone in development, only admins in production
router.post('/test-email', async (req, res) => {
  try {
    const { email, testType = 'direct' } = req.body;
    
    if (!email) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email address is required' 
      });
    }
    
    const testTypes = ['direct', 'template', 'password-reset'];
    if (!testTypes.includes(testType)) {
      return res.status(400).json({
        success: false,
        message: `Invalid test type. Must be one of: ${testTypes.join(', ')}`
      });
    }
    
    logger.info(`Executing email test (${testType}) to: ${email}`);
    
    let result;
    const baseUrl = getBaseUrlFromRequest(req);
    const timestamp = new Date().toISOString();
    
    switch (testType) {
      case 'direct':
        // Test direct HTML email without templates
        const { sendEmail } = await import('../utils/sendgrid');
        result = await sendEmail({
          to: email,
          subject: `A2lexa Direct Test Email (${timestamp})`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
              <h1 style="color: #4a6cf7; margin-top: 0;">A2lexa Direct Email Test</h1>
              <p>This is a direct test email from the A2lexa platform (without template system).</p>
              <p><strong>Time sent:</strong> ${timestamp}</p>
              <p><strong>Server URL:</strong> ${baseUrl}</p>
              <div style="margin-top: 30px; padding: 15px; background-color: #f5f7fb; border-radius: 4px;">
                <p style="margin: 0;">This is a test email to diagnose delivery issues. Please ignore.</p>
              </div>
            </div>
          `
        });
        break;
        
      case 'template':
        // Test standard template email
        const { testBasicTemplateEmail } = await import('../utils/email-test');
        result = await testBasicTemplateEmail(email);
        break;
        
      case 'password-reset':
        // Test password reset template email
        const { testPasswordResetEmail } = await import('../utils/email-test');
        result = await testPasswordResetEmail(email, baseUrl);
        break;
    }
    
    // If the user is an admin, log this activity
    if (req.isAuthenticated() && req.user?.role === 'admin') {
      await storage.createAdminActivityLog({
        adminId: req.user.id,
        action: 'test_email',
        targetUserId: null,
        details: `Sent test email (${testType}) to ${email}`,
        createdAt: new Date()
      });
    }
    
    return res.json({
      success: true,
      message: `${testType} test email sent to ${email}`,
      testType,
      timestamp,
      result
    });
  } catch (error) {
    logger.error('Error sending test email:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to send test email',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Admin-only test email endpoints
router.post('/admin/test-email', requireAdmin, async (req, res) => {
  try {
    const { email, testType = 'template' } = req.body;
    
    if (!email) {
      return res.status(400).json({ message: "Email address is required" });
    }
    
    const baseUrl = getBaseUrlFromRequest(req);
    
    // Choose the test type based on request
    let result;
    if (testType === 'password-reset') {
      const { testPasswordResetEmail } = await import('../utils/email-test');
      result = await testPasswordResetEmail(email, baseUrl);
    } else {
      const { testBasicTemplateEmail } = await import('../utils/email-test');
      result = await testBasicTemplateEmail(email);
    }
    
    // Log admin activity
    await storage.createAdminActivityLog({
      adminId: req.user?.id || 0,
      action: 'test_email',
      targetUserId: req.user?.id || null,
      details: `Sent ${testType} test email to ${email}`,
      createdAt: new Date()
    });
    
    res.json({
      success: true,
      message: `${testType} test email sent to ${email}`,
      result
    });
  } catch (error) {
    logger.error('Error sending admin test email:', error);
    res.status(500).json({ 
      success: false, 
      message: `Failed to send test email: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  }
});

export default router;