import { logger } from './logger';
import { withDatabaseRetry } from './db-retry';
import { 
  users, assessees, schools, assessments, forms, documents, activities,
  referrals, adminActivityLogs, conversations, conversationParticipants,
  messages, messageRecipients, notifications, notificationPreferences,
  passwordResetTokens, formQuestions, formResponses, universities,
  publicTrackingRecords, issues, issueComments, assessmentNotes,
  type User, type InsertUser, type Assessee, type InsertAssessee,
  type School, type InsertSchool, type Assessment, type InsertAssessment,
  type Form, type InsertForm, type Document, type InsertDocument,
  type Activity, type InsertActivity, type Referral, type InsertReferral,
  type Conversation, type InsertConversation, type ConversationParticipant,
  type InsertConversationParticipant, type Message, type InsertMessage,
  type MessageRecipient, type InsertMessageRecipient, type Notification,
  type InsertNotification, type NotificationPreference, type InsertNotificationPreference,
  type FormQuestion, type InsertFormQuestion, type FormResponse, type InsertFormResponse,
  type Issue, type InsertIssue, type IssueComment, type InsertIssueComment
} from "@shared/schema";
import { db } from "./db";
import { and, eq, desc, asc, sql, count, like, ilike, isNull, or, gt } from "drizzle-orm";
import { IStorage } from "./storage";
import { scrypt, randomBytes } from "crypto";
import { promisify } from "util";

const scryptAsync = promisify(scrypt);

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

export class DatabaseStorage implements IStorage {
  sessionStore: any;

  constructor() {
    this.sessionStore = null;
  }

  // University methods
  async getUniversity(id: number): Promise<any> { return null; }
  async getUniversityByEmailDomain(domain: string): Promise<any> { return null; }
  async createUniversity(data: any): Promise<any> { return null; }
  async updateUniversity(id: number, data: any): Promise<any> { return null; }
  async listUniversities(): Promise<any[]> { return []; }
  async getAllUniversitiesWithAdminInfo(): Promise<any[]> { return []; }
  async getUniversityWithStaff(id: number): Promise<any> { return null; }
  async getUniversityAdmins(universityId: number): Promise<any[]> { return []; }
  
  // Public tracking methods
  async getPublicTrackingRecord(trackingId: string): Promise<any> { return null; }
  async createPublicTrackingRecord(record: any): Promise<any> { return null; }
  async getAssessmentByTrackingId(trackingId: string): Promise<any> { return null; }
  async listUsersByUniversity(universityId: number): Promise<any[]> { return []; }
  async getAssessmentsByUniversity(universityId: number): Promise<any[]> { return []; }
  async getAssessmentsByReferringUser(userId: number): Promise<any[]> { return []; }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const hashedPassword = await hashPassword(insertUser.password);
    const userWithHashedPassword = {
      email: insertUser.email,
      username: insertUser.username,
      password: hashedPassword,
      fullName: insertUser.fullName,
      role: insertUser.role,
      status: insertUser.status,
      phone: insertUser.phone,
      organization: insertUser.organization,
      position: insertUser.position,
      department: insertUser.department,
      createdAt: new Date()
    };

    const [user] = await db.insert(users).values(userWithHashedPassword).returning();
    return user;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const [user] = await db.update(users).set(userData).where(eq(users.id, id)).returning();
    return user;
  }

  async listUsers(role?: string): Promise<User[]> {
    if (role) {
      return db.select().from(users).where(eq(users.role, role as any));
    }
    return db.select().from(users);
  }

  // Password reset methods
  async storePasswordResetToken(userId: number, token: string, expires: Date): Promise<boolean> {
    try {
      await db
        .update(passwordResetTokens)
        .set({ usedAt: new Date() })
        .where(and(eq(passwordResetTokens.userId, userId), isNull(passwordResetTokens.usedAt)));

      await db.insert(passwordResetTokens).values({
        userId,
        token,
        expires,
        createdAt: new Date()
      });

      return true;
    } catch (error) {
      logger.error('Error storing password reset token:', error);
      return false;
    }
  }

  async getPasswordResetToken(token: string): Promise<{ userId: number; expires: Date } | null> {
    try {
      const [resetToken] = await db
        .select()
        .from(passwordResetTokens)
        .where(and(
          eq(passwordResetTokens.token, token),
          isNull(passwordResetTokens.usedAt)
        ));

      if (!resetToken) return null;

      return {
        userId: resetToken.userId,
        expires: resetToken.expires
      };
    } catch (error) {
      logger.error('Error getting password reset token:', error);
      return null;
    }
  }

  async invalidatePasswordResetToken(token: string): Promise<boolean> {
    try {
      await db
        .update(passwordResetTokens)
        .set({ usedAt: new Date() })
        .where(eq(passwordResetTokens.token, token));
      return true;
    } catch (error) {
      logger.error('Error invalidating password reset token:', error);
      return false;
    }
  }

  // Assessee methods
  async getAssessee(id: number): Promise<Assessee | undefined> {
    const [assessee] = await db.select().from(assessees).where(eq(assessees.id, id));
    return assessee;
  }

  async createAssessee(insertAssessee: InsertAssessee): Promise<Assessee> {
    const [assessee] = await db.insert(assessees).values(insertAssessee).returning();
    return assessee;
  }

  async updateAssessee(id: number, assesseeData: Partial<Assessee>): Promise<Assessee | undefined> {
    const [assessee] = await db.update(assessees).set(assesseeData).where(eq(assessees.id, id)).returning();
    return assessee;
  }

  async listAssesseesByParent(parentId: number): Promise<Assessee[]> {
    return db.select().from(assessees).where(eq(assessees.parentId, parentId));
  }

  async listAssesseesBySchool(schoolId: number): Promise<Assessee[]> {
    return db.select().from(assessees).where(eq(assessees.schoolId, schoolId));
  }

  async listAllAssessees(): Promise<Assessee[]> {
    return db.select().from(assessees);
  }

  // School methods
  async getSchool(id: number): Promise<School | undefined> {
    const [school] = await db.select().from(schools).where(eq(schools.id, id));
    return school;
  }

  async createSchool(insertSchool: InsertSchool): Promise<School> {
    const [school] = await db.insert(schools).values(insertSchool).returning();
    return school;
  }

  async updateSchool(id: number, schoolData: Partial<School>): Promise<School | undefined> {
    const [school] = await db.update(schools).set(schoolData).where(eq(schools.id, id)).returning();
    return school;
  }

  async listSchools(): Promise<School[]> {
    return db.select().from(schools);
  }

  // Assessment methods
  async getAssessment(id: number): Promise<Assessment | undefined> {
    const [assessment] = await db.select().from(assessments).where(eq(assessments.id, id));
    return assessment;
  }

  async createAssessment(insertAssessment: InsertAssessment): Promise<Assessment> {
    const [assessment] = await db.insert(assessments).values(insertAssessment).returning();
    return assessment;
  }

  async updateAssessment(id: number, assessmentData: Partial<Assessment>): Promise<Assessment | undefined> {
    const [assessment] = await db.update(assessments).set(assessmentData).where(eq(assessments.id, id)).returning();
    return assessment;
  }

  async listAssessments(filters?: Partial<Assessment>): Promise<Assessment[]> {
    let query = db.select().from(assessments);
    
    if (filters?.status) {
      query = query.where(eq(assessments.status, filters.status));
    }
    
    return query.orderBy(desc(assessments.createdAt));
  }

  async listAllAssessmentsWithUsers(): Promise<any[]> {
    try {
      const result = await db
        .select({
          assessmentId: assessments.id,
          assesseeId: assessments.assesseeId,
          assessorId: assessments.assessorId,
          referringUserId: assessments.referringUserId,
          referralType: assessments.referralType,
          status: assessments.status,
          paymentStatus: assessments.paymentStatus,
          depositAmount: assessments.depositAmount,
          finalAmount: assessments.finalAmount,
          depositPaidAt: assessments.depositPaidAt,
          finalPaidAt: assessments.finalPaidAt,
          scheduledDate: assessments.scheduledDate,
          completedDate: assessments.completedDate,
          notes: assessments.notes,
          createdAt: assessments.createdAt,
          updatedAt: assessments.updatedAt,
          assesseeFullName: assessees.fullName,
          assesseeEmail: assessees.email,
          assesseePhone: assessees.phone,
          referringUserFullName: users.fullName,
          referringUserEmail: users.email,
          referringUserPhone: users.phone,
          referringUserRole: users.role,
          referringUserOrganization: users.organization
        })
        .from(assessments)
        .leftJoin(assessees, eq(assessments.assesseeId, assessees.id))
        .leftJoin(users, eq(assessments.referringUserId, users.id))
        .orderBy(desc(assessments.createdAt));

      return result.map(row => ({
        id: row.assessmentId,
        assesseeId: row.assesseeId,
        assessorId: row.assessorId,
        referringUserId: row.referringUserId,
        referralType: row.referralType,
        status: row.status,
        paymentStatus: row.paymentStatus,
        depositAmount: row.depositAmount,
        finalAmount: row.finalAmount,
        depositPaidAt: row.depositPaidAt,
        finalPaidAt: row.finalPaidAt,
        scheduledDate: row.scheduledDate,
        completedDate: row.completedDate,
        notes: row.notes,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        assessee: {
          fullName: row.assesseeFullName,
          email: row.assesseeEmail,
          phone: row.assesseePhone
        },
        referringUser: row.referringUserFullName ? {
          fullName: row.referringUserFullName,
          organization: row.referringUserOrganization,
          email: row.referringUserEmail,
          phone: row.referringUserPhone,
          role: row.referringUserRole
        } : null
      }));
    } catch (error) {
      logger.error('Error fetching all assessments with users:', error);
      throw error;
    }
  }

  async listAssessmentsByUser(userId: number, role: string): Promise<Assessment[]> {
    if (role === 'parent') {
      return db.select().from(assessments)
        .innerJoin(assessees, eq(assessments.assesseeId, assessees.id))
        .where(eq(assessees.parentId, userId))
        .orderBy(desc(assessments.createdAt));
    } else if (role === 'school') {
      return db.select().from(assessments)
        .innerJoin(assessees, eq(assessments.assesseeId, assessees.id))
        .where(eq(assessees.schoolId, userId))
        .orderBy(desc(assessments.createdAt));
    }
    return [];
  }

  // Form methods
  async getForm(id: number): Promise<Form | undefined> {
    const [form] = await db.select().from(forms).where(eq(forms.id, id));
    return form;
  }

  async getFormsByAssessment(assessmentId: number): Promise<Form[]> {
    return db.select().from(forms).where(eq(forms.assessmentId, assessmentId));
  }

  async getAllForms(): Promise<Form[]> {
    return db.select().from(forms);
  }

  async getFormByAccessToken(token: string): Promise<Form | undefined> {
    const [form] = await db.select().from(forms).where(eq(forms.accessToken, token));
    return form;
  }

  async createForm(insertForm: InsertForm): Promise<Form> {
    const [form] = await db.insert(forms).values(insertForm).returning();
    return form;
  }

  async updateForm(id: number, formData: Partial<Form>): Promise<Form | undefined> {
    const [form] = await db.update(forms).set(formData).where(eq(forms.id, id)).returning();
    return form;
  }

  // Form question methods
  async getFormQuestion(id: number): Promise<FormQuestion | undefined> {
    const [question] = await db.select().from(formQuestions).where(eq(formQuestions.id, id));
    return question;
  }

  async getFormQuestionsByType(formType: string): Promise<FormQuestion[]> {
    return db.select().from(formQuestions).where(eq(formQuestions.formType, formType)).orderBy(asc(formQuestions.order));
  }

  async getFormQuestionsBySection(formType: string, section: string): Promise<FormQuestion[]> {
    return db.select().from(formQuestions)
      .where(and(eq(formQuestions.formType, formType), eq(formQuestions.section, section)))
      .orderBy(asc(formQuestions.order));
  }

  async createFormQuestion(question: InsertFormQuestion): Promise<FormQuestion> {
    const [formQuestion] = await db.insert(formQuestions).values(question).returning();
    return formQuestion;
  }

  async bulkCreateFormQuestions(questions: InsertFormQuestion[]): Promise<FormQuestion[]> {
    return db.insert(formQuestions).values(questions).returning();
  }

  // Form response methods
  async getFormResponse(id: number): Promise<FormResponse | undefined> {
    const [response] = await db.select().from(formResponses).where(eq(formResponses.id, id));
    return response;
  }

  async getFormResponseByQuestion(formId: number, questionId: number): Promise<FormResponse | undefined> {
    const [response] = await db.select().from(formResponses)
      .where(and(eq(formResponses.formId, formId), eq(formResponses.questionId, questionId)));
    return response;
  }

  async getFormResponses(formId: number): Promise<FormResponse[]> {
    return db.select().from(formResponses).where(eq(formResponses.formId, formId));
  }

  async createFormResponse(response: InsertFormResponse): Promise<FormResponse> {
    const [formResponse] = await db.insert(formResponses).values(response).returning();
    return formResponse;
  }

  async updateFormResponse(id: number, responseData: Partial<FormResponse>): Promise<FormResponse | undefined> {
    const [response] = await db.update(formResponses).set(responseData).where(eq(formResponses.id, id)).returning();
    return response;
  }

  async bulkUpsertFormResponses(responses: any[]): Promise<FormResponse[]> {
    const results: FormResponse[] = [];
    
    for (const response of responses) {
      const existing = await this.getFormResponseByQuestion(response.formId, response.questionId);
      
      if (existing) {
        const updated = await this.updateFormResponse(existing.id, response);
        if (updated) results.push(updated);
      } else {
        const created = await this.createFormResponse(response);
        results.push(created);
      }
    }
    
    return results;
  }

  // Document methods
  async getDocument(id: number): Promise<Document | undefined> {
    const [document] = await db.select().from(documents).where(eq(documents.id, id));
    return document;
  }

  async getDocumentsByAssessment(assessmentId: number): Promise<Document[]> {
    return db.select().from(documents).where(eq(documents.assessmentId, assessmentId));
  }

  async createDocument(insertDocument: InsertDocument): Promise<Document> {
    const [documentRecord] = await db.insert(documents).values(insertDocument).returning();
    
    const document: Document = {
      id: documentRecord.id,
      assessmentId: documentRecord.assessmentId,
      name: documentRecord.name,
      type: documentRecord.type,
      filePath: documentRecord.filePath,
      uploadedBy: documentRecord.uploadedBy,
      createdAt: documentRecord.createdAt
    };
    
    return document;
  }

  // Activity methods
  async createActivity(insertActivity: InsertActivity): Promise<Activity> {
    const [activity] = await db.insert(activities).values(insertActivity).returning();
    return activity;
  }

  async listActivitiesByAssessment(assessmentId: number): Promise<Activity[]> {
    return db.select().from(activities).where(eq(activities.assessmentId, assessmentId)).orderBy(desc(activities.createdAt));
  }

  async listRecentActivities(limit: number): Promise<Activity[]> {
    return db.select().from(activities).orderBy(desc(activities.createdAt)).limit(limit);
  }

  // Admin activity log methods
  async createAdminActivityLog(logData: { adminId: number, action: string, targetUserId: number | null, details: string, createdAt: Date }): Promise<void> {
    await db.insert(adminActivityLogs).values(logData);
  }

  async getAdminActivityLogs(limit = 100): Promise<any[]> {
    return db.select().from(adminActivityLogs).orderBy(desc(adminActivityLogs.createdAt)).limit(limit);
  }

  // User management methods
  async updateUserPassword(userId: number, newPassword: string): Promise<boolean> {
    try {
      const hashedPassword = await hashPassword(newPassword);
      await db.update(users).set({ password: hashedPassword }).where(eq(users.id, userId));
      return true;
    } catch (error) {
      logger.error('Error updating user password:', error);
      return false;
    }
  }

  async updateUserStatus(userId: number, status: string): Promise<boolean> {
    try {
      await db.update(users).set({ status: status as any }).where(eq(users.id, userId));
      return true;
    } catch (error) {
      logger.error('Error updating user status:', error);
      return false;
    }
  }

  // Assessment notes methods
  async getAssessmentNotes(assessmentId: number): Promise<any[]> {
    return db.select().from(assessmentNotes)
      .leftJoin(users, eq(assessmentNotes.userId, users.id))
      .where(eq(assessmentNotes.assessmentId, assessmentId))
      .orderBy(desc(assessmentNotes.createdAt));
  }

  async createAssessmentNote(data: any): Promise<any> {
    const [note] = await db.insert(assessmentNotes).values(data).returning();
    return note;
  }

  async updateAssessmentNote(noteId: number, data: any): Promise<any> {
    const [note] = await db.update(assessmentNotes).set(data).where(eq(assessmentNotes.id, noteId)).returning();
    return note;
  }

  async deleteAssessmentNote(noteId: number): Promise<void> {
    await db.delete(assessmentNotes).where(eq(assessmentNotes.id, noteId));
  }

  async getAssessmentWithDetails(id: number): Promise<any | undefined> {
    const [assessment] = await db.select().from(assessments)
      .leftJoin(assessees, eq(assessments.assesseeId, assessees.id))
      .leftJoin(users, eq(assessments.assessorId, users.id))
      .where(eq(assessments.id, id));
    return assessment;
  }

  // Notification methods
  async getNotification(id: number): Promise<any> {
    const [notification] = await db.select().from(notifications).where(eq(notifications.id, id));
    return notification || null;
  }

  async createNotification(data: any): Promise<any> {
    const [notification] = await db.insert(notifications).values(data).returning();
    return notification;
  }

  async getUnreadNotificationCount(userId: number): Promise<number> {
    const result = await db.select({ count: count() }).from(notifications)
      .where(and(eq(notifications.userId, userId), eq(notifications.status, 'unread')));
    return result[0]?.count || 0;
  }

  async getNotificationsByUser(userId: number, limit?: number, includeRead?: boolean): Promise<any[]> {
    let query = db.select().from(notifications).where(eq(notifications.userId, userId));

    if (!includeRead) {
      query = query.where(and(eq(notifications.userId, userId), eq(notifications.status, 'unread')));
    }

    query = query.orderBy(desc(notifications.createdAt));

    if (limit) {
      query = query.limit(limit);
    }

    return query;
  }

  async createNotification(data: any): Promise<any> {
    try {
      const [notification] = await db
        .insert(notifications)
        .values({
          userId: data.userId,
          type: data.type,
          title: data.title,
          content: data.content || data.message,
          status: 'unread',
          url: data.url,
          sourceId: data.sourceId,
          sourceType: data.sourceType,
          data: data.data
        })
        .returning();
      return notification;
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw error;
    }
  }

  async getNotification(notificationId: number): Promise<any> {
    try {
      const [notification] = await db.select().from(notifications).where(eq(notifications.id, notificationId));
      return notification;
    } catch (error) {
      logger.error('Error getting notification:', error);
      return null;
    }
  }

  async markNotificationAsRead(notificationId: number): Promise<boolean> {
    try {
      await db.update(notifications).set({ status: 'read', readAt: new Date() }).where(eq(notifications.id, notificationId));
      return true;
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      return false;
    }
  }

  async markAllNotificationsAsRead(userId: number): Promise<boolean> {
    try {
      await db.update(notifications)
        .set({ status: 'read', readAt: new Date() })
        .where(and(eq(notifications.userId, userId), eq(notifications.status, 'unread')));
      return true;
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      return false;
    }
  }

  // Notification preference methods
  async getNotificationPreferences(userId: number): Promise<NotificationPreference | undefined> {
    try {
      const [prefs] = await db.select().from(notificationPreferences).where(eq(notificationPreferences.userId, userId));
      return prefs;
    } catch (error) {
      logger.error('Error in getNotificationPreferences:', error);
      return undefined;
    }
  }

  async createOrUpdateNotificationPreferences(userId: number, preferences: any): Promise<NotificationPreference> {
    try {
      const existingPrefs = await this.getNotificationPreferences(userId);

      if (existingPrefs) {
        const [updatedPrefs] = await db.update(notificationPreferences)
          .set({ preferences, updatedAt: new Date() })
          .where(eq(notificationPreferences.id, existingPrefs.id))
          .returning();
        return updatedPrefs;
      } else {
        const [newPrefs] = await db.insert(notificationPreferences)
          .values({ userId, preferences, updatedAt: new Date() })
          .returning();
        return newPrefs;
      }
    } catch (error) {
      logger.error('Error in createOrUpdateNotificationPreferences:', error);
      throw error;
    }
  }

  // Referral methods
  async getReferral(id: number): Promise<Referral | undefined> {
    try {
      const [referral] = await db.select().from(referrals).where(eq(referrals.id, id));
      return referral;
    } catch (error) {
      logger.error('Error in getReferral:', error);
      return undefined;
    }
  }

  async getReferralByTrackingId(trackingId: string): Promise<Referral | undefined> {
    try {
      const [referral] = await db.select().from(referrals).where(eq(referrals.trackingId, trackingId));
      return referral;
    } catch (error) {
      logger.error('Error in getReferralByTrackingId:', error);
      return undefined;
    }
  }

  async getReferralByAssessmentId(assessmentId: number): Promise<Referral | undefined> {
    try {
      const [referral] = await db.select().from(referrals).where(eq(referrals.assessmentId, assessmentId));
      return referral;
    } catch (error) {
      logger.error('Error in getReferralByAssessmentId:', error);
      return undefined;
    }
  }

  async createReferral(insertReferral: InsertReferral): Promise<Referral> {
    const [referral] = await db.insert(referrals).values(insertReferral).returning();
    return referral;
  }

  async updateReferral(id: number, referralData: Partial<Referral>): Promise<Referral | undefined> {
    const [referral] = await db.update(referrals)
      .set({ ...referralData, updatedAt: new Date() })
      .where(eq(referrals.id, id))
      .returning();
    return referral;
  }

  // Stub methods for unimplemented features
  async getConversation(id: number): Promise<any> { return null; }
  async getConversationWithParticipants(id: number): Promise<any> { return null; }
  async createConversation(data: any): Promise<any> { return null; }
  async updateConversation(id: number, data: any): Promise<any> { return null; }
  async getConversationsByUser(userId: number): Promise<any[]> { return []; }
  async getConversationsByAssessment(assessmentId: number): Promise<any[]> { return []; }
  async addParticipantToConversation(conversationId: number, userId: number): Promise<any> { return null; }
  async removeParticipantFromConversation(conversationId: number, userId: number): Promise<boolean> { return false; }
  async getMessage(id: number): Promise<any> { return null; }
  async getMessageWithSender(id: number): Promise<any> { return null; }
  async createMessage(data: any): Promise<any> { return null; }
  async updateMessage(id: number, data: any): Promise<any> { return null; }
  async getMessagesByConversation(conversationId: number): Promise<any[]> { return []; }
  async getMessagesByUser(userId: number): Promise<any[]> { return []; }
  async markMessageAsRead(messageId: number, userId: number): Promise<boolean> { return false; }
  async getIssue(id: number): Promise<any> { return null; }
  async createIssue(data: any): Promise<any> { return null; }
  async updateIssue(id: number, data: any): Promise<any> { return null; }
  async listIssues(filters?: any): Promise<any[]> { return []; }
  async resolveIssue(id: number, resolvedById: number, resolutionNotes?: string): Promise<any> { return null; }
  async assignIssue(id: number, assignedToId: number): Promise<any> { return null; }
  async createIssueComment(data: any): Promise<any> { return null; }
  async getIssueComments(issueId: number): Promise<any[]> { return []; }
  async updateIssueComment(id: number, data: any): Promise<any> { return null; }
}