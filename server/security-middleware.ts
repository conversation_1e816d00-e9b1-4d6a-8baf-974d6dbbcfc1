/**
 * Security Middleware for A2lexa Application
 * 
 * This module provides enhanced security protections including:
 * - HTTP Security Headers via Helmet.js
 * - Rate limiting for public endpoints
 * - CSRF protection
 * - Input sanitization
 */

import { Express, Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import csurf from 'csurf';
import { JSDOM } from 'jsdom';
import createDOMPurify from 'dompurify';

// Initialize DOMPurify
const window = new JSDOM('').window;
const DOMPurify = createDOMPurify(window);

// Rate limiting middleware
export const publicFormRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour window
  max: 5, // 5 submissions per IP per hour
  message: { 
    error: "Too many submissions from this IP. Please try again later." 
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// API rate limiter (more permissive)
export const apiRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per IP per 15 minutes
  message: { 
    error: "Too many requests. Please try again later." 
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Function to sanitize user input
export function sanitizeInput(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [], // No HTML tags allowed, only text
    ALLOWED_ATTR: []  // No HTML attributes allowed
  });
}

// Function to sanitize rich text input (allows limited HTML)
export function sanitizeRichText(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ['p', 'b', 'i', 'em', 'strong', 'a', 'ul', 'ol', 'li', 'br'],
    ALLOWED_ATTR: ['href', 'target', 'rel'],
    FORBID_TAGS: ['script', 'style', 'iframe', 'form', 'input'],
    FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover']
  });
}

// CSRF protection middleware (to be used selectively)
export const csrfProtection = csurf({ 
  cookie: false,  // Don't use cookies for CSRF
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS'],
  value: (req) => {
    return req.body && req.body._csrf || req.headers['x-csrf-token'] || req.headers['csrf-token'] || '';
  }
});

// Middleware to handle CSRF token errors
export function handleCSRFError(err: any, req: Request, res: Response, next: NextFunction) {
  if (err.code === 'EBADCSRFTOKEN') {
    return res.status(403).json({ 
      error: 'CSRF token validation failed. The form may have expired. Please refresh the page and try again.' 
    });
  }
  next(err);
}

// Setup all security middleware
export function setupSecurityMiddleware(app: Express) {
  // Apply Helmet for securing HTTP headers
  app.use(helmet());

  // Simple CORS middleware to allow cross-origin API requests
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET,POST,PATCH,PUT,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-CSRF-Token');
    res.header('Access-Control-Allow-Credentials', 'true');
    if (req.method === 'OPTIONS') {
      return res.sendStatus(200);
    }
    next();
  });

  // Configure Content Security Policy
  app.use(
    helmet.contentSecurityPolicy({
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: [
          "'self'",
          "'unsafe-inline'",
          "'unsafe-eval'",
          "https://www.google.com/recaptcha/",
          "https://www.gstatic.com/"
        ],
        styleSrc: [
          "'self'",
          "'unsafe-inline'",
          "https://fonts.googleapis.com"
        ],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "blob:"],
        connectSrc: [
          "'self'",
          "wss://*.replit.app",
          "wss://*.replit.dev",
          // Allow API requests to local backend during development
          "http://localhost:*",
          "https://localhost:*",
          "ws://localhost:*",
          "http://127.0.0.1:*",
          "https://127.0.0.1:*",
          "ws://127.0.0.1:*",
          "https://www.google.com/",
          "https://www.google.com/recaptcha/",
          "https://www.gstatic.com/",
          "https://recaptcha.net/",
          "https://*.recaptcha.net/",
          "https://*.google.com/"
        ],
        frameSrc: ["'self'", "https://www.google.com/"],
        objectSrc: ["'none'"],
        baseUri: ["'self'"]
      }
    })
  );

  // Apply rate limiting to public forms
  app.use('/api/public/university-referral', publicFormRateLimiter);
  app.use('/api/public/individual-referral', publicFormRateLimiter);
  
  // Apply more permissive rate limiting to all other API routes
  app.use('/api', apiRateLimiter);
  
  // CSRF protection for routes that need it
  // Note: We don't apply this globally as it would break API functionality
  // Instead it should be applied selectively to form submission routes
  
  // Handle CSRF errors
  app.use(handleCSRFError);
  
  // Add any additional security middleware here
}