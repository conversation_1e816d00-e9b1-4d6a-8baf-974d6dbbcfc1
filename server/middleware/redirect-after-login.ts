import { logger } from '../logger';
/**
 * Middleware to handle redirects after login
 * 
 * This middleware saves the original URL that unauthenticated users were trying to access
 * so they can be redirected back after successful login
 */

import { Request, Response, NextFunction } from 'express';
import { SessionData } from 'express-session';

// Extend the Session type to include our custom returnTo property
declare module 'express-session' {
  interface SessionData {
    returnTo?: string;
  }
}

export function saveOriginalUrl(req: Request, res: Response, next: NextFunction) {
  // Only save URLs for GET requests
  if (req.method === 'GET') {
    // Need to check if the user is authenticated
    const isAuthenticated = req.isAuthenticated ? req.isAuthenticated() : false;
    
    // Only proceed for unauthenticated users
    if (!isAuthenticated) {
      // Skip API routes and auth pages
      if (!req.path.startsWith('/api/') && 
          !req.path.startsWith('/auth') && 
          !req.path.startsWith('/login') && 
          !req.path.startsWith('/register') &&
          !req.path.startsWith('/reset-password')) {
        
        // Save the full URL including query parameters
        const fullUrl = req.originalUrl;
        
        logger.info(`Saving original URL for redirect after login: ${fullUrl}`);
        if (req.session) {
          req.session.returnTo = fullUrl;
        }
      }
    }
  }
  
  next();
}

export function redirectToOriginalUrl(req: Request, res: Response, next: NextFunction) {
  // Check if we have a saved URL to redirect to
  if (req.session && req.session.returnTo) {
    const returnTo = req.session.returnTo;
    
    // Clear the saved URL to prevent future redirects
    delete req.session.returnTo;
    
    logger.info(`Redirecting to original URL after login: ${returnTo}`);
    return res.redirect(returnTo);
  }
  
  next();
}