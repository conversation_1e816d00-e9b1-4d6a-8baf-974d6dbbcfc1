import { logger } from '../logger';
import { Request } from 'express';

/**
 * Get the base URL for the current environment
 * This handles different deployment environments including Replit Dev/App URLs
 * 
 * @param req Optional Express request object for dynamic URL generation
 * @returns The base URL (protocol + hostname) without trailing slash
 */
export function getBaseUrl(req?: Request | any): string {
  // Try to detect if we're running on Replit
  const isOnReplit = process.env.REPL_ID || process.env.REPL_SLUG;
  
  // Check for specific environment variables
  const replitAppUrl = process.env.REPLIT_APP_URL;
  const replitDevUrl = process.env.REPLIT_DEV_URL;
  const customDomain = process.env.CUSTOM_DOMAIN;
  const appUrl = process.env.APP_URL;
  
  // Prioritize explicitly set APP_URL if available
  if (appUrl) {
    return appUrl.startsWith('http') ? appUrl : `https://${appUrl}`;
  }
  
  // Next, prioritize custom domain if specified
  if (customDomain) {
    return customDomain.startsWith('http') ? customDomain : `https://${customDomain}`;
  }
  
  // Next, try Replit app URL (production)
  if (replitAppUrl) {
    return replitAppUrl.startsWith('http') ? replitAppUrl : `https://${replitAppUrl}`;
  }
  
  // Next, try Replit dev URL
  if (replitDevUrl) {
    return replitDevUrl.startsWith('http') ? replitDevUrl : `https://${replitDevUrl}`;
  }
  
  // If we're on Replit but no specific URLs are set, use the request's host
  if (isOnReplit && req) {
    let protocol: string;
    let host: string;
    
    // Handle different types of request objects (Express, mock requests, etc.)
    if (typeof req.get === 'function') {
      // Standard Express request with get() method
      protocol = req.protocol || 'https';
      host = req.get('host') || 'localhost:3000';
    } else if (req.headers) {
      // Request with headers object but no get() method
      protocol = req.headers['x-forwarded-proto'] || req.protocol || 'https';
      host = req.headers.host || 'localhost:3000';
    } else {
      // Fallback for other request object structures
      protocol = 'https';
      host = 'neuroelevate.replit.app'; // Fallback to production domain
      logger.info('Warning: Unusual request object format, using fallback URL');
    }
    
    return `${protocol}://${host}`;
  }
  
  // If we're running in a test or a non-request context, use a hardcoded value
  if (process.env.NODE_ENV === 'test' || !req) {
    return 'https://neuroelevate.replit.app';
  }
  
  // Fallback to localhost with default port for local development
  const port = process.env.PORT || 3000;
  return `http://localhost:${port}`;
}