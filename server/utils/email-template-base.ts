/**
 * Base email template for NeuroElevate
 * This template provides a consistent layout for all emails sent from the platform
 */

interface EmailTemplateOptions {
  title: string;         // Email title (displayed in the header)
  preheader?: string;    // Email preheader text (preview text shown in email clients)
  recipientName?: string; // Name of the recipient
  content: string;       // Main content HTML for the email
  buttonText?: string;   // Text for the CTA button
  buttonUrl?: string;    // URL for the CTA button
  footerText?: string;   // Custom footer text
}

/**
 * Creates HTML content for an email using the standard NeuroElevate template
 * @param options Template options or recipient email, subject, content if used with old signature
 * @returns HTML string for the email
 */
export function createEmailTemplate(options: EmailTemplateOptions | string, subject?: string, htmlContent?: string): string {
  // Handle backwards compatibility with old function signature
  if (typeof options === 'string') {
    if (!subject || !htmlContent) {
      throw new Error('When using string email signature, subject and content are required');
    }
    return createEmailTemplate({
      title: subject,
      content: htmlContent
    });
  }
  // Destructure options object for the main template generation
  const {
    title,
    preheader = "A message from NeuroElevate Assessment Platform",
    recipientName = "there",
    content,
    buttonText,
    buttonUrl,
    footerText = "If you have any questions or need assistance, please contact our support team."
  } = options as EmailTemplateOptions;

  // Current year for the copyright notice
  const currentYear = new Date().getFullYear();

  // Generate the button section if button parameters were provided
  const buttonSection = buttonText && buttonUrl
    ? `
      <div style="text-align: center; margin: 30px 0;">
        <a href="${buttonUrl}" 
           style="background-color: #10b981; color: white; padding: 12px 24px; 
                  text-decoration: none; border-radius: 4px; font-weight: bold;
                  display: inline-block;">
          ${buttonText}
        </a>
      </div>
    `
    : '';

  // Full HTML template
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="color-scheme" content="light">
      <meta name="supported-color-schemes" content="light">
      <title>${title}</title>
      <!--[if mso]>
      <style type="text/css">
        .fallback-font {
          font-family: Arial, sans-serif;
        }
      </style>
      <![endif]-->
      <!--[if !mso]><!-->
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
      </style>
      <!--<![endif]-->
    </head>
    <body style="margin: 0; padding: 0; width: 100%; background-color: #f9fafb;">
      <span style="display: none; max-height: 0px; overflow: hidden;">
        ${preheader}
      </span>
      
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
          <td align="center" style="padding: 25px 0;">
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
              <!-- Header with Logo -->
              <tr>
                <td style="background-color: #10b981; padding: 20px; text-align: center; border-radius: 6px 6px 0 0;">
                  <h1 style="color: white; margin: 0; font-family: 'Inter', Arial, sans-serif; font-weight: 700;" class="fallback-font">NeuroElevate</h1>
                  <p style="color: white; margin: 5px 0 0; font-family: 'Inter', Arial, sans-serif;" class="fallback-font">Assessment Platform</p>
                </td>
              </tr>
              
              <!-- Main Content -->
              <tr>
                <td style="background-color: #ffffff; padding: 30px; border-left: 1px solid #e5e7eb; border-right: 1px solid #e5e7eb; font-family: 'Inter', Arial, sans-serif; font-size: 16px; line-height: 1.6; color: #374151;" class="fallback-font">
                  
                  ${content}
                  
                  ${buttonSection}
                  
                  <p style="margin-bottom: 0;">${footerText}</p>
                </td>
              </tr>
              
              <!-- Footer -->
              <tr>
                <td style="background-color: #f3f4f6; padding: 20px; text-align: center; border-radius: 0 0 6px 6px; font-family: 'Inter', Arial, sans-serif; font-size: 14px; color: #6b7280;" class="fallback-font">
                  <p style="margin: 0 0 10px 0;">Thank you,<br>The NeuroElevate Team</p>
                  <p style="margin: 0; font-size: 12px;">© ${currentYear} NeuroElevate. All rights reserved.</p>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `;
}

/**
 * Creates plain text content for an email
 * This serves as a fallback for email clients that don't support HTML
 * 
 * @param options Template options
 * @returns Plain text string for the email
 */
export function createPlainTextEmail(options: EmailTemplateOptions): string {
  const {
    title,
    recipientName = "there",
    content: htmlContent,
    buttonText,
    buttonUrl,
    footerText = "If you have any questions or need assistance, please contact our support team."
  } = options as EmailTemplateOptions;

  // Convert the HTML content to plain text by removing HTML tags
  const plainContent = htmlContent
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/p>/gi, '\n\n')
    .replace(/<\/h[1-6]>/gi, '\n\n')
    .replace(/<\/div>/gi, '\n')
    .replace(/<li>/gi, '- ')
    .replace(/<\/li>/gi, '\n')
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/\n\s*\n/g, '\n\n')  // Replace multiple newlines with just two
    .trim();

  // Create the plain text email
  let plainTextEmail = `
NeuroElevate Assessment Platform
${title}

Hello ${recipientName},

${plainContent}
`;

  // Add button information if provided
  if (buttonText && buttonUrl) {
    plainTextEmail += `\n${buttonText}: ${buttonUrl}\n`;
  }

  // Add footer
  plainTextEmail += `\n${footerText}\n\nThank you,\nThe NeuroElevate Team`;
  
  return plainTextEmail;
}