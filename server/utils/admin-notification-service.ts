import { sendEmail } from './sendgrid';
import { createNewUserNotificationEmail, createPendingRegistrationsDigestEmail } from './admin-notification-email';
import { logger } from '../logger';
import { DatabaseStorage } from '../db-storage';
import { User } from '@shared/schema';
import { getBaseUrl } from './url-helper';

// Use DatabaseStorage directly to avoid interface mismatch issues
const dbStorage = new DatabaseStorage();

/**
 * Notify all admin users when a new user registers
 * @param newUser The newly registered user
 * @param baseUrl Base URL of the application for building review links
 * @returns Promise<boolean> indicating success
 */
export async function notifyAdminsOfNewRegistration(newUser: User, baseUrl: string): Promise<boolean> {
  try {
    logger.info(`Starting admin notification process for new user registration: ${newUser.email}`);
    
    // Get all admin users
    const adminUsers = await dbStorage.listUsers('admin');
    
    if (adminUsers.length === 0) {
      logger.warn('No admin users found to notify about new registration');
      return false;
    }
    
    logger.info(`Found ${adminUsers.length} admin user(s) to notify`);
    
    // Build the review URL for admins
    const reviewUrl = `${baseUrl}/admin/users`;
    
    // Send notification to each admin
    const emailPromises = adminUsers.map(async (admin) => {
      try {
        logger.info(`Sending new registration notification to admin: ${admin.email}`);
        
        const emailOptions = createNewUserNotificationEmail(
          admin.email,
          newUser,
          reviewUrl
        );
        
        const success = await sendEmail(emailOptions);
        
        if (success) {
          logger.info(`Successfully sent notification to admin: ${admin.email}`);
          
          // Create a notification record in the database for the admin
          await dbStorage.createNotification({
            userId: admin.id,
            type: 'user_registration',
            title: 'New User Registration',
            message: `${newUser.fullName || newUser.username} (${newUser.role}) has registered and requires review`,
            data: JSON.stringify({
              newUserId: newUser.id,
              newUserEmail: newUser.email,
              newUserRole: newUser.role,
              reviewUrl
            }),
            isRead: false
          });
          
        } else {
          logger.error(`Failed to send notification to admin: ${admin.email}`);
        }
        
        return success;
      } catch (error) {
        logger.error(`Error sending notification to admin ${admin.email}:`, error);
        return false;
      }
    });
    
    const results = await Promise.all(emailPromises);
    const successCount = results.filter(result => result).length;
    
    logger.info(`Email notification results: ${successCount}/${adminUsers.length} successful`);
    
    return successCount > 0;
  } catch (error) {
    logger.error('Error in notifyAdminsOfNewRegistration:', error);
    return false;
  }
}

/**
 * Send a digest email to all admins about pending registrations
 * @param baseUrl Base URL of the application
 * @returns Promise<boolean> indicating success
 */
export async function sendPendingRegistrationsDigest(baseUrl: string): Promise<boolean> {
  try {
    logger.info('Starting pending registrations digest process');
    
    // Get all admin users
    const adminUsers = await dbStorage.listUsers('admin');
    
    if (adminUsers.length === 0) {
      logger.warn('No admin users found for digest notification');
      return false;
    }
    
    // Get all pending users (assuming status 'pending' or similar)
    const allUsers = await dbStorage.listUsers();
    const pendingUsers = allUsers.filter(user => 
      user.status === 'pending' || 
      user.status === 'inactive' || 
      !user.status
    );
    
    if (pendingUsers.length === 0) {
      logger.info('No pending registrations found for digest');
      return true; // Not an error condition
    }
    
    logger.info(`Found ${pendingUsers.length} pending registration(s) for digest`);
    
    // Build the review URL for admins
    const reviewUrl = `${baseUrl}/admin/users`;
    
    // Send digest to each admin
    const emailPromises = adminUsers.map(async (admin) => {
      try {
        logger.info(`Sending pending registrations digest to admin: ${admin.email}`);
        
        const emailOptions = createPendingRegistrationsDigestEmail(
          admin.email,
          pendingUsers,
          reviewUrl
        );
        
        const success = await sendEmail(emailOptions);
        
        if (success) {
          logger.info(`Successfully sent digest to admin: ${admin.email}`);
        } else {
          logger.error(`Failed to send digest to admin: ${admin.email}`);
        }
        
        return success;
      } catch (error) {
        logger.error(`Error sending digest to admin ${admin.email}:`, error);
        return false;
      }
    });
    
    const results = await Promise.all(emailPromises);
    const successCount = results.filter(result => result).length;
    
    logger.info(`Digest email results: ${successCount}/${adminUsers.length} successful`);
    
    return successCount > 0;
  } catch (error) {
    logger.error('Error in sendPendingRegistrationsDigest:', error);
    return false;
  }
}

/**
 * Get the base URL from a request object
 * @param req Express request object
 * @returns Base URL string
 */
export function getBaseUrlFromRequest(req: any): string {
  // Delegate to the shared helper which handles APP_URL and other env vars
  return getBaseUrl(req);
}