import { logger } from '../logger';

export async function verifyRecaptchaV3(token: string, expectedAction = 'submit'): Promise<boolean> {
  try {
    logger.info('🔍 [reCAPTCHA v3] Starting verification process');
    logger.info('🔍 [reCAPTCHA v3] Token received:', token?.substring(0, 20) + '...');
    logger.info('🔍 [reCAPTCHA v3] Expected action:', expectedAction);
    
    // Skip verification for development or fallback production tokens
    if (token === 'manual_verification_token' || token === 'production_fallback_token') {
      logger.info('Using fallback verification token');
      return true;
    }
    
    // For testing in development without a real reCAPTCHA key
    if (process.env.NODE_ENV !== 'production' && !process.env.RECAPTCHA_V3_SECRET_KEY) {
      logger.info('Bypassing reCAPTCHA v3 verification in development environment');
      return true;
    }
    
    const secretKey = process.env.RECAPTCHA_V3_SECRET_KEY;
    
    if (!secretKey) {
      logger.error('reCAPTCHA v3 secret key is not configured');
      // Allow in development environments
      return process.env.NODE_ENV !== 'production';
    }
    
    // Verify with Google's reCAPTCHA v3 API
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${secretKey}&response=${token}`,
    });

    const data = await response.json();
    logger.info('🔍 [reCAPTCHA v3] Full verification response:', JSON.stringify(data, null, 2));
    
    if (!data.success) {
      logger.error('❌ [reCAPTCHA v3] Verification failed:', data['error-codes']);
      return false;
    }
    
    // Check score (reCAPTCHA v3 returns a score from 0.0 to 1.0)
    if (data.score !== undefined) {
      const threshold = 0.5; // You can adjust this threshold
      logger.info(`🔍 [reCAPTCHA v3] Score: ${data.score} (threshold: ${threshold})`);
      
      if (data.score < threshold) {
        logger.warn(`⚠️ [reCAPTCHA v3] Score ${data.score} below threshold ${threshold}`);
        return false;
      }
    }
    
    // Check action if provided
    if (data.action && expectedAction && data.action !== expectedAction) {
      logger.warn(`⚠️ [reCAPTCHA v3] Action mismatch: expected ${expectedAction}, got ${data.action}`);
      // Don't fail on action mismatch, just log it
    }
    
    logger.info('✅ [reCAPTCHA v3] Verification successful');
    return true;
    
  } catch (error) {
    logger.error('❌ [reCAPTCHA v3] Verification error:', error);
    // Allow requests in development if verification fails
    return process.env.NODE_ENV !== 'production';
  }
}