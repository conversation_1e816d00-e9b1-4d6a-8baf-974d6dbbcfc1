import { logger } from '../logger';
import { storage } from '../storage';
import { Form } from '@shared/schema';
import { Request } from 'express';
import { isAfter } from 'date-fns';

/**
 * Validates a form access token and returns the form if valid
 * 
 * @param token The access token to validate
 * @param req The Express request object (for tracking IP)
 * @returns The form if token is valid, null otherwise
 */
export async function validateFormAccessToken(
  token: string, 
  req: Request
): Promise<Form | null> {
  try {
    // Get form directly by access token
    const form = await storage.getFormByAccessToken(token);
    
    // If no form found with this token
    if (!form) {
      logger.warn(`No form found with access token: ${token}`);
      return null;
    }
    
    // Check if token is expired
    if (form.accessTokenExpiresAt && isAfter(new Date(), form.accessTokenExpiresAt)) {
      logger.warn(`Access token expired for form ID ${form.id} on ${form.accessTokenExpiresAt}`);
      return null;
    }
    
    // If form is already completed, don't allow access
    if (form.status === 'completed') {
      logger.warn(`Form ID ${form.id} is already completed, token access denied`);
      return null;
    }
    
    // Track access information
    const clientIp = req.ip || req.socket.remoteAddress || 'unknown';
    
    // Update form's last accessed information
    await storage.updateForm(form.id, {
      lastAccessedAt: new Date(),
      lastAccessedIp: clientIp
    });
    
    logger.info(`Valid token access for form ID ${form.id} from IP ${clientIp}`);
    return form;
  } catch (error) {
    logger.error('Error validating form access token:', error);
    return null;
  }
}

/**
 * Renews an expired form access token
 * 
 * @param formId The ID of the form to renew token for
 * @param assessmentId The ID of the assessment (for verification)
 * @returns New token if successful, null otherwise
 */
export async function renewFormAccessToken(
  formId: number,
  assessmentId: number
): Promise<string | null> {
  try {
    const form = await storage.getForm(formId);
    
    // Validate that form exists and belongs to the specified assessment
    if (!form || form.assessmentId !== assessmentId) {
      logger.warn(`Invalid form renewal request: form ID ${formId}, assessment ID ${assessmentId}`);
      return null;
    }
    
    // Don't renew tokens for completed forms
    if (form.status === 'completed') {
      logger.warn(`Cannot renew token for completed form ID ${formId}`);
      return null;
    }
    
    // Generate a new token with UUID and expiration date (30 days from now)
    const { v4: uuidv4 } = await import('uuid');
    const { addDays } = await import('date-fns');
    
    const newToken = uuidv4();
    const expirationDate = addDays(new Date(), 30);
    
    // Update the form with the new token
    await storage.updateForm(form.id, {
      accessToken: newToken,
      accessTokenExpiresAt: expirationDate
    });
    
    logger.info(`Renewed access token for form ID ${formId}`);
    return newToken;
  } catch (error) {
    logger.error('Error renewing form access token:', error);
    return null;
  }
}