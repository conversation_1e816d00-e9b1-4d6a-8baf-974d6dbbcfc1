import { logger } from '../logger';
import { v4 as uuidv4 } from 'uuid';
import { storage } from '../storage';
import { sendEmail } from './sendgrid';
import { getBaseUrl } from './url-helper';

/**
 * Generate a secure password reset token and store it in the database
 * 
 * @param userId The ID of the user requesting a password reset
 * @returns The generated password reset token or null if an error occurred
 */
export async function generatePasswordResetToken(userId: number): Promise<string | null> {
  try {
    // Generate a secure token
    const token = uuidv4();
    
    // Set expiration date (24 hours from now)
    const expires = new Date();
    expires.setHours(expires.getHours() + 24);
    
    // Store token in database
    const success = await storage.storePasswordResetToken(userId, token, expires);
    
    if (!success) {
      logger.error(`Failed to store password reset token for user ${userId}`);
      return null;
    }
    
    logger.info(`Generated password reset token for user ${userId}`);
    return token;
  } catch (error) {
    logger.error('Error generating password reset token:', error);
    return null;
  }
}

/**
 * Verify a password reset token
 * 
 * @param token The token to verify
 * @returns The user ID associated with the token if valid, null otherwise
 */
export async function verifyPasswordResetToken(token: string): Promise<number | null> {
  try {
    // Look up token in database
    const result = await storage.getPasswordResetToken(token);
    
    // If token doesn't exist or has expired, return null
    if (!result) {
      logger.warn(`Invalid or non-existent password reset token: ${token.substring(0, 8)}...`);
      return null;
    }
    
    if (result.expires < new Date()) {
      logger.warn(`Expired password reset token: ${token.substring(0, 8)}...`);
      return null;
    }
    
    logger.info(`Valid password reset token for user ${result.userId}`);
    return result.userId;
  } catch (error) {
    logger.error('Error verifying password reset token:', error);
    return null;
  }
}

/**
 * This function has been replaced with direct URL generation in the routes
 * to ensure accurate hostname/protocol detection.
 * 
 * @deprecated Use direct URL construction with req.protocol and req.get('host')
 * @param userId The ID of the user requesting a password reset
 * @returns The full password reset URL or null if an error occurred
 */
export async function generatePasswordResetLink(userId: number): Promise<string | null> {
  try {
    logger.warn("generatePasswordResetLink is deprecated - use direct URL construction instead");
    const token = await generatePasswordResetToken(userId);
    if (!token) {
      return null;
    }
    
    // Generate URL using base URL helper to account for different environments
    const baseUrl = getBaseUrl();
    const resetUrl = `${baseUrl}/reset-password/${token}`;
    
    return resetUrl;
  } catch (error) {
    logger.error('Error generating password reset link:', error);
    return null;
  }
}

/**
 * @deprecated Use sendWelcomeEmailWithPasswordResetFromRequest instead
 * Legacy function to send a welcome email with password reset instructions
 * @param userId The ID of the user to send the welcome email to
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendWelcomeEmailWithPasswordReset(userId: number): Promise<boolean> {
  logger.warn("sendWelcomeEmailWithPasswordReset is deprecated. Use sendWelcomeEmailWithPasswordResetFromRequest instead.");
  try {
    // Get user's information
    const user = await storage.getUser(userId);
    if (!user) {
      logger.error(`User with ID ${userId} not found`);
      return false;
    }
    
    // Generate password reset link - NOTE: This may use localhost URLs
    const resetLink = await generatePasswordResetLink(userId);
    if (!resetLink) {
      logger.error(`Failed to generate password reset link for user ${userId}`);
      return false;
    }
    
    return await sendWelcomeEmailCore(user, resetLink);
  } catch (error) {
    logger.error('Error sending welcome email with password reset:', error);
    return false;
  }
}

/**
 * Send a welcome email with password reset instructions to a new user
 * This version uses the request object to determine the correct base URL
 * 
 * @param userId The ID of the user to send the welcome email to
 * @param req The Express request object, used to get the correct host/protocol
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendWelcomeEmailWithPasswordResetFromRequest(
  userId: number, 
  req: any
): Promise<boolean> {
  try {
    // Get user's information
    const user = await storage.getUser(userId);
    if (!user) {
      logger.error(`User with ID ${userId} not found`);
      return false;
    }
    
    // Generate token first
    const token = await generatePasswordResetToken(userId);
    if (!token) {
      logger.error(`Failed to generate password reset token for user ${userId}`);
      return false;
    }
    
    // Generate URL using request object's protocol and host
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const resetLink = `${baseUrl}/reset-password/${token}`;
    
    logger.info(`Generated password reset link for user ${userId} using URL: ${resetLink}`);
    
    return await sendWelcomeEmailCore(user, resetLink);
  } catch (error) {
    logger.error('Error sending welcome email with password reset:', error);
    return false;
  }
}

/**
 * Core function to send welcome email with reset link
 * Extracted to avoid duplication between the two public functions
 */
async function sendWelcomeEmailCore(user: any, resetLink: string): Promise<boolean> {
  // Get role-specific content
  let roleSpecificContent = '';
  
  // Customize message based on user role
  switch(user.role) {
    case 'school':
      roleSpecificContent = `
        <p>Your school has been added to our system as part of an assessment referral. This account will allow you to:</p>
        <ul>
          <li>Access information about assessments related to your school</li>
          <li>Complete required pre-assessment forms</li>
          <li>Communicate with assessors during the assessment process</li>
        </ul>
      `;
      break;
    case 'parent':
      roleSpecificContent = `
        <p>This account has been created because you are the parent/guardian of a student under 16 who has been referred for an assessment. This account will allow you to:</p>
        <ul>
          <li>Complete parent/guardian questionnaires</li>
          <li>Track the progress of your child's assessment</li>
          <li>Communicate with assessors</li>
          <li>Access reports and recommendations when completed</li>
        </ul>
      `;
      break;
    case 'assessee':
      roleSpecificContent = `
        <p>This account has been created because you have been referred for an assessment. This account will allow you to:</p>
        <ul>
          <li>Complete your pre-assessment questionnaire</li>
          <li>Track the progress of your assessment</li>
          <li>Communicate with your assessor</li>
          <li>Access your reports and recommendations when completed</li>
        </ul>
      `;
      break;
    default:
      roleSpecificContent = `
        <p>This account will allow you to access and interact with assessments on the NeuroElevate platform.</p>
      `;
  }
  
  // Send email with password reset link
  const emailSent = await sendEmail({
    to: user.email,
    subject: 'Welcome to NeuroElevate Assessment Platform - Account Setup',
    templateData: {
      fullName: user.fullName,
      username: user.username,
      resetLink,
      role: user.role
    },
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #4a6cf7;">Welcome to NeuroElevate</h1>
        </div>
        
        <p>Hello ${user.fullName},</p>
        
        <p>Welcome to the NeuroElevate Assessment Platform! <strong>An account has been automatically created for you</strong> as a <strong>${user.role}</strong>.</p>
        
        ${roleSpecificContent}
        
        <div style="background-color: #f8fafc; border-radius: 8px; padding: 15px; margin: 20px 0; border: 1px solid #e2e8f0;">
          <h3 style="margin-top: 0; color: #4a6cf7;">Getting Started - First Time Access</h3>
          <p><strong>Step 1:</strong> Click the button below to set your password</p>
          <p><strong>Step 2:</strong> Create a secure password</p>
          <p><strong>Step 3:</strong> Once set, you can log in anytime using your email address (${user.email}) and your new password</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetLink}" style="background-color: #4a6cf7; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            Set Up Your Password
          </a>
        </div>
        
        <p>This link is valid for 24 hours. If you didn't expect to receive this email, please contact us for assistance.</p>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="font-size: 14px; color: #666;">If the button above doesn't work, copy and paste this URL into your browser:</p>
          <p style="font-size: 14px; color: #666; word-break: break-all;">${resetLink}</p>
        </div>
      </div>
    `
  });
  
  if (emailSent) {
    logger.info(`Welcome email with password reset instructions sent to ${user.email}`);
  } else {
    logger.error(`Failed to send welcome email to ${user.email}`);
  }
  
  return emailSent;
}

/**
 * Reset a user's password using a valid token
 * 
 * @param token The password reset token
 * @param newPassword The new password to set
 * @returns True if the password was reset successfully, false otherwise
 */
export async function resetPassword(token: string, newPassword: string): Promise<boolean> {
  try {
    // Verify the token is valid
    const userId = await verifyPasswordResetToken(token);
    if (!userId) {
      logger.warn('Cannot reset password: Invalid or expired token');
      return false;
    }
    
    // Update the user's password
    const success = await storage.updateUserPassword(userId, newPassword);
    if (!success) {
      logger.error(`Failed to update password for user ${userId}`);
      return false;
    }
    
    // Invalidate the token to prevent reuse
    await storage.invalidatePasswordResetToken(token);
    
    logger.info(`Password reset successful for user ${userId}`);
    return true;
  } catch (error) {
    logger.error('Error resetting password:', error);
    return false;
  }
}