import { logger } from '../logger';
import { sendEmail, EmailOptions } from './sendgrid';
import { createEmailTemplate } from './email-template-base';

/**
 * Creates a password reset email using our standard template
 * @param email Recipient email address
 * @param name Recipient name
 * @param resetLink Password reset link
 * @returns EmailOptions object
 */
function createResetPasswordEmail(email: string, name: string, resetLink: string): EmailOptions {
  const subject = 'Reset Your NeuroElevate Platform Password';
  
  const content = `
    <p>Hello ${name},</p>
    
    <p>We received a request to reset your password for the NeuroElevate Assessment Platform.</p>
    
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 30px 0;">
      <tr>
        <td align="center">
          <table role="presentation" border="0" cellpadding="0" cellspacing="0">
            <tr>
              <td>
                <a href="${resetLink}" target="_blank" style="display: inline-block; padding: 12px 24px; background-color: #4a6cf7; color: #ffffff; text-decoration: none; border-radius: 4px; font-weight: 600;">Reset Password</a>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
    
    <p>If you didn't request a password reset, you can ignore this email - your password will not be changed.</p>
    
    <p>For security reasons, this password reset link will expire in 24 hours.</p>
    
    <div style="margin-top: 30px; padding: 15px; background-color: #f5f7fb; border-radius: 4px;">
      <p style="margin: 0; font-size: 14px; color: #64748b;">If the button above doesn't work, copy and paste this URL into your browser:</p>
      <p style="margin: 10px 0 0 0; word-break: break-all; font-size: 14px;">
        <a href="${resetLink}" style="color: #4a6cf7; text-decoration: none;">${resetLink}</a>
      </p>
    </div>
  `;
  
  // Use the object parameter pattern instead of the string parameters
  const html = createEmailTemplate({
    title: subject,
    content: content,
    recipientName: name
  });
  
  return {
    to: email,
    subject: subject,
    html: html
  };
}

/**
 * Send a test email with basic styling for diagnostic purposes
 * @param email Email address to send the test to
 * @returns Promise<boolean> true if successful
 */
export async function testBasicTemplateEmail(email: string): Promise<boolean> {
  try {
    logger.info(`Sending basic test email to ${email}`);
    
    const currentDate = new Date().toLocaleString();
    
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px; background-color: #f9f9f9;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #4a6cf7; margin-bottom: 10px;">NeuroElevate Assessment Platform</h1>
          <p style="color: #666; font-size: 16px; margin: 0;">Test Email</p>
        </div>
        
        <div style="background-color: #fff; padding: 20px; border-radius: 4px; margin-bottom: 20px; border-left: 4px solid #4a6cf7;">
          <p style="margin-top: 0; color: #333; font-size: 16px;">This is a test email from the NeuroElevate platform.</p>
          <p style="color: #333; font-size: 16px;">If you received this email, the email delivery system is working correctly.</p>
          <p style="color: #333; font-size: 16px; margin-bottom: 0;">Time sent: ${currentDate}</p>
        </div>
        
        <div style="font-size: 12px; color: #999; text-align: center; margin-top: 20px;">
          <p>This is an automated message. Please do not reply to this email.</p>
          <p>© ${new Date().getFullYear()} NeuroElevate Assessment Platform. All rights reserved.</p>
        </div>
      </div>
    `;
    
    const emailOptions = {
      to: email,
      subject: 'NeuroElevate Platform - Email System Test',
      html: htmlContent,
    };

    return await sendEmail(emailOptions);
  } catch (error) {
    logger.error('Error sending test email:', error);
    return false;
  }
}

/**
 * Send a test password reset email
 * @param email Email address to send the test to
 * @param baseUrl Base URL of the application
 * @returns Promise<boolean> true if successful
 */
export async function testPasswordResetEmail(email: string, baseUrl: string): Promise<boolean> {
  try {
    logger.info(`Sending test password reset email to ${email}`);
    
    // Generate a fake reset URL for testing purposes
    const resetLink = `${baseUrl}/reset-password/test-token-not-valid-${Date.now()}`;
    
    // Use our actual password reset email template
    const emailOptions = createResetPasswordEmail(email, 'Test User', resetLink);
    
    logger.info('Sending password reset email with options:', {
      to: emailOptions.to,
      subject: emailOptions.subject,
    });
    
    return await sendEmail(emailOptions);
  } catch (error) {
    logger.error('Error sending password reset test email:', error);
    return false;
  }
}

/**
 * Send a direct email without using templates
 * @param email Email address to send to
 * @returns Promise<boolean> true if successful
 */
export async function testDirectEmail(email: string): Promise<boolean> {
  try {
    logger.info(`Sending direct test email to ${email}`);
    
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4a6cf7;">NeuroElevate Test Email</h1>
        <p>This is a direct test email without using a template system.</p>
        <p>Time sent: ${new Date().toLocaleString()}</p>
        <p style="color: #666; font-size: 12px; margin-top: 30px;">This is a test email. Please ignore.</p>
      </div>
    `;
    
    return await sendEmail({
      to: email,
      subject: 'NeuroElevate Direct Email Test',
      html: htmlContent
    });
  } catch (error) {
    logger.error('Error sending direct test email:', error);
    return false;
  }
}