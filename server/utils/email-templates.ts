import { format } from 'date-fns';
import { EmailOptions } from './sendgrid';
import { createEmailTemplate, createPlainTextEmail } from './email-template-base';

/**
 * Creates a test email to verify the email system is working properly
 * @param recipientEmail Email address to send to
 * @returns EmailOptions object
 */
export function createTestEmail(recipientEmail: string): EmailOptions {
  // Format current date for display in the email
  const currentDate = format(new Date(), 'PPpp');

  // Create the content for the test email
  const content = `
    <p>This is a test email from the NeuroElevate Assessment Platform.</p>
    
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-bottom: 20px; border-collapse: collapse; background-color: #f3f4fe; border-radius: 4px;">
      <tr>
        <td style="padding: 15px; border-left: 4px solid #10b981;">
          <p style="margin: 0; font-weight: 600; color: #10b981;">System Status</p>
          <p style="margin: 5px 0 0 0;">Email Delivery: <span style="color: #22c55e; font-weight: 600;">✓ Working Correctly</span></p>
          <p style="margin: 5px 0 0 0;">Sent on: ${currentDate}</p>
        </td>
      </tr>
    </table>

    <p style="margin: 0 0 20px 0; line-height: 1.5;">This confirms that your SendGrid integration is working properly and the platform can successfully send email notifications.</p>
  `;

  // Create the email template options
  const templateOptions = {
    title: 'Email System Test',
    preheader: 'This is a test of the NeuroElevate email system',
    content,
    footerText: 'This is an automated message. Please do not reply to this email.'
  };

  // Generate the HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  // Email options
  return {
    to: recipientEmail,
    from: {
      email: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      name: process.env.SENDGRID_FROM_NAME || 'NeuroElevate Platform'
    },
    subject: 'NeuroElevate Email System Test',
    text,
    html
  };
}

/**
 * Creates a password reset email with a secure link
 * @param email Email address to send to
 * @param fullName User's full name for personalization
 * @param resetLink The password reset link
 * @returns EmailOptions object
 */
export function createPasswordResetEmail(email: string, fullName: string | undefined, resetLink: string): EmailOptions {
  // Create the content for the password reset email
  const content = `
    <p>We received a request to reset your password for your NeuroElevate account. If you did not make this request, you can safely ignore this email.</p>
    
    <div style="padding: 15px; background-color: #f3f4f6; border-left: 4px solid #10b981; margin: 20px 0;">
      <p style="margin: 0; color: #4b5563;">
        This link will expire in <strong>24 hours</strong> for security reasons.
      </p>
    </div>
    
    <p style="margin: 15px 0;">To reset your password, click the button below:</p>
  `;

  // Create the email template options
  const templateOptions = {
    title: 'Reset Your Password',
    preheader: 'Use this link to reset your NeuroElevate account password',
    recipientName: fullName || 'there',
    content,
    buttonText: 'Reset Your Password',
    buttonUrl: resetLink,
    footerText: 'If you did not request this password reset, please ignore this email or contact support if you have concerns.'
  };

  // Generate the HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  // Email options
  return {
    to: email,
    from: {
      email: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      name: process.env.SENDGRID_FROM_NAME || 'NeuroElevate Platform'
    },
    subject: 'NeuroElevate - Reset Your Password',
    text,
    html
  };
}