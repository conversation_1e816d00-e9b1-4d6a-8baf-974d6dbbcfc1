import { logger } from '../logger';
import { sendEmail } from './sendgrid';
import { EmailOptions } from './sendgrid';
import { createEmailTemplate, createPlainTextEmail } from './email-template-base';
import { Form } from '@shared/schema';

interface AssessmentStatusUpdate {
  assessmentId: number;
  assesseeName: string;
  oldStatus: string;
  newStatus: string;
  updatedBy: string;
  notes?: string;
  formLinks?: { [key: string]: string }; // Add form links
}

/**
 * Creates and sends a notification email when an assessment status is updated
 * 
 * @param recipientEmail The email address to send the notification to
 * @param update Status update information
 * @param isUniversity Whether this email is for university/school or student/parent
 * @returns Success status
 */
export async function sendAssessmentStatusEmail(
  recipientEmail: string, 
  update: AssessmentStatusUpdate,
  isUniversity: boolean = false
): Promise<boolean> {
  const fromEmail = process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.SENDGRID_FROM_NAME || 'NeuroElevate Assessment Platform';
  
  const subject = isUniversity 
    ? `Referral Status Update: ${update.assesseeName}` 
    : `Assessment Status Update: ${update.assesseeName}`;
  
  // Format status for display
  const formatStatus = (status: string): string => {
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  
  const oldStatusDisplay = formatStatus(update.oldStatus);
  const newStatusDisplay = formatStatus(update.newStatus);
  
  // Create different content based on recipient type
  let content = '';
  
  if (isUniversity) {
    // University/referring party specific content
    content = `
      <p>Hi there,</p>
      <p>Thank you for your referral for ${update.assesseeName}. The status has been updated to <strong>${newStatusDisplay}</strong>.</p>
      
      <div style="background-color: #f3f4f6; border-radius: 4px; padding: 20px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #10b981;">Referral Update Details</h3>
        <p><strong>Assessment ID:</strong> ${update.assessmentId}</p>
        <p><strong>Student Name:</strong> ${update.assesseeName}</p>
        <p><strong>Previous Status:</strong> ${oldStatusDisplay}</p>
        <p><strong>New Status:</strong> <span style="color: #10b981; font-weight: 600;">${newStatusDisplay}</span></p>
        <p><strong>Updated By:</strong> ${update.updatedBy}</p>
      </div>
    `;
  } else {
    // Standard content for student/parent
    content = `
      <p>Hi ${update.assesseeName.split(' ')[0]},</p>
      <p>Your assessment status has been updated to <strong>${newStatusDisplay}</strong>.</p>
      
      <div style="background-color: #f3f4f6; border-radius: 4px; padding: 20px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #10b981;">Assessment Update Details</h3>
        <p><strong>Assessment ID:</strong> ${update.assessmentId}</p>
        <p><strong>Previous Status:</strong> ${oldStatusDisplay}</p>
        <p><strong>New Status:</strong> <span style="color: #10b981; font-weight: 600;">${newStatusDisplay}</span></p>
        <p><strong>Updated By:</strong> ${update.updatedBy}</p>
      </div>
    `;
  }
  
  // Add notes section if provided
  if (update.notes) {
    content += `
      <div style="background-color: #f9fafb; border-left: 4px solid #6b7280; padding: 15px; margin: 20px 0;">
        <h4 style="margin-top: 0; color: #4b5563;">Notes:</h4>
        <p style="margin-bottom: 0;">${update.notes}</p>
      </div>
    `;
  }
  
  // Process form links if it's a pre-assessment status update
  if (update.newStatus === 'pre_assessment' && update.formLinks && typeof update.formLinks === 'object') {
    // Create a fresh, deep-copied object for form links
    const formLinksForEmail = JSON.parse(JSON.stringify(update.formLinks || {}));
    
    if (Object.keys(formLinksForEmail).length > 0) {
      if (isUniversity) {
        // University/school recipient - show form names without links
        content += `
          <div style="background-color: #eff6ff; border-left: 4px solid #3b82f6; padding: 15px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #1e40af;">Assessment Forms Information</h4>
            <p>The student has been sent the following forms to complete:</p>
            <ul style="margin-bottom: 10px;">
        `;
        
        // Add form names (no links) for university recipients
        Object.entries(formLinksForEmail).forEach(([formType]) => {
          let formName = "Pre-Assessment Form";
          
          // Set appropriate form name based on type
          if (formType === 'school') {
            formName = "School Pre-Assessment Questionnaire";
          } else if (formType === 'parent_under_16') {
            formName = "Parent/Guardian Pre-Assessment Questionnaire";
          } else if (formType === 'assessee_over_16') {
            formName = "Assessee Pre-Assessment Questionnaire";
          }
          
          content += `
            <li style="margin-bottom: 8px;">${formName}</li>
          `;
        });
        
        content += `
            </ul>
            <p style="font-size: 0.9em; color: #6b7280; margin-bottom: 0;">
              You will be notified when the assessment progresses to the next stage.
            </p>
          </div>
        `;
      } else {
        // Student/parent recipient - show clickable form links
        content += `
          <div style="background-color: #eff6ff; border-left: 4px solid #3b82f6; padding: 15px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #1e40af;">Complete Your Pre-Assessment Forms</h4>
            <p style="font-weight: 500; margin-bottom: 10px;">You have TWO OPTIONS to complete your forms:</p>
            
            <div style="background-color: #ffffff; border: 1px solid #dbeafe; border-radius: 6px; padding: 12px; margin-bottom: 15px;">
              <p style="font-weight: 600; color: #1e40af; margin-top: 0; margin-bottom: 8px;">Option 1: Direct Form Links (Fastest Method)</p>
              <p style="margin-top: 0; margin-bottom: 10px;">Click the secure links below to access forms directly without logging in:</p>
              <ul style="margin-bottom: 10px;">
        `;
        
        // Add form links for student/parent recipients
        Object.entries(formLinksForEmail).forEach(([formType, link]) => {
          let formName = "Pre-Assessment Form";
          
          // Set appropriate form name based on type
          if (formType === 'school') {
            formName = "School Pre-Assessment Questionnaire";
          } else if (formType === 'parent_under_16') {
            formName = "Parent/Guardian Pre-Assessment Questionnaire";
          } else if (formType === 'assessee_over_16') {
            formName = "Assessee Pre-Assessment Questionnaire";
          }
          
          content += `
            <li style="margin-bottom: 8px;">
              <a href="${link}" style="color: #2563eb; font-weight: 500; text-decoration: underline;">
                ${formName}
              </a>
              <span style="display: block; font-size: 0.9em; color: #4b5563; margin-top: 2px;">
                This link allows direct secure access without logging in. No account needed.
              </span>
            </li>
          `;
        });
        
        content += `
              </ul>
              <p style="font-size: 0.9em; color: #6b7280; margin-bottom: 0;">
                These secure links will expire in 30 days. No login required.
              </p>
            </div>
            
            <div style="background-color: #ffffff; border: 1px solid #dbeafe; border-radius: 6px; padding: 12px;">
              <p style="font-weight: 600; color: #1e40af; margin-top: 0; margin-bottom: 8px;">
                Option 2: Login To Your Account
              </p>
              <p style="margin-top: 0; margin-bottom: 10px;">
                You can also access forms by logging into your NeuroElevate account:
              </p>
              <ol style="margin-bottom: 10px;">
                <li style="margin-bottom: 5px;">Click the "View Assessment" button below</li>
                <li style="margin-bottom: 5px;">Navigate to the "Forms" section of the assessment</li>
                <li style="margin-bottom: 0px;">Complete each required form</li>
              </ol>
              <p style="font-size: 0.9em; color: #6b7280; margin-bottom: 0;">
                This method requires you to set up your account password first.
              </p>
            </div>
          </div>
        `;
      }
    } else if (!isUniversity) {
      // No form links but student/parent recipient - show message about missing links
      content += `
        <div style="background-color: #eff6ff; border-left: 4px solid #3b82f6; padding: 15px; margin: 20px 0;">
          <h4 style="margin-top: 0; color: #1e40af;">Complete Your Pre-Assessment Forms</h4>
          <p style="font-weight: 500; margin-bottom: 10px;">You have TWO OPTIONS to complete your forms:</p>
          
          <div style="background-color: #ffffff; border: 1px solid #dbeafe; border-radius: 6px; padding: 12px; margin-bottom: 15px;">
            <p style="font-weight: 600; color: #1e40af; margin-top: 0; margin-bottom: 8px;">
              Option 1: Direct Form Links (Fastest Method)
            </p>
            <p style="margin-top: 0; margin-bottom: 10px;">
              The form links are being prepared and will be available shortly. 
              Please try Option 2 below or check back later.
            </p>
          </div>
          
          <div style="background-color: #ffffff; border: 1px solid #dbeafe; border-radius: 6px; padding: 12px;">
            <p style="font-weight: 600; color: #1e40af; margin-top: 0; margin-bottom: 8px;">
              Option 2: Login To Your Account
            </p>
            <p style="margin-top: 0; margin-bottom: 10px;">
              You can access forms by logging into your NeuroElevate account:
            </p>
            <ol style="margin-bottom: 10px;">
              <li style="margin-bottom: 5px;">Click the "View Assessment" button below</li>
              <li style="margin-bottom: 5px;">Navigate to the "Forms" section of the assessment</li>
              <li style="margin-bottom: 0px;">Complete each required form</li>
            </ol>
            <p style="font-size: 0.9em; color: #6b7280; margin-bottom: 0;">
              This method requires you to set up your account password first.
            </p>
          </div>
        </div>
      `;
    }
    
    // Add account information section for pre-assessment status
    // This is primarily for newly created accounts (schools, parents, assessees 16+)
    if (!isUniversity) {
      content += `
        <div style="background-color: #ecfdf5; border-left: 4px solid #10b981; padding: 15px; margin: 20px 0;">
          <h4 style="margin-top: 0; color: #047857;">Important: Your NeuroElevate Account</h4>
          <p>An account has been automatically created for you on the NeuroElevate platform.</p>
          <p><strong>Your login email:</strong> ${recipientEmail}</p>
          <p>To access the platform for the first time, please:</p>
          <ol>
            <li>Go to the login page by clicking the button below</li>
            <li>Click "Forgot Password" on the login screen</li>
            <li>Enter your email address (${recipientEmail})</li>
            <li>Follow the instructions in the email you receive to set your password</li>
          </ol>
          <p>After setting your password, you'll have full access to view and manage assessment information.</p>
        </div>
      `;
    }
  }
  
  // Add final paragraph
  content += `
    <p>Please log in to the NeuroElevate platform to view the complete assessment details.</p>
  `;

  // Create the email using our template with a custom title based on recipient type
  const templateOptions = {
    title: isUniversity ? 'Referral Status Update' : 'Assessment Status Update',
    preheader: `Assessment for ${update.assesseeName} has been updated to ${newStatusDisplay}`,
    recipientName: isUniversity ? 'there' : update.assesseeName.split(' ')[0],
    content,
    buttonText: 'View Assessment',
    buttonUrl: `${process.env.APP_URL || 'https://neuroelevate.replit.app'}/assessments/${update.assessmentId}`
  };

  // Generate HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  try {
    return await sendEmail({
      to: recipientEmail,
      from: {
        email: fromEmail,
        name: fromName
      },
      subject,
      html,
      text
    });
  } catch (error) {
    logger.error('Error sending assessment status email:', error);
    return false;
  }
}