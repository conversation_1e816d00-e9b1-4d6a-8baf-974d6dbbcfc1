import { logger } from '../logger';
import { v4 as uuidv4 } from 'uuid';
import { storage } from '../storage';
import { sendWelcomeEmailWithPasswordReset, sendWelcomeEmailWithPasswordResetFromRequest } from './password-reset';
import { Request } from 'express';

/**
 * Checks if an assessee is over 16 years old and creates a user account if needed
 * 
 * @param assesseeId The ID of the assessee to check
 * @param req Optional Express request object - needed for generating correct URLs in emails
 * @returns The user ID if created or found, null otherwise
 */
export async function createUserForAssesseeIfOver16(
  assesseeId: number, 
  req?: Request
): Promise<number | null> {
  try {
    // Get the assessee record
    const assessee = await storage.getAssessee(assesseeId);
    if (!assessee) {
      logger.error(`Assessee with ID ${assesseeId} not found`);
      return null;
    }
    
    // If the assessee already has a user account or no email, skip
    if (assessee.userId || !assessee.email) {
      return assessee.userId;
    }
    
    // Calculate age
    const currentDate = new Date();
    const birthDate = new Date(assessee.dateOfBirth);
    const ageInMilliseconds = currentDate.getTime() - birthDate.getTime();
    const ageInYears = ageInMilliseconds / (1000 * 60 * 60 * 24 * 365.25);
    const isOver16 = ageInYears >= 16;
    
    if (!isOver16) {
      // Under 16, no user account needed
      logger.info(`Assessee ${assessee.fullName} is under 16, no user account created`);
      return null;
    }
    
    // Check if a user with this email already exists
    let assesseeUser = await storage.getUserByEmail(assessee.email);
    
    if (!assesseeUser) {
      // Create a new user account
      assesseeUser = await storage.createUser({
        username: assessee.email,
        email: assessee.email,
        password: uuidv4(), // Generate a random password - they'll need to reset it
        fullName: assessee.fullName,
        role: 'assessee',
        phone: assessee.phone || null,
        organization: null
      });
      
      logger.info(`Created new assessee user: ${assesseeUser.fullName} with ID ${assesseeUser.id}`);
      
      // Send welcome email with password reset instructions
      try {
        let emailSent = false;
        
        if (req) {
          // Use request-based version for accurate URL generation
          emailSent = await sendWelcomeEmailWithPasswordResetFromRequest(assesseeUser.id, req);
          logger.info(`Using request-based welcome email to ${assesseeUser.email}`);
        } else {
          // Fallback to legacy version if no request object available
          emailSent = await sendWelcomeEmailWithPasswordReset(assesseeUser.id);
          logger.warn(`Falling back to legacy welcome email to ${assesseeUser.email} - URLs may be incorrect!`);
        }
        
        if (emailSent) {
          logger.info(`Welcome email with password reset link sent to ${assesseeUser.email}`);
        } else {
          logger.warn(`Failed to send welcome email to ${assesseeUser.email}`);
        }
      } catch (emailError) {
        logger.error(`Error sending welcome email to ${assesseeUser.email}:`, emailError);
      }
    } else {
      logger.info(`Found existing user for assessee: ${assesseeUser.fullName} with ID ${assesseeUser.id}`);
    }
    
    // Link the assessee record to the user account if not already linked
    if (!assessee.userId) {
      await storage.updateAssessee(assessee.id, {
        userId: assesseeUser.id
      });
      
      logger.info(`Linked assessee record ${assessee.id} to user account ${assesseeUser.id}`);
    }
    
    return assesseeUser.id;
  } catch (error) {
    logger.error('Error creating user for assessee:', error);
    return null;
  }
}