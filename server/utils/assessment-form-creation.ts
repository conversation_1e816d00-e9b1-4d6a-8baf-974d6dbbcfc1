import { logger } from '../logger';
import { Form, Assessment, Assessee, User, School } from "@shared/schema";
import { storage } from "../storage";
import { sendFormNotificationEmail } from "./form-notification-email";
import { getBaseUrl } from "./url-helper";
import { Request } from "express";
import { ZodError } from "zod";
import { v4 as uuidv4 } from 'uuid';
import { addDays } from 'date-fns';

/**
 * Creates the appropriate forms for an assessment based on the assessee's age
 * and sends notifications to the relevant parties
 * 
 * @param assessmentId The ID of the assessment to create forms for
 * @param req Express request object (needed for generating URLs)
 * @returns Object with creation status
 */
export async function createFormsForAssessment(
  assessmentId: number,
  req: Request
): Promise<{ success: boolean; message: string; createdForms: Form[] }> {
  try {
    // Get the assessment data
    const assessment = await storage.getAssessment(assessmentId);
    if (!assessment) {
      return { 
        success: false, 
        message: "Assessment not found", 
        createdForms: [] 
      };
    }

    // Get the assessee data
    const assessee = await storage.getAssessee(assessment.assesseeId);
    if (!assessee) {
      return { 
        success: false, 
        message: "Assessee not found", 
        createdForms: [] 
      };
    }

    // Calculate age
    const currentDate = new Date();
    const birthDate = new Date(assessee.dateOfBirth);
    const ageInMilliseconds = currentDate.getTime() - birthDate.getTime();
    const ageInYears = ageInMilliseconds / (1000 * 60 * 60 * 24 * 365.25);
    const isUnder16 = ageInYears < 16;

    const createdForms: Form[] = [];
    const baseUrl = getBaseUrl(req);
    
    // Check for existing forms to prevent duplication and database errors
    try {
      const existingForms = await storage.getFormsByAssessment(assessmentId);
      logger.info(`Found ${existingForms.length} existing forms for assessment ID ${assessmentId}`);
      
      // If forms already exist, return them instead of creating new ones
      if (existingForms.length > 0) {
        return {
          success: true,
          message: `Found ${existingForms.length} existing forms for assessment`,
          createdForms: existingForms
        };
      }
    } catch (existingCheckError) {
      logger.warn("Error checking existing forms, will continue with form creation:", existingCheckError);
      // Continue with creation, since we're handling potential duplicates in each creation function
    }

    // For all assessments, create a School form
    if (assessee.schoolId) {
      try {
        const school = await storage.getSchool(assessee.schoolId);
        
        if (school) {
          // Create the school form
          const schoolForm = await createSchoolForm(assessment, assessee, school, baseUrl);
          if (schoolForm) {
            createdForms.push(schoolForm);
          }
        }
      } catch (schoolFormError) {
        logger.error("Error creating school form:", schoolFormError);
        // Continue with other forms
      }
    }

    // Create appropriate forms based on age
    if (isUnder16) {
      // For under 16, create a Parent/Guardian form
      if (assessee.parentId) {
        try {
          const parent = await storage.getUser(assessee.parentId);
          
          if (parent) {
            // Create the parent form
            const parentForm = await createParentForm(assessment, assessee, parent, baseUrl);
            if (parentForm) {
              createdForms.push(parentForm);
            }
          }
        } catch (parentFormError) {
          logger.error("Error creating parent form:", parentFormError);
          // Continue with other forms
        }
      }
    } else {
      // For 16+, create an Assessee form
      // Check if assessee has a user account
      if (assessee.userId) {
        try {
          const assesseeUser = await storage.getUser(assessee.userId);
          
          if (assesseeUser) {
            // Create the assessee form
            const assesseeForm = await createAssesseeForm(assessment, assessee, assesseeUser, baseUrl);
            if (assesseeForm) {
              createdForms.push(assesseeForm);
            }
          }
        } catch (assesseeFormError) {
          logger.error("Error creating assessee form:", assesseeFormError);
          // Continue with other forms
        }
      }
    }

    // Consider it a success if we created at least one form, or all requested forms
    const expectedFormCount = isUnder16 ? 
      (assessee.schoolId ? 2 : 1) : // For under 16: school + parent forms
      (assessee.schoolId ? 2 : 1);  // For 16+: school + assessee forms
    
    const allFormsCreated = createdForms.length === expectedFormCount;
    const someFormsCreated = createdForms.length > 0;
    
    if (allFormsCreated) {
      return {
        success: true,
        message: `Successfully created all ${createdForms.length} forms for assessment`,
        createdForms
      };
    } else if (someFormsCreated) {
      return {
        success: true,
        message: `Created ${createdForms.length} out of ${expectedFormCount} expected forms for assessment`,
        createdForms
      };
    } else {
      return {
        success: false,
        message: `Failed to create any forms for assessment`,
        createdForms: []
      };
    }
  } catch (error: unknown) {
    logger.error("Error creating forms for assessment:", error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      message: `Error creating forms: ${errorMessage}`,
      createdForms: []
    };
  }
}

/**
 * Creates a school form and sends notification
 */
async function createSchoolForm(
  assessment: Assessment,
  assessee: Assessee,
  school: School,
  baseUrl: string
): Promise<Form | null> {
  try {
    // Check if a school form already exists for this assessment
    const existingForms = await storage.getFormsByAssessment(assessment.id);
    const existingSchoolForm = existingForms.find(form => form.formType === 'school');
    
    if (existingSchoolForm) {
      logger.info(`School form already exists for assessment ${assessment.id}`);
      return existingSchoolForm;
    }
    
    // Generate a secure access token and set expiration (30 days from now)
    const accessToken = uuidv4();
    const expirationDate = addDays(new Date(), 30);
    
    // Create the school form with secure token
    const schoolForm = await storage.createForm({
      assessmentId: assessment.id,
      formType: 'school',
      status: 'not_started',
      completedById: null,
      completedAt: null,
      data: null,
      accessToken,
      accessTokenExpiresAt: expirationDate
    });

    logger.info(`Created school form with ID ${schoolForm.id} for assessment ${assessment.id}`);

    // Find the school user to send notification
    if (school.userId) {
      const schoolUser = await storage.getUser(school.userId);
      
      if (schoolUser?.email) {
        // Create the secure form link using token
        const formLink = `${baseUrl}/forms/access/${schoolForm.accessToken}`;
        
        // Send combined notification with account information
        await sendFormNotificationEmail(
          schoolUser.email,
          {
            id: schoolForm.id,
            formType: 'school',
            formName: 'School Pre-Assessment Questionnaire',
            assessmentId: assessment.id,
            assesseeFullName: assessee.fullName
          },
          formLink,
          {
            accountEmail: schoolUser.email,
            hasAccount: true,
            appUrl: baseUrl
          }
        );
        
        logger.info(`Sent school form notification to ${schoolUser.email} with account info`);
      }
    }

    return schoolForm;
  } catch (error: unknown) {
    logger.error("Error creating school form:", error);
    return null;
  }
}

/**
 * Creates a parent form and sends notification
 */
async function createParentForm(
  assessment: Assessment,
  assessee: Assessee,
  parent: User,
  baseUrl: string
): Promise<Form | null> {
  try {
    // Check if a parent form already exists for this assessment
    const existingForms = await storage.getFormsByAssessment(assessment.id);
    const existingParentForm = existingForms.find(form => form.formType === 'parent_under_16');
    
    if (existingParentForm) {
      logger.info(`Parent form already exists for assessment ${assessment.id}`);
      return existingParentForm;
    }
    
    // Generate a secure access token and set expiration (30 days from now)
    const accessToken = uuidv4();
    const expirationDate = addDays(new Date(), 30);
    
    // Create the parent form with secure token
    const parentForm = await storage.createForm({
      assessmentId: assessment.id,
      formType: 'parent_under_16',
      status: 'not_started',
      completedById: null,
      completedAt: null,
      data: null,
      accessToken,
      accessTokenExpiresAt: expirationDate
    });

    logger.info(`Created parent form with ID ${parentForm.id} for assessment ${assessment.id}`);

    // Send notification to parent
    if (parent.email) {
      // Create the secure form link using token
      const formLink = `${baseUrl}/forms/access/${parentForm.accessToken}`;
      
      // Send combined notification with account information
      await sendFormNotificationEmail(
        parent.email,
        {
          id: parentForm.id,
          formType: 'parent_under_16',
          formName: 'Parent/Guardian Pre-Assessment Questionnaire',
          assessmentId: assessment.id,
          assesseeFullName: assessee.fullName
        },
        formLink,
        {
          accountEmail: parent.email,
          hasAccount: true,
          appUrl: baseUrl
        }
      );
      
      logger.info(`Sent parent form notification to ${parent.email} with account info`);
    }

    return parentForm;
  } catch (error: unknown) {
    logger.error("Error creating parent form:", error);
    return null;
  }
}

/**
 * Creates an assessee form and sends notification
 */
async function createAssesseeForm(
  assessment: Assessment,
  assessee: Assessee,
  assesseeUser: User,
  baseUrl: string
): Promise<Form | null> {
  try {
    // Check if an assessee form already exists for this assessment
    const existingForms = await storage.getFormsByAssessment(assessment.id);
    const existingAssesseeForm = existingForms.find(form => form.formType === 'assessee_over_16');
    
    if (existingAssesseeForm) {
      logger.info(`Assessee form already exists for assessment ${assessment.id}`);
      return existingAssesseeForm;
    }
    
    // Generate a secure access token and set expiration (30 days from now)
    const accessToken = uuidv4();
    const expirationDate = addDays(new Date(), 30);
    
    // Create the assessee form with secure token
    const assesseeForm = await storage.createForm({
      assessmentId: assessment.id,
      formType: 'assessee_over_16',
      status: 'not_started',
      completedById: null,
      completedAt: null,
      data: null,
      accessToken,
      accessTokenExpiresAt: expirationDate
    });

    logger.info(`Created assessee form with ID ${assesseeForm.id} for assessment ${assessment.id}`);

    // Send notification to assessee
    if (assesseeUser.email) {
      // Create the secure form link using token
      const formLink = `${baseUrl}/forms/access/${assesseeForm.accessToken}`;
      
      // Send combined notification with account information
      await sendFormNotificationEmail(
        assesseeUser.email,
        {
          id: assesseeForm.id,
          formType: 'assessee_over_16',
          formName: 'Assessee Pre-Assessment Questionnaire',
          assessmentId: assessment.id,
          assesseeFullName: assessee.fullName
        },
        formLink,
        {
          accountEmail: assesseeUser.email,
          hasAccount: true,
          appUrl: baseUrl
        }
      );
      
      logger.info(`Sent assessee form notification to ${assesseeUser.email} with account info`);
    }

    return assesseeForm;
  } catch (error: unknown) {
    logger.error("Error creating assessee form:", error);
    return null;
  }
}