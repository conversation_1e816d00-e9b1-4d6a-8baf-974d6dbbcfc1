import { logger } from '../logger';
import { EmailOptions } from './sendgrid';
import { sendEmail } from './sendgrid';
import { createEmailTemplate, createPlainTextEmail } from './email-template-base';

interface InviteUserData {
  email: string;
  fullName?: string;
  role: string;
  tempPassword: string;
  inviteToken: string;
  message?: string;
}

/**
 * Creates and sends an invitation email to a new user
 * @param userData The user data for the invitation
 * @returns Success status and message
 */
export async function sendInvitationEmail(userData: InviteUserData): Promise<{ success: boolean; message: string }> {
  try {
    if (!process.env.SENDGRID_FROM_EMAIL || !process.env.SENDGRID_FROM_NAME) {
      return {
        success: false,
        message: 'SENDGRID_FROM_EMAIL or SENDGRID_FROM_NAME environment variables not set'
      };
    }

    // Get the current deployment URL
    // In development, use the Replit provided URL
    const baseUrl = process.env.REPLIT_DOMAINS ? 
      'https://' + process.env.REPLIT_DOMAINS?.split(',')[0] :
      'http://localhost:5000';
    
    // Generate readable role description
    const roleName = userData.role.charAt(0).toUpperCase() + userData.role.slice(1);
    
    // Use the person's name if available, otherwise use email
    const recipientName = userData.fullName || userData.email.split('@')[0];

    // Create the email content using our template
    let content = `
      <p>You have been invited to join the NeuroElevate Assessment Platform as a <strong>${roleName}</strong>.</p>
    `;
    
    // Add the admin message if provided
    if (userData.message) {
      content += `
        <div style="padding: 15px; background-color: #f3f4f6; border-left: 4px solid #10b981; margin: 20px 0;">
          <p style="margin: 0;"><strong>Message from the admin:</strong></p>
          <p style="margin: 10px 0 0 0;">${userData.message}</p>
        </div>
      `;
    }
    
    // Add login details
    content += `
      <div style="margin: 25px 0; padding: 20px; border: 1px solid #e5e7eb; border-radius: 5px; background-color: #f9fafb;">
        <h3 style="margin-top: 0; color: #10b981;">Your Login Details</h3>
        <p><strong>Email:</strong> ${userData.email}</p>
        <p><strong>Temporary Password:</strong> ${userData.tempPassword}</p>
        <p style="color: #ef4444; font-size: 0.9em; font-weight: 500;">Please change your password after logging in.</p>
      </div>
    `;

    // Create template options
    const templateOptions = {
      title: 'Welcome to NeuroElevate',
      preheader: `You've been invited to join NeuroElevate Assessment Platform as a ${roleName}`,
      recipientName,
      content,
      buttonText: 'Login to NeuroElevate',
      buttonUrl: `${baseUrl}/auth`,
      footerText: 'If you have any questions, please contact your administrator.'
    };

    // Generate HTML and plain text versions using our template system
    const html = createEmailTemplate(templateOptions);
    const text = createPlainTextEmail(templateOptions);

    const emailOptions: EmailOptions = {
      to: userData.email,
      from: {
        email: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
        name: process.env.SENDGRID_FROM_NAME || 'NeuroElevate Platform'
      },
      subject: 'You\'ve Been Invited to NeuroElevate Assessment Platform',
      text,
      html
    };

    const result = await sendEmail(emailOptions);
    
    if (result) {
      return {
        success: true,
        message: `Invitation email sent successfully to ${userData.email}`
      };
    } else {
      return {
        success: false,
        message: 'Failed to send invitation email'
      };
    }
  } catch (error: any) {
    logger.error('Error sending invitation email:', error);
    return {
      success: false,
      message: `Error sending invitation email: ${error.message || 'Unknown error'}`
    };
  }
}