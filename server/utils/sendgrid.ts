import { logger } from '../logger';
import sgMail from '@sendgrid/mail';

// Initialize SendGrid with enhanced configuration
if (process.env.SENDGRID_API_KEY) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  
  // Set timeout for API requests (30 seconds)
  sgMail.setTimeout(30000);
  
  // Set substitution wrappers for template variables 
  sgMail.setSubstitutionWrappers('{{', '}}');
} else {
  logger.warn('SENDGRID_API_KEY environment variable not set. Email functionality will not work.');
}

/**
 * Email sender interface
 */
export interface EmailSender {
  email: string;
  name: string;
}

/**
 * Email options interface
 */
export interface EmailOptions {
  to: string;
  from?: string | EmailSender;
  subject: string;
  text?: string;
  html?: string;
  headers?: Record<string, string>;
  templateData?: Record<string, any>;
}

/**
 * Send an email using SendGrid with improved authentication support and deliverability
 * @param options Email options
 * @returns Promise that resolves to true if email was sent successfully, false otherwise
 */
export async function sendEmail(toEmail: string, options: EmailOptions): Promise<boolean>;
export async function sendEmail(options: EmailOptions): Promise<boolean>;
export async function sendEmail(emailOrOptions: string | EmailOptions, maybeOptions?: EmailOptions): Promise<boolean> {
  try {
    let options: EmailOptions;
    
    // Handle both function signatures
    if (typeof emailOrOptions === 'string') {
      if (!maybeOptions) {
        logger.error('Cannot send email: Missing options');
        return false;
      }
      options = {
        ...maybeOptions,
        to: emailOrOptions
      };
    } else {
      options = emailOrOptions;
    }
    
    // If SendGrid key is not configured, log a message and return false
    if (!process.env.SENDGRID_API_KEY) {
      logger.error('Cannot send email: SENDGRID_API_KEY is not configured');
      return false;
    }

    // Make sure we have required fields
    if (!options.to || !options.subject || (!options.text && !options.html && !options.templateData)) {
      logger.error('Cannot send email: Missing required fields');
      return false;
    }
    
    // Set default from address if not provided
    if (!options.from) {
      options.from = {
        email: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
        name: process.env.SENDGRID_FROM_NAME || 'A2lexa Assessment Platform'
      };
    }

    // Prepare email data with meticulous sender formatting for maximum deliverability
    const msg: any = {
      to: options.to,
      subject: options.subject,
      mailSettings: {
        sandboxMode: {
          enable: false
        }
      },
      trackingSettings: {
        clickTracking: {
          enable: false,
          enableText: false
        },
        openTracking: {
          enable: false
        },
        subscriptionTracking: {
          enable: false
        }
      }
    };
    
    // Handle template data if provided
    if (options.templateData) {
      // Use dynamic templates
      msg.dynamicTemplateData = options.templateData;
      
      // Generate simple HTML if template data is provided but no template ID
      if (!options.text && !options.html) {
        // Create a basic HTML email from the template data
        const htmlContent = Object.entries(options.templateData)
          .map(([key, value]) => `<p><strong>${key}:</strong> ${value}</p>`)
          .join('');
          
        msg.content = [
          {
            type: 'text/plain',
            value: 'Please view this email in an HTML compatible email client.'
          },
          {
            type: 'text/html',
            value: `<div style="font-family: Arial, sans-serif; padding: 20px;">
              <h2>A2lexa Assessment Platform</h2>
              <div>${htmlContent}</div>
            </div>`
          }
        ];
      }
    } else {
      // Standard content
      msg.content = [
        {
          type: 'text/plain',
          value: options.text || 'Please view this email in an HTML compatible email client.'
        },
        {
          type: 'text/html',
          value: options.html || '<p>Please view this email in an HTML compatible email client.</p>'
        }
      ];
    }
    
    // Handle the 'from' field with proper RFC-compliance formatting
    if (typeof options.from === 'string') {
      // If just a string is provided, use as is but log a warning
      logger.warn('Using simple string email format is not recommended. Use {email, name} object for better deliverability.');
      msg.from = options.from;
    } else {
      // The preferred method with separate email and name
      if (!options.from.email || !options.from.name) {
        logger.error('From email or name is missing');
        return false;
      }
      
      // Make sure the domain matches the authenticated domain in SendGrid
      const emailParts = options.from.email.trim().split('@');
      if (emailParts.length === 2) {
        // Ensure local part and domain are properly formatted
        const [localPart, domain] = emailParts;
        
        // Ensure domain is lowercase (important for authentication)
        const formattedEmail = `${localPart}@${domain.toLowerCase()}`;
        
        msg.from = {
          email: formattedEmail,
          name: options.from.name.trim()
        };
        
        // Log the detailed sender information for debugging
        logger.info(`Using authenticated sender: "${msg.from.name}" <${msg.from.email}>`);
        logger.info(`Domain: ${domain.toLowerCase()}, Local part: ${localPart}`);
      } else {
        logger.error('Invalid email format detected!');
        msg.from = {
          email: options.from.email.trim(),
          name: options.from.name.trim()
        };
      }
    }
    
    // Standard and custom headers for enhanced deliverability
    const defaultHeaders = {
      'X-Priority': '1',
      'Importance': 'high',
      'X-Mailer': 'A2lexa Platform (SendGrid)',
      'X-Sender': typeof options.from === 'string' ? 
        options.from : 
        options.from.email,
      'Return-Path': typeof options.from === 'string' ? 
        options.from : 
        options.from.email,
      'List-Unsubscribe': `<mailto:${typeof options.from === 'string' ? 
        options.from : 
        options.from.email}?subject=Unsubscribe>`,
      'X-Report-Abuse': `Please report abuse to: ${typeof options.from === 'string' ? 
        options.from : 
        options.from.email}`
    };
    
    // Merge custom headers with defaults
    msg.headers = {
      ...defaultHeaders,
      ...(options.headers || {})
    };

    // Send email using SendGrid
    try {
      logger.info(`Attempting to send email to ${options.to} with SendGrid API key: ${process.env.SENDGRID_API_KEY ? 'Valid API key exists' : 'No API key found'}`);
      logger.info(`FROM email: ${typeof options.from === 'string' ? options.from : options.from.email}`);
      logger.info(`FROM name: ${typeof options.from === 'string' ? 'Not provided' : options.from.name}`);
      
      await sgMail.send(msg);
      logger.info(`Email sent successfully to ${options.to}`);
      return true;
    } catch (error: any) {
      // Log detailed error information
      logger.error('SendGrid error sending email:', error);
      
      if (error.response && error.response.body) {
        logger.error('SendGrid response body:', JSON.stringify(error.response.body));
        
        if (error.response.body.errors) {
          logger.error('SendGrid detailed errors:', JSON.stringify(error.response.body.errors));
        }
      }
      
      throw error;  // Rethrow to be caught by outer catch
    }
  } catch (error: any) {
    logger.error('Error sending email via SendGrid:', error);
    return false;
  }
}

// The sendTestEmail function has been moved to email-test.ts