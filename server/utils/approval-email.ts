import { logger } from '../logger';
import { sendEmail } from './sendgrid';
import { createEmailTemplate, createPlainTextEmail } from './email-template-base';

/**
 * Sends an account approval email to a user whose account has been approved
 * 
 * @param email User's email address
 * @param fullName User's full name
 * @param role User's role in the system
 */
export async function sendApprovalEmail(email: string, fullName: string, role: string): Promise<boolean> {
  const fromEmail = process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.SENDGRID_FROM_NAME || 'NeuroElevate Assessment Platform';
  
  const subject = 'Your NeuroElevate Account Has Been Approved';
  
  const roleDisplay = role.charAt(0).toUpperCase() + role.slice(1);
  
  // Create the content for the email
  const content = `
    <p>We're pleased to inform you that your NeuroElevate account has been <strong>approved</strong>!</p>
    
    <div style="background-color: #f3f4f6; border-radius: 4px; padding: 15px; margin: 20px 0;">
      <h3 style="margin-top: 0; color: #10b981;">Account Details</h3>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Role:</strong> ${roleDisplay}</p>
      <p><strong>Status:</strong> Active</p>
    </div>
    
    <p>You can now log in to NeuroElevate using your email and password.</p>
  `;

  // Create the email using our template
  const templateOptions = {
    title: 'Account Approved',
    preheader: 'Good news! Your NeuroElevate account has been approved.',
    recipientName: fullName || 'there',
    content,
    buttonText: 'Log In Now',
    buttonUrl: 'https://neuroelevate.replit.app/auth'
  };

  // Generate HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  try {
    return await sendEmail({
      to: email,
      from: {
        email: fromEmail,
        name: fromName
      },
      subject,
      html,
      text
    });
  } catch (error) {
    logger.error('Error sending approval email:', error);
    return false;
  }
}