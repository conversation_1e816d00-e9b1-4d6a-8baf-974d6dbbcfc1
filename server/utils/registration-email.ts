import { logger } from '../logger';
import { sendEmail } from './sendgrid';
import { createEmailTemplate, createPlainTextEmail } from './email-template-base';

/**
 * Sends a registration confirmation email to a newly registered user
 * 
 * @param email User's email address
 * @param fullName User's full name
 */
export async function sendRegistrationEmail(email: string, fullName: string): Promise<boolean> {
  const fromEmail = process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.SENDGRID_FROM_NAME || 'NeuroElevate Assessment Platform';
  
  const subject = 'Your NeuroElevate Registration - Awaiting Approval';
  
  // Create the content for the email
  const content = `
    <p>Thank you for registering with NeuroElevate, your account has been created and is currently awaiting administrator approval.</p>
    
    <h3 style="color: #10b981; margin-top: 24px;">What happens next?</h3>
    
    <ul>
      <li>An administrator will review your registration</li>
      <li>You will receive an email notification when your account is approved</li>
      <li>After approval, you can log in with your credentials</li>
      <li>This process typically takes 1-2 business days</li>
    </ul>
  `;

  // Create the email using our template
  const templateOptions = {
    title: 'Registration Received',
    preheader: 'Thank you for registering with NeuroElevate. Your account is awaiting approval.',
    recipientName: fullName || 'there',
    content,
    buttonText: 'Visit Our Website',
    buttonUrl: 'https://neuroelevate.replit.app'
  };

  // Generate HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  try {
    return await sendEmail({
      to: email,
      from: {
        email: fromEmail,
        name: fromName
      },
      subject,
      html,
      text
    });
  } catch (error) {
    logger.error('Error sending registration email:', error);
    return false;
  }
}