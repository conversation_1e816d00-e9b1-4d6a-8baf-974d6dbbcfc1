import { logger } from '../logger';
import { sendEmail } from './sendgrid';
import { EmailOptions } from './sendgrid';
import { createEmailTemplate, createPlainTextEmail } from './email-template-base';
import { format } from 'date-fns';

interface AssessmentData {
  id: number;
  assesseeFullName: string;
  assessmentType: string;
  dateOfBirth?: string | Date;
  status: string;
  createdAt: Date;
}

/**
 * Creates and sends a notification email when a new assessment is created
 * 
 * @param recipientEmail The email address to send the notification to
 * @param assessment Assessment data to include in the notification
 * @returns Success status
 */
export async function sendAssessmentNotificationEmail(
  recipientEmail: string, 
  assessment: AssessmentData
): Promise<boolean> {
  const fromEmail = process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.SENDGRID_FROM_NAME || 'NeuroElevate Assessment Platform';
  
  const subject = `New Assessment Created: ${assessment.assesseeFullName}`;
  
  // Format dates for display
  const createdDate = format(assessment.createdAt, 'PPP');
  const dob = assessment.dateOfBirth 
    ? (typeof assessment.dateOfBirth === 'string' 
      ? assessment.dateOfBirth 
      : format(assessment.dateOfBirth, 'PPP'))
    : 'Not provided';
  
  // Format assessment type for display
  const assessmentTypeDisplay = assessment.assessmentType === 'under_16' 
    ? 'Under 16 Years' 
    : 'Over 16 Years';
  
  // Create the content for the email
  const content = `
    <p>A new assessment has been created in the NeuroElevate platform. Here are the details:</p>
    
    <div style="background-color: #f3f4f6; border-radius: 4px; padding: 20px; margin: 20px 0;">
      <h3 style="margin-top: 0; color: #10b981;">Assessment Details</h3>
      <p><strong>Assessment ID:</strong> ${assessment.id}</p>
      <p><strong>Assessee Name:</strong> ${assessment.assesseeFullName}</p>
      <p><strong>Date of Birth:</strong> ${dob}</p>
      <p><strong>Assessment Type:</strong> ${assessmentTypeDisplay}</p>
      <p><strong>Status:</strong> ${assessment.status}</p>
      <p><strong>Created On:</strong> ${createdDate}</p>
    </div>
    
    <p>Please log in to the NeuroElevate platform to view the complete assessment details and take appropriate action.</p>
  `;

  // Create the email using our template
  const templateOptions = {
    title: 'New Assessment Notification',
    preheader: `New assessment created for ${assessment.assesseeFullName}`,
    content,
    buttonText: 'View Assessment',
    buttonUrl: `https://neuroelevate.cloud9assist.com/assessments/${assessment.id}`
  };

  // Generate HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  try {
    return await sendEmail({
      to: recipientEmail,
      from: {
        email: fromEmail,
        name: fromName
      },
      subject,
      html,
      text
    });
  } catch (error) {
    logger.error('Error sending assessment notification email:', error);
    return false;
  }
}