import { logger } from '../logger';
import { sendEmail } from './sendgrid';
import { createEmailTemplate, createPlainTextEmail } from './email-template-base';

/**
 * Sends an account rejection email to a user whose account has been rejected
 * 
 * @param email User's email address
 * @param fullName User's full name
 * @param role User's role in the system
 */
export async function sendRejectionEmail(email: string, fullName: string, role: string): Promise<boolean> {
  const fromEmail = process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.SENDGRID_FROM_NAME || 'NeuroElevate Assessment Platform';
  
  const subject = 'Your NeuroElevate Account Registration';
  
  const roleDisplay = role.charAt(0).toUpperCase() + role.slice(1);
  
  // Create the content for the email
  const content = `
    <p>Thank you for your interest in the NeuroElevate Assessment Platform.</p>
    
    <p>After reviewing your registration request, we regret to inform you that we are unable to approve your account at this time.</p>
    
    <div style="background-color: #f3f4f6; border-radius: 4px; padding: 15px; margin: 20px 0;">
      <h3 style="margin-top: 0; color: #6b7280;">Registration Details</h3>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Requested Role:</strong> ${roleDisplay}</p>
    </div>
    
    <p>If you believe this decision was made in error or if you have any questions, please contact our admin team at <a href="mailto:<EMAIL>"><EMAIL></a> for further assistance.</p>
  `;

  // Create the email using our template
  const templateOptions = {
    title: 'Account Registration Status',
    preheader: 'Information about your NeuroElevate account registration',
    recipientName: fullName || 'there',
    content,
    footerText: 'If you have any questions, please contact our admin <NAME_EMAIL>.'
  };

  // Generate HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  try {
    return await sendEmail({
      to: email,
      from: {
        email: fromEmail,
        name: fromName
      },
      subject,
      html,
      text
    });
  } catch (error) {
    logger.error('Error sending rejection email:', error);
    return false;
  }
}