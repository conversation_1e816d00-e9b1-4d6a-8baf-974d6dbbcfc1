import { format } from 'date-fns';
import { EmailOptions } from './sendgrid';
import { createEmailTemplate, createPlainTextEmail } from './email-template-base';
import { User } from '@shared/schema';

/**
 * Creates an email notification for admin users when a new user registers
 * @param adminEmail Email address of the admin to notify
 * @param newUser The newly registered user data
 * @param reviewUrl URL for admins to review the new user registration
 * @returns EmailOptions object
 */
export function createNewUserNotificationEmail(
  adminEmail: string, 
  newUser: User, 
  reviewUrl: string
): EmailOptions {
  // Format registration date for display
  const registrationDate = format(new Date(newUser.createdAt || new Date()), 'PPpp');

  // Create the content for the admin notification email
  const content = `
    <p>A new user has registered on the NeuroElevate platform and requires review for account approval.</p>
    
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 20px 0; border-collapse: collapse; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px;">
      <tr>
        <td style="padding: 20px;">
          <h3 style="margin: 0 0 15px 0; color: #1e293b; font-size: 18px;">New User Registration Details</h3>
          
          <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; width: 30%; font-weight: 600; color: #475569;">Full Name:</td>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; color: #1e293b;">${newUser.fullName || 'Not provided'}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #475569;">Email:</td>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; color: #1e293b;">${newUser.email}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #475569;">Username:</td>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; color: #1e293b;">${newUser.username}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #475569;">Role:</td>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; color: #1e293b;">
                <span style="background-color: #dbeafe; color: #1e40af; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                  ${newUser.role.toUpperCase()}
                </span>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #475569;">Status:</td>
              <td style="padding: 8px 0; border-bottom: 1px solid #e2e8f0; color: #1e293b;">
                <span style="background-color: #fef3c7; color: #92400e; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                  ${newUser.status?.toUpperCase() || 'PENDING REVIEW'}
                </span>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: 600; color: #475569;">Registration Date:</td>
              <td style="padding: 8px 0; color: #1e293b;">${registrationDate}</td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

    <div style="padding: 15px; background-color: #fef3c7; border-left: 4px solid #f59e0b; margin: 20px 0; border-radius: 4px;">
      <p style="margin: 0; color: #92400e; font-weight: 600;">Action Required</p>
      <p style="margin: 5px 0 0 0; color: #92400e;">Please review this registration and approve or reject the user account to complete the registration process.</p>
    </div>

    <p style="margin: 15px 0;">Click the button below to review and manage this registration:</p>
  `;

  // Create the email template options
  const templateOptions = {
    title: 'New User Registration Pending Review',
    preheader: `${newUser.fullName || newUser.username} has registered and needs approval`,
    content,
    buttonText: 'Review Registration',
    buttonUrl: reviewUrl,
    footerText: 'This is an automated notification. Please log in to the admin panel to manage user registrations.'
  };

  // Generate the HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  // Email options
  return {
    to: adminEmail,
    from: {
      email: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      name: process.env.SENDGRID_FROM_NAME || 'NeuroElevate Platform'
    },
    subject: `New User Registration: ${newUser.fullName || newUser.username} (${newUser.role})`,
    text,
    html
  };
}

/**
 * Creates a bulk notification email for admins about multiple pending registrations
 * @param adminEmail Email address of the admin to notify
 * @param pendingUsers Array of users pending approval
 * @param reviewUrl URL for admins to review pending registrations
 * @returns EmailOptions object
 */
export function createPendingRegistrationsDigestEmail(
  adminEmail: string,
  pendingUsers: User[],
  reviewUrl: string
): EmailOptions {
  const userCount = pendingUsers.length;
  const currentDate = format(new Date(), 'PPP');

  // Create user list HTML
  const userListHtml = pendingUsers.map(user => `
    <tr>
      <td style="padding: 8px 12px; border-bottom: 1px solid #e2e8f0; color: #1e293b;">${user.fullName || user.username}</td>
      <td style="padding: 8px 12px; border-bottom: 1px solid #e2e8f0; color: #1e293b;">${user.email}</td>
      <td style="padding: 8px 12px; border-bottom: 1px solid #e2e8f0; color: #1e293b;">
        <span style="background-color: #dbeafe; color: #1e40af; padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: 500;">
          ${user.role.toUpperCase()}
        </span>
      </td>
      <td style="padding: 8px 12px; border-bottom: 1px solid #e2e8f0; color: #1e293b; font-size: 12px;">${format(new Date(user.createdAt || new Date()), 'MMM dd')}</td>
    </tr>
  `).join('');

  const content = `
    <p>You have ${userCount} user registration${userCount > 1 ? 's' : ''} pending review on the NeuroElevate platform.</p>
    
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 20px 0; border-collapse: collapse; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px;">
      <tr>
        <td style="padding: 20px;">
          <h3 style="margin: 0 0 15px 0; color: #1e293b; font-size: 18px;">Pending Registrations (${userCount})</h3>
          
          <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; border: 1px solid #e2e8f0; border-radius: 4px;">
            <thead>
              <tr style="background-color: #f1f5f9;">
                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #e2e8f0; color: #475569; font-weight: 600; font-size: 14px;">Name</th>
                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #e2e8f0; color: #475569; font-weight: 600; font-size: 14px;">Email</th>
                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #e2e8f0; color: #475569; font-weight: 600; font-size: 14px;">Role</th>
                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #e2e8f0; color: #475569; font-weight: 600; font-size: 14px;">Date</th>
              </tr>
            </thead>
            <tbody>
              ${userListHtml}
            </tbody>
          </table>
        </td>
      </tr>
    </table>

    <div style="padding: 15px; background-color: #fef3c7; border-left: 4px solid #f59e0b; margin: 20px 0; border-radius: 4px;">
      <p style="margin: 0; color: #92400e; font-weight: 600;">Action Required</p>
      <p style="margin: 5px 0 0 0; color: #92400e;">Please review these registrations and approve or reject the user accounts to complete the registration process.</p>
    </div>

    <p style="margin: 15px 0;">Click the button below to access the admin panel:</p>
  `;

  // Create the email template options
  const templateOptions = {
    title: 'Pending User Registrations Digest',
    preheader: `${userCount} user registration${userCount > 1 ? 's' : ''} awaiting your review`,
    content,
    buttonText: 'Review All Registrations',
    buttonUrl: reviewUrl,
    footerText: `This digest was generated on ${currentDate}. You can adjust notification preferences in the admin panel.`
  };

  // Generate the HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  // Email options
  return {
    to: adminEmail,
    from: {
      email: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      name: process.env.SENDGRID_FROM_NAME || 'NeuroElevate Platform'
    },
    subject: `${userCount} User Registration${userCount > 1 ? 's' : ''} Pending Review - NeuroElevate`,
    text,
    html
  };
}