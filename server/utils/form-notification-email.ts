import { logger } from '../logger';
import { sendEmail } from './sendgrid';
import { EmailOptions } from './sendgrid';
import { createEmailTemplate, createPlainTextEmail } from './email-template-base';

interface FormData {
  id: number;
  formType: string;
  formName: string;
  assessmentId: number;
  assesseeFullName: string;
}

interface AccountInfo {
  accountEmail: string;
  hasAccount: boolean;
  appUrl: string;
}

/**
 * Creates and sends a notification email about a form that needs to be completed
 * 
 * @param recipientEmail The email address to send the notification to
 * @param form Form data to include in the notification
 * @param formLink Direct link to the form
 * @param accountInfo Optional account information if the recipient has an account
 * @returns Success status
 */
export async function sendFormNotificationEmail(
  recipientEmail: string, 
  form: FormData,
  formLink: string,
  accountInfo?: AccountInfo
): Promise<boolean> {
  const fromEmail = process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.SENDGRID_FROM_NAME || 'NeuroElevate Assessment Platform';
  
  const subject = `NeuroElevate: ${form.formName} - Action Required`;
  
  // Get a readable form type
  const formTypeDisplay = form.formType.split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
  
  // Create the content for the email
  let content = `
    <p>You have a form that requires your attention as part of an ongoing assessment process in the NeuroElevate platform.</p>
    
    <div style="background-color: #f3f4f6; border-radius: 4px; padding: 20px; margin: 20px 0;">
      <h3 style="margin-top: 0; color: #10b981;">Form Details</h3>
      <p><strong>Form:</strong> ${form.formName}</p>
      <p><strong>Type:</strong> ${formTypeDisplay}</p>
      <p><strong>For Assessment of:</strong> ${form.assesseeFullName}</p>
    </div>
    
    <p style="font-weight: 500; margin-bottom: 10px; color: #4b5563;">You have TWO OPTIONS to complete this form:</p>
    
    <div style="background-color: #f0f9ff; border: 1px solid #bae6fd; border-radius: 6px; padding: 15px; margin-bottom: 20px;">
      <h4 style="margin-top: 0; color: #0369a1; font-weight: 600;">Option 1: Complete Form Directly (Fastest Method)</h4>
      <p>You can complete this form immediately by clicking the "Complete Form" button at the bottom of this email.</p>
      <ul style="margin-bottom: 5px;">
        <li>No account login required</li>
        <li>No password to remember</li>
        <li>Secure, direct access</li>
      </ul>
      <p style="font-size: 0.9em; color: #64748b; margin-bottom: 0;">This secure link will expire in 30 days and is specific to you. Please do not share it with others.</p>
    </div>
  `;

  // Add account information section if provided
  if (accountInfo && accountInfo.hasAccount) {
    content += `
      <div style="background-color: #ecfdf5; border: 1px solid #a7f3d0; border-radius: 6px; padding: 15px; margin-bottom: 20px;">
        <h4 style="margin-top: 0; color: #047857; font-weight: 600;">Option 2: Access Through Your NeuroElevate Account</h4>
        <p>An account has been automatically created for you on the NeuroElevate platform where you can access all assessment information.</p>
        <p><strong>Your login email:</strong> ${accountInfo.accountEmail}</p>
        
        <div style="background-color: #f8fafc; border-radius: 4px; padding: 10px; margin: 10px 0;">
          <p style="margin-top: 0; margin-bottom: 5px; font-weight: 500;">First-time access instructions:</p>
          <ol style="margin-top: 0; margin-bottom: 0;">
            <li>Click the "Access Account" button at the bottom of this email</li>
            <li>Click "Forgot Password" on the login screen</li>
            <li>Enter your email address (${accountInfo.accountEmail})</li>
            <li>Follow the password reset instructions you receive</li>
          </ol>
        </div>
        
        <p style="margin-bottom: 0;">After setting your password, you'll have full access to view and manage all assessment information including this form.</p>
      </div>
    `;
  }

  // Add debugging logs to help diagnose form link issues
  logger.info('DEBUG FORM EMAIL: formLink:', formLink);
  logger.info('DEBUG FORM EMAIL: formLink type:', typeof formLink);
  
  // Ensure the formLink is a string and not undefined or null
  const safeFormLink = formLink && typeof formLink === 'string' ? formLink : '';
  logger.info('DEBUG FORM EMAIL: safeFormLink:', safeFormLink);
  
  // Create the email using our template with dynamic content
  const templateOptions = {
    title: 'Form Requires Your Input',
    preheader: `Please complete the ${form.formName} form for ${form.assesseeFullName}`,
    content,
    buttonText: 'Complete Form',
    buttonUrl: safeFormLink, // Use the safe form link instead
    footerText: 'If you have any questions about this form or need assistance, please contact our support team.'
  };

  // Add account access button if applicable
  if (accountInfo && accountInfo.hasAccount) {
    templateOptions.footerText = `
      <div style="text-align: center; margin-top: 20px; margin-bottom: 20px;">
        <div style="display: block; margin-bottom: 10px; font-weight: 500; color: #4b5563; text-align: center;">Or</div>
        <a href="${accountInfo.appUrl}/login" style="background-color: #e0f2fe; color: #0369a1; border: 1px solid #93c5fd; text-decoration: none; padding: 10px 16px; border-radius: 4px; font-weight: 500; display: inline-block; margin-bottom: 5px;">Access Your Account</a>
        <div style="font-size: 0.8em; color: #6b7280; margin-bottom: 20px;">This will take you to the login page where you can set up your password.</div>
      </div>
      <p>If you have any questions about this form or need assistance, please contact our support team.</p>
    `;
  }

  // Generate HTML and plain text versions
  const html = createEmailTemplate(templateOptions);
  const text = createPlainTextEmail(templateOptions);

  try {
    return await sendEmail({
      to: recipientEmail,
      from: {
        email: fromEmail,
        name: fromName
      },
      subject,
      html,
      text
    });
  } catch (error) {
    logger.error('Error sending form notification email:', error);
    return false;
  }
}