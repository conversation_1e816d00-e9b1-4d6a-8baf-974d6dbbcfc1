import { logger } from './logger';
import { withDatabaseRetry } from './db-retry';
import { 
  users, assessees, schools, assessments, forms, documents, activities,
  referrals, adminActivityLogs, conversations, conversationParticipants,
  messages, messageRecipients, notifications, notificationPreferences,
  passwordResetTokens, formQuestions, formResponses, universities,
  publicTrackingRecords, issues, issueComments, assessmentNotes,
  type User, type InsertUser, type Assessee, type InsertAssessee,
  type School, type InsertSchool, type Assessment, type InsertAssessment,
  type Form, type InsertForm, type Document, type InsertDocument,
  type Activity, type InsertActivity, type Referral, type InsertReferral,
  type Conversation, type InsertConversation, type ConversationParticipant,
  type InsertConversationParticipant, type Message, type InsertMessage,
  type MessageRecipient, type InsertMessageRecipient, type Notification,
  type InsertNotification, type NotificationPreference, type InsertNotificationPreference,
  type FormQuestion, type InsertFormQuestion, type FormResponse, type InsertFormResponse,
  type Issue, type InsertIssue, type IssueComment, type InsertIssueComment
} from "@shared/schema";
import { db } from "./db";
import { and, eq, desc, asc, sql, count, like, ilike, isNull, or, gt } from "drizzle-orm";
import { IStorage } from "./storage";
import { scrypt, randomBytes } from "crypto";
import { promisify } from "util";

const scryptAsync = promisify(scrypt);

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

export class DatabaseStorage implements IStorage {
  sessionStore: any;

  constructor() {
    // Simple in-memory session store for now
    this.sessionStore = null;
  }

  // Missing stub methods for IStorage interface
  async getAllUniversitiesWithAdminInfo(): Promise<any[]> { return []; }
  async getUniversityWithStaff(id: number): Promise<any> { return null; }
  async getUniversityAdmins(universityId: number): Promise<any[]> { return []; }
  async getPublicTrackingRecord(trackingId: string): Promise<any> { return null; }
  async createPublicTrackingRecord(record: any): Promise<any> { return null; }
  async getAssessmentByTrackingId(trackingId: string): Promise<any> { return null; }
  async listUsersByUniversity(universityId: number): Promise<any[]> { return []; }
  async getAssessmentsByUniversity(universityId: number): Promise<any[]> { return []; }
  async getAssessmentsByReferringUser(userId: number): Promise<any[]> { return []; }

  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const hashedPassword = await hashPassword(insertUser.password);
    const userWithHashedPassword = {
      email: insertUser.email,
      username: insertUser.username,
      password: hashedPassword,
      fullName: insertUser.fullName,
      role: insertUser.role,
      status: insertUser.status,
      phone: insertUser.phone,
      organization: insertUser.organization,
      position: insertUser.position,
      department: insertUser.department,
      createdAt: new Date()
    };

    const [user] = await db.insert(users).values(userWithHashedPassword).returning();
    return user;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const [user] = await db.update(users).set(userData).where(eq(users.id, id)).returning();
    return user;
  }

  async listUsers(role?: string): Promise<User[]> {
    if (role) {
      return db.select().from(users).where(eq(users.role, role as any));
    }
    return db.select().from(users);
  }

  async getAssessee(id: number): Promise<Assessee | undefined> {
    const [assessee] = await db.select().from(assessees).where(eq(assessees.id, id));
    return assessee;
  }

  async createAssessee(insertAssessee: InsertAssessee): Promise<Assessee> {
    const [assessee] = await db.insert(assessees).values(insertAssessee).returning();
    return assessee;
  }

  async updateAssessee(id: number, assesseeData: Partial<Assessee>): Promise<Assessee | undefined> {
    const [assessee] = await db.update(assessees).set(assesseeData).where(eq(assessees.id, id)).returning();
    return assessee;
  }

  async listAssesseesByParent(parentId: number): Promise<Assessee[]> {
    return db.select().from(assessees).where(eq(assessees.parentId, parentId));
  }

  async listAssesseesBySchool(schoolId: number): Promise<Assessee[]> {
    return db.select().from(assessees).where(eq(assessees.schoolId, schoolId));
  }

  async listAllAssessees(): Promise<Assessee[]> {
    return db.select().from(assessees);
  }

  async getSchool(id: number): Promise<School | undefined> {
    const [school] = await db.select().from(schools).where(eq(schools.id, id));
    return school;
  }

  async createSchool(insertSchool: InsertSchool): Promise<School> {
    const [school] = await db.insert(schools).values(insertSchool).returning();
    return school;
  }

  async updateSchool(id: number, schoolData: Partial<School>): Promise<School | undefined> {
    const [school] = await db.update(schools).set(schoolData).where(eq(schools.id, id)).returning();
    return school;
  }

  async listSchools(): Promise<School[]> {
    return db.select().from(schools);
  }

  async getAssessment(id: number): Promise<Assessment | undefined> {
    const [assessment] = await db.select().from(assessments).where(eq(assessments.id, id));
    return assessment;
  }



  async createAssessment(insertAssessment: InsertAssessment): Promise<Assessment> {
    const [assessment] = await db.insert(assessments).values(insertAssessment).returning();
    return assessment;
  }

  async updateAssessment(id: number, assessmentData: Partial<Assessment>): Promise<Assessment | undefined> {
    const [assessment] = await db.update(assessments).set(assessmentData).where(eq(assessments.id, id)).returning();
    return assessment;
  }

  async listAssessments(filters?: Partial<Assessment>): Promise<Assessment[]> {
    let query = db.select().from(assessments);
    
    if (filters) {
      const conditions = [];
      if (filters.status) conditions.push(eq(assessments.status, filters.status));
      if (filters.assessorId) conditions.push(eq(assessments.assessorId, filters.assessorId));
      if (filters.assesseeId) conditions.push(eq(assessments.assesseeId, filters.assesseeId));
      
      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }
    }
    
    return query.orderBy(desc(assessments.createdAt));
  }

  async listAllAssessmentsWithUsers(): Promise<any[]> {
    try {
      const result = await db
        .select({
          // Assessment fields
          id: assessments.id,
          assesseeId: assessments.assesseeId,
          assessorId: assessments.assessorId,
          status: assessments.status,
          paymentStatus: assessments.paymentStatus,
          depositAmount: assessments.depositAmount,
          notes: assessments.notes,
          scheduledDate: assessments.scheduledDate,
          completedDate: assessments.completedDate,
          createdAt: assessments.createdAt,
          updatedAt: assessments.updatedAt,
          // Assessee fields (flattened)
          assesseeFullName: assessees.fullName,
          assesseeDateOfBirth: assessees.dateOfBirth,
          assesseeEmail: assessees.email,
          assesseePhone: assessees.phone,
          assesseeAddress: assessees.address,
          assesseeParentId: assessees.parentId,
          // User fields (flattened)
          referringUserId: users.id,
          referringUserFullName: users.fullName,
          referringUserOrganization: users.organization,
          referringUserEmail: users.email,
          referringUserPhone: users.phone,
          referringUserRole: users.role
        })
        .from(assessments)
        .innerJoin(assessees, eq(assessments.assesseeId, assessees.id))
        .leftJoin(users, eq(assessees.parentId, users.id))
        .orderBy(desc(assessments.createdAt));
      
      // Transform the flat result into nested structure expected by frontend
      return result.map(row => ({
        id: row.id,
        assesseeId: row.assesseeId,
        assessorId: row.assessorId,
        status: row.status,
        paymentStatus: row.paymentStatus,
        depositAmount: row.depositAmount,
        notes: row.notes,
        scheduledDate: row.scheduledDate,
        completedDate: row.completedDate,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        assessee: {
          id: row.assesseeId,
          fullName: row.assesseeFullName,
          dateOfBirth: row.assesseeDateOfBirth,
          email: row.assesseeEmail,
          phone: row.assesseePhone,
          address: row.assesseeAddress,
          parentId: row.assesseeParentId
        },
        referringUser: row.referringUserId ? {
          id: row.referringUserId,
          fullName: row.referringUserFullName,
          organization: row.referringUserOrganization,
          email: row.referringUserEmail,
          phone: row.referringUserPhone,
          role: row.referringUserRole
        } : null,
        // Add referral type based on user role, default to 'individual' if no referring user
        referralType: row.referringUserRole === 'school' ? 'university' : 'individual'
      }));
    } catch (error) {
      logger.error('Error fetching all assessments with users:', error);
      throw error;
    }
  }

  async listAssessmentsByUser(userId: number, role: string): Promise<Assessment[]> {
    if (role === 'parent') {
      return db.select({
        id: assessments.id,
        assesseeId: assessments.assesseeId,
        assessorId: assessments.assessorId,
        status: assessments.status,
        urgency: assessments.urgency,
        paymentStatus: assessments.paymentStatus,
        depositAmount: assessments.depositAmount,
        totalAmount: assessments.totalAmount,
        notes: assessments.notes,
        scheduledDate: assessments.scheduledDate,
        completedDate: assessments.completedDate,
        createdAt: assessments.createdAt,
        updatedAt: assessments.updatedAt
      })
      .from(assessments)
      .innerJoin(assessees, eq(assessments.assesseeId, assessees.id))
      .where(eq(assessees.parentId, userId))
      .orderBy(desc(assessments.createdAt));
    } else if (role === 'school') {
      return db.select({
        id: assessments.id,
        assesseeId: assessments.assesseeId,
        assessorId: assessments.assessorId,
        status: assessments.status,
        urgency: assessments.urgency,
        paymentStatus: assessments.paymentStatus,
        depositAmount: assessments.depositAmount,
        totalAmount: assessments.totalAmount,
        notes: assessments.notes,
        scheduledDate: assessments.scheduledDate,
        completedDate: assessments.completedDate,
        createdAt: assessments.createdAt,
        updatedAt: assessments.updatedAt
      })
      .from(assessments)
      .innerJoin(assessees, eq(assessments.assesseeId, assessees.id))
      .where(eq(assessees.schoolId, userId))
      .orderBy(desc(assessments.createdAt));
    } else if (role === 'assessor') {
      return db.select().from(assessments).where(eq(assessments.assessorId, userId)).orderBy(desc(assessments.createdAt));
    } else {
      return db.select().from(assessments).orderBy(desc(assessments.createdAt));
    }
  }

  async getForm(id: number): Promise<Form | undefined> {
    const [form] = await db.select().from(forms).where(eq(forms.id, id));
    return form;
  }

  async getFormsByAssessment(assessmentId: number): Promise<Form[]> {
    return db.select().from(forms).where(eq(forms.assessmentId, assessmentId));
  }

  async getAllForms(): Promise<Form[]> {
    return db.select().from(forms);
  }

  async getFormByAccessToken(token: string): Promise<Form | undefined> {
    const [form] = await db.select().from(forms).where(eq(forms.accessToken, token));
    return form;
  }

  async createForm(insertForm: InsertForm): Promise<Form> {
    const [form] = await db.insert(forms).values(insertForm).returning();
    return form;
  }

  async updateForm(id: number, formData: Partial<Form>): Promise<Form | undefined> {
    const [form] = await db.update(forms).set(formData).where(eq(forms.id, id)).returning();
    return form;
  }

  async getFormQuestion(id: number): Promise<FormQuestion | undefined> {
    const [question] = await db.select().from(formQuestions).where(eq(formQuestions.id, id));
    return question;
  }

  async getFormQuestionsByType(formType: string): Promise<FormQuestion[]> {
    return db.select().from(formQuestions).where(eq(formQuestions.formType, formType)).orderBy(asc(formQuestions.order));
  }

  async getFormQuestionsBySection(formType: string, section: string): Promise<FormQuestion[]> {
    return db.select().from(formQuestions)
      .where(and(
        eq(formQuestions.formType, formType),
        eq(formQuestions.section, section)
      ))
      .orderBy(asc(formQuestions.order));
  }

  async createFormQuestion(question: InsertFormQuestion): Promise<FormQuestion> {
    const [formQuestion] = await db.insert(formQuestions).values(question).returning();
    return formQuestion;
  }

  async bulkCreateFormQuestions(questions: InsertFormQuestion[]): Promise<FormQuestion[]> {
    return db.insert(formQuestions).values(questions).returning();
  }

  async getFormResponse(id: number): Promise<FormResponse | undefined> {
    const [response] = await db.select().from(formResponses).where(eq(formResponses.id, id));
    return response;
  }

  async getFormResponseByQuestion(formId: number, questionId: number): Promise<FormResponse | undefined> {
    const [response] = await db.select().from(formResponses)
      .where(and(
        eq(formResponses.formId, formId),
        eq(formResponses.questionId, questionId)
      ));
    return response;
  }

  async getFormResponses(formId: number): Promise<FormResponse[]> {
    return db.select().from(formResponses).where(eq(formResponses.formId, formId));
  }

  async createFormResponse(response: InsertFormResponse): Promise<FormResponse> {
    const [formResponse] = await db.insert(formResponses).values(response).returning();
    return formResponse;
  }

  async updateFormResponse(id: number, responseData: Partial<FormResponse>): Promise<FormResponse | undefined> {
    const [response] = await db.update(formResponses).set(responseData).where(eq(formResponses.id, id)).returning();
    return response;
  }

  async bulkUpsertFormResponses(responses: any[]): Promise<FormResponse[]> {
    const results: FormResponse[] = [];
    
    for (const response of responses) {
      const existingResponse = await this.getFormResponseByQuestion(response.formId, response.questionId);
      
      if (existingResponse) {
        const updated = await this.updateFormResponse(existingResponse.id, response);
        if (updated) results.push(updated);
      } else {
        const created = await this.createFormResponse(response);
        results.push(created);
      }
    }
    
    return results;
  }

  async getDocument(id: number): Promise<Document | undefined> {
    const [document] = await db.select().from(documents).where(eq(documents.id, id));
    return document;
  }

  async getDocumentsByAssessment(assessmentId: number): Promise<Document[]> {
    return db.select().from(documents).where(eq(documents.assessmentId, assessmentId));
  }

  async createDocument(insertDocument: InsertDocument): Promise<Document> {
    try {
      logger.info(`Creating document with data:`, JSON.stringify(insertDocument, null, 2));
      
      const result = await db.execute(sql`
        INSERT INTO documents (
          assessment_id, name, type, data, file_path, file_name, 
          original_name, mime_type, file_size, uploaded_by_id, created_at
        ) VALUES (
          ${insertDocument.assessmentId}, ${insertDocument.name}, ${insertDocument.type}, 
          ${insertDocument.data}, ${insertDocument.filePath}, ${insertDocument.fileName},
          ${insertDocument.originalName}, ${insertDocument.mimeType}, ${insertDocument.fileSize},
          ${insertDocument.uploadedById}, NOW()
        ) RETURNING *
      `);
      
      if (result.rows.length === 0) {
        throw new Error('Failed to create document');
      }
      
      const row = result.rows[0] as any;
      const document: Document = {
        id: row.id,
        assessmentId: row.assessment_id,
        name: row.name,
        type: row.type,
        data: row.data,
        filePath: row.file_path,
        fileName: row.file_name,
        originalName: row.original_name,
        mimeType: row.mime_type,
        fileSize: row.file_size,
        uploadedById: row.uploaded_by_id,
        createdAt: row.created_at
      };
      
      logger.info(`Document created successfully with ID: ${document.id}`);
      return document;
    } catch (error) {
      logger.error(`Database error in createDocument:`, error);
      throw error;
    }
  }

  async createActivity(insertActivity: InsertActivity): Promise<Activity> {
    const activityWithDefaults = {
      ...insertActivity,
      userId: insertActivity.userId || null,
      assessmentId: insertActivity.assessmentId || null,
      details: insertActivity.details || null
    };
    
    const [activity] = await db.insert(activities).values(activityWithDefaults).returning();
    return activity;
  }

  async listActivitiesByAssessment(assessmentId: number): Promise<Activity[]> {
    return db.select()
      .from(activities)
      .where(eq(activities.assessmentId, assessmentId))
      .orderBy(desc(activities.createdAt));
  }

  async listRecentActivities(limit: number): Promise<Activity[]> {
    return db.select()
      .from(activities)
      .orderBy(desc(activities.createdAt))
      .limit(limit);
  }

  async createAdminActivityLog(logData: { adminId: number, action: string, targetUserId: number | null, details: string, createdAt: Date }): Promise<void> {
    await db.insert(adminActivityLogs).values(logData);
  }
  
  async getAdminActivityLogs(limit = 100): Promise<any[]> {
    const logs = await db
      .select({
        id: adminActivityLogs.id,
        adminId: adminActivityLogs.adminId,
        targetUserId: adminActivityLogs.targetUserId,
        action: adminActivityLogs.action,
        details: adminActivityLogs.details,
        createdAt: adminActivityLogs.createdAt
      })
      .from(adminActivityLogs)
      .orderBy(desc(adminActivityLogs.createdAt))
      .limit(limit);
      
    return logs;
  }

  async updateUserPassword(userId: number, newPassword: string): Promise<boolean> {
    try {
      const hashedPassword = await hashPassword(newPassword);
      const [updatedUser] = await db
        .update(users)
        .set({ password: hashedPassword })
        .where(eq(users.id, userId))
        .returning();
      
      return !!updatedUser;
    } catch (error) {
      logger.error('Error updating user password:', error);
      return false;
    }
  }

  async updateUserStatus(userId: number, status: string): Promise<boolean> {
    try {
      const [updatedUser] = await db
        .update(users)
        .set({ status: status as any })
        .where(eq(users.id, userId))
        .returning();
      
      return !!updatedUser;
    } catch (error) {
      logger.error('Error updating user status:', error);
      return false;
    }
  }

  async getAssessmentNotes(assessmentId: number): Promise<any[]> {
    try {
      const notes = await db
        .select({
          id: assessmentNotes.id,
          assessmentId: assessmentNotes.assessmentId,
          userId: assessmentNotes.userId,
          note: assessmentNotes.note,
          createdAt: assessmentNotes.createdAt,
          user: {
            fullName: users.fullName,
            role: users.role
          }
        })
        .from(assessmentNotes)
        .leftJoin(users, eq(assessmentNotes.userId, users.id))
        .where(eq(assessmentNotes.assessmentId, assessmentId))
        .orderBy(desc(assessmentNotes.createdAt));
      
      return notes;
    } catch (error) {
      logger.error('Error fetching assessment notes:', error);
      throw error;
    }
  }

  async createAssessmentNote(data: any): Promise<any> {
    try {
      const [note] = await db
        .insert(assessmentNotes)
        .values({
          assessmentId: data.assessmentId,
          userId: data.userId,
          note: data.note,
          createdAt: data.createdAt || new Date()
        })
        .returning();
      
      return note;
    } catch (error) {
      logger.error('Error creating assessment note:', error);
      throw error;
    }
  }

  async updateAssessmentNote(noteId: number, data: any): Promise<any> {
    try {
      const [note] = await db
        .update(assessmentNotes)
        .set({ note: data.note })
        .where(eq(assessmentNotes.id, noteId))
        .returning();
      
      return note;
    } catch (error) {
      logger.error('Error updating assessment note:', error);
      throw error;
    }
  }

  async deleteAssessmentNote(noteId: number): Promise<void> {
    try {
      await db
        .delete(assessmentNotes)
        .where(eq(assessmentNotes.id, noteId));
    } catch (error) {
      logger.error('Error deleting assessment note:', error);
      throw error;
    }
  }

  async getAssessmentWithDetails(id: number): Promise<any | undefined> {
    try {
      const result = await db
        .select({
          // Assessment fields
          id: assessments.id,
          assesseeId: assessments.assesseeId,
          assessorId: assessments.assessorId,
          referringUserId: assessments.referringUserId,
          referralType: assessments.referralType,
          status: assessments.status,
          paymentStatus: assessments.paymentStatus,
          depositAmount: assessments.depositAmount,
          finalAmount: assessments.finalAmount,
          depositPaidAt: assessments.depositPaidAt,
          finalPaidAt: assessments.finalPaidAt,
          notes: assessments.notes,
          scheduledDate: assessments.scheduledDate,
          completedDate: assessments.completedDate,
          createdAt: assessments.createdAt,
          updatedAt: assessments.updatedAt,
          // Assessee fields
          assesseeFullName: assessees.fullName,
          assesseeDateOfBirth: assessees.dateOfBirth,
          assesseeEmail: assessees.email,
          assesseePhone: assessees.phone,
          assesseeAddress: assessees.address,
          assesseeCourseProgram: assessees.courseProgram,
          assesseeYearOfStudy: assessees.yearOfStudy,
          assesseeParentId: assessees.parentId,
          // Parent fields
          parentFullName: users.fullName,
          parentEmail: users.email,
          parentPhone: users.phone,
          parentRole: users.role
        })
        .from(assessments)
        .innerJoin(assessees, eq(assessments.assesseeId, assessees.id))
        .leftJoin(users, eq(assessees.parentId, users.id))
        .where(eq(assessments.id, id));

      if (result.length === 0) {
        return undefined;
      }

      const row = result[0];
      
      // Get referring user details if different from parent
      let referringUser = null;
      if (row.referringUserId) {
        const [referringUserData] = await db
          .select()
          .from(users)
          .where(eq(users.id, row.referringUserId));
        
        if (referringUserData) {
          referringUser = {
            id: referringUserData.id,
            fullName: referringUserData.fullName,
            organization: referringUserData.organization,
            department: referringUserData.department,
            position: referringUserData.position,
            email: referringUserData.email,
            phone: referringUserData.phone,
            role: referringUserData.role
          };
        }
      }

      // Get referral details from referrals table
      let referral = null;
      const [referralData] = await db
        .select()
        .from(referrals)
        .where(eq(referrals.assessmentId, id));
      
      if (referralData) {
        referral = referralData;
      }

      // Get school details if applicable
      let referringSchool = null;
      if (row.referralType === 'school') {
        // Try to find school by name from referral data
        if (referral && referral.schoolName) {
          const [schoolData] = await db
            .select()
            .from(schools)
            .where(eq(schools.name, referral.schoolName));
          
          if (schoolData) {
            referringSchool = schoolData;
          }
        }
      }

      // Transform into expected structure
      return {
        id: row.id,
        assesseeId: row.assesseeId,
        assessorId: row.assessorId,
        referringUserId: row.referringUserId,
        referralType: row.referralType,
        status: row.status,
        paymentStatus: row.paymentStatus,
        depositAmount: row.depositAmount,
        finalAmount: row.finalAmount,
        depositPaidAt: row.depositPaidAt,
        finalPaidAt: row.finalPaidAt,
        notes: row.notes,
        scheduledDate: row.scheduledDate,
        completedDate: row.completedDate,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        assessee: {
          id: row.assesseeId,
          fullName: row.assesseeFullName,
          dateOfBirth: row.assesseeDateOfBirth,
          email: row.assesseeEmail,
          phone: row.assesseePhone,
          address: row.assesseeAddress,
          courseProgram: row.assesseeCourseProgram,
          yearOfStudy: row.assesseeYearOfStudy,
          parentId: row.assesseeParentId,
          parent: row.parentFullName ? {
            id: row.assesseeParentId,
            fullName: row.parentFullName,
            email: row.parentEmail,
            phone: row.parentPhone,
            role: row.parentRole
          } : null
        },
        referringUser: referringUser,
        referral: referral,
        referringSchool: referringSchool
      };
    } catch (error) {
      logger.error('Error fetching assessment with details:', error);
      throw error;
    }
  }

  // Placeholder methods for missing interface requirements
  async getUniversity(id: number): Promise<any> { return null; }
  async getUniversityByEmailDomain(domain: string): Promise<any> { return null; }
  async createUniversity(data: any): Promise<any> { return null; }
  async updateUniversity(id: number, data: any): Promise<any> { return null; }
  async listUniversities(): Promise<any[]> { return []; }
  async getConversation(id: number): Promise<any> { return null; }
  async getConversationWithParticipants(id: number): Promise<any> { return null; }
  async createConversation(data: any): Promise<any> { return null; }
  async updateConversation(id: number, data: any): Promise<any> { return null; }
  async getConversationsByUser(userId: number): Promise<any[]> { return []; }
  async getConversationsByAssessment(assessmentId: number): Promise<any[]> { return []; }
  async addParticipantToConversation(conversationId: number, userId: number): Promise<any> { return null; }
  async removeParticipantFromConversation(conversationId: number, userId: number): Promise<boolean> { return false; }
  async getMessage(id: number): Promise<any> { return null; }
  async getMessageWithSender(id: number): Promise<any> { return null; }
  async createMessage(data: any): Promise<any> { return null; }
  async updateMessage(id: number, data: any): Promise<any> { return null; }
  async getMessagesByConversation(conversationId: number): Promise<any[]> { return []; }
  async getMessagesByUser(userId: number): Promise<any[]> { return []; }
  async markMessageAsRead(messageId: number, userId: number): Promise<boolean> { return false; }
  async getNotification(id: number): Promise<any> {
    try {
      const [notification] = await db
        .select()
        .from(notifications)
        .where(eq(notifications.id, id))
        .limit(1);
      return notification || null;
    } catch (error) {
      logger.error('Error getting notification:', error);
      throw error;
    }
  }

  async createNotification(data: any): Promise<any> {
    return withDatabaseRetry(async () => {
      const [notification] = await db
        .insert(notifications)
        .values({
          userId: data.userId,
          type: data.type,
          title: data.title,
          content: data.content || data.message,
          status: 'unread',
          url: data.url,
          sourceId: data.sourceId,
          sourceType: data.sourceType,
          data: data.data
        })
        .returning();
      return notification;
    });
  }

  async getUnreadNotificationCount(userId: number): Promise<number> {
    return withDatabaseRetry(async () => {
      const result = await db
        .select({ count: count() })
        .from(notifications)
        .where(and(
          eq(notifications.userId, userId),
          eq(notifications.status, 'unread')
        ));
      return result[0]?.count || 0;
    }, { maxRetries: 2 });
  }

  async getNotificationsByUser(userId: number, limit?: number, includeRead?: boolean): Promise<any[]> {
    return withDatabaseRetry(async () => {
      let conditions = [eq(notifications.userId, userId)];

      if (!includeRead) {
        conditions.push(eq(notifications.status, 'unread'));
      }

      const result = await db
        .select()
        .from(notifications)
        .where(and(...conditions))
        .orderBy(desc(notifications.createdAt))
        .limit(limit || 20);

      return result;
    }, { maxRetries: 2 });
  }

  async markNotificationAsRead(notificationId: number): Promise<boolean> {
    return withDatabaseRetry(async () => {
      await db
        .update(notifications)
        .set({ 
          status: 'read',
          readAt: new Date()
        })
        .where(eq(notifications.id, notificationId));
      return true;
    });
  }

  async markAllNotificationsAsRead(userId: number): Promise<boolean> {
    return withDatabaseRetry(async () => {
      await db
        .update(notifications)
        .set({ 
          status: 'read',
          readAt: new Date()
        })
        .where(and(
          eq(notifications.userId, userId),
          eq(notifications.status, 'unread')
        ));
      return true;
    });
  }

  // Notification Preferences
  async getNotificationPreferences(userId: number): Promise<NotificationPreference | undefined> {
    try {
      const [prefs] = await db
        .select()
        .from(notificationPreferences)
        .where(eq(notificationPreferences.userId, userId));
      return prefs;
    } catch (error) {
      logger.error('Error in getNotificationPreferences:', error);
      return undefined;
    }
  }

  async createOrUpdateNotificationPreferences(userId: number, preferences: any): Promise<NotificationPreference> {
    try {
      // Check if preferences already exist
      const existingPrefs = await this.getNotificationPreferences(userId);

      if (existingPrefs) {
        // Update existing preferences
        const [updatedPrefs] = await db
          .update(notificationPreferences)
          .set({
            preferences,
            updatedAt: new Date()
          })
          .where(eq(notificationPreferences.id, existingPrefs.id))
          .returning();
        return updatedPrefs;
      } else {
        // Create new preferences
        const [newPrefs] = await db
          .insert(notificationPreferences)
          .values({
            userId,
            preferences,
            updatedAt: new Date()
          })
          .returning();
        return newPrefs;
      }
    } catch (error) {
      logger.error('Error in createOrUpdateNotificationPreferences:', error);
      throw error;
    }
  }

  // Referrals
  async getReferral(id: number): Promise<Referral | undefined> {
    try {
      const { rows } = await pool.query('SELECT * FROM referrals WHERE id = $1', [id]);
      return rows[0] as Referral | undefined;
    } catch (error) {
      logger.error('Error in getReferral:', error);
      return undefined;
    }
  }

  async getReferralByTrackingId(trackingId: string): Promise<Referral | undefined> {
    try {
      const { rows } = await pool.query('SELECT * FROM referrals WHERE tracking_id = $1', [trackingId]);
      return rows[0] as Referral | undefined;
    } catch (error) {
      logger.error('Error in getReferralByTrackingId:', error);
      return undefined;
    }
  }

  async getReferralByAssessmentId(assessmentId: number): Promise<Referral | undefined> {
    try {
      const { rows } = await pool.query('SELECT * FROM referrals WHERE assessment_id = $1', [assessmentId]);
      return rows[0] as Referral | undefined;
    } catch (error) {
      logger.error('Error in getReferralByAssessmentId:', error);
      return undefined;
    }
  }

  async createReferral(insertReferral: InsertReferral): Promise<Referral> {
    const [referralId] = await db
      .insert(referrals)
      .values(insertReferral)
      .returning({ id: referrals.id });

    const { rows } = await pool.query('SELECT * FROM referrals WHERE id = $1', [
      referralId.id,
    ]);
    return rows[0] as Referral;
  }

  async updateReferral(id: number, referralData: Partial<Referral>): Promise<Referral | undefined> {
    const [refId] = await db
      .update(referrals)
      .set({
        ...referralData,
        updatedAt: new Date(),
      })
      .where(eq(referrals.id, id))
      .returning({ id: referrals.id });

    const { rows } = await pool.query('SELECT * FROM referrals WHERE id = $1', [refId.id]);
    return rows[0] as Referral | undefined;
  }

  async storePasswordResetToken(userId: number, token: string, expires: Date): Promise<boolean> {
    try {
      // Invalidate existing unused tokens for this user
      await db
        .update(passwordResetTokens)
        .set({ usedAt: new Date() })
        .where(and(eq(passwordResetTokens.userId, userId), isNull(passwordResetTokens.usedAt)));

      await db
        .insert(passwordResetTokens)
        .values({ userId, token, expires, createdAt: new Date() });

      return true;
    } catch (error) {
      logger.error('Error storing password reset token:', error);
      return false;
    }
  }

  async getPasswordResetToken(token: string): Promise<{ userId: number; expires: Date } | null> {
    try {
      const [resetToken] = await db
        .select()
        .from(passwordResetTokens)
        .where(
          and(
            eq(passwordResetTokens.token, token),
            isNull(passwordResetTokens.usedAt),
            gt(passwordResetTokens.expires, new Date())
          )
        );

      if (!resetToken) {
        return null;
      }

      return {
        userId: resetToken.userId,
        expires: resetToken.expires,
      };
    } catch (error) {
      logger.error('Error retrieving password reset token:', error);
      return null;
    }
  }

  async invalidatePasswordResetToken(token: string): Promise<boolean> {
    try {
      await db
        .update(passwordResetTokens)
        .set({ usedAt: new Date() })
        .where(eq(passwordResetTokens.token, token));

      return true;
    } catch (error) {
      logger.error('Error invalidating password reset token:', error);
      return false;
    }
  }
  async getIssue(id: number): Promise<any> { return null; }
  async createIssue(data: any): Promise<any> { return null; }
  async updateIssue(id: number, data: any): Promise<any> { return null; }
  async listIssues(filters?: any): Promise<any[]> { return []; }
  async resolveIssue(id: number, resolvedById: number, resolutionNotes?: string): Promise<any> { return null; }
  async assignIssue(id: number, assignedToId: number): Promise<any> { return null; }
  async createIssueComment(data: any): Promise<any> { return null; }
  async getIssueComments(issueId: number): Promise<any[]> { return []; }
  async updateIssueComment(id: number, data: any): Promise<any> { return null; }
}