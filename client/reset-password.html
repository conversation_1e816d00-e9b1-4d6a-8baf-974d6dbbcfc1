<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>NeuroElevate - Reset Password</title>
    <meta name="description" content="NeuroElevate Assessment Platform Reset Password" />
    <!-- Redirect to the SPA with the full path preserved -->
    <script>
      // Get the current URL path
      const path = window.location.pathname;
      const token = path.replace('/reset-password/', '');
      
      // Redirect to the main application with the token as a query parameter
      window.location.href = `/?reset_token=${token}`;
    </script>
  </head>
  <body>
    <div style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column; font-family: system-ui, sans-serif;">
      <h1 style="margin-bottom: 20px;">NeuroElevate</h1>
      <p>Redirecting to reset password page...</p>
      <div style="width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
    </div>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </body>
</html>