<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4a6cf7;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #3a5ce5;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow: auto;
            max-height: 400px;
        }
        .result {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Password Reset Email Debug Test</h1>
    
    <div class="form-group">
        <label for="email">Email Address:</label>
        <input type="email" id="email" placeholder="Enter email address">
    </div>
    
    <button id="testPasswordReset">Test Password Reset Email</button>
    <button id="testNormalEmail">Test Regular Email</button>
    
    <div class="result">
        <h3>Result:</h3>
        <pre id="result">No test run yet</pre>
    </div>
    
    <script>
        document.getElementById('testPasswordReset').addEventListener('click', async () => {
            const email = document.getElementById('email').value;
            const resultElement = document.getElementById('result');
            
            if (!email) {
                resultElement.textContent = 'Please enter an email address';
                return;
            }
            
            resultElement.textContent = 'Sending request...';
            
            try {
                const response = await fetch('/api/debug/password-reset-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email })
                });
                
                const data = await response.json();
                resultElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultElement.textContent = `Error: ${error.message}`;
            }
        });
        
        document.getElementById('testNormalEmail').addEventListener('click', async () => {
            const email = document.getElementById('email').value;
            const resultElement = document.getElementById('result');
            
            if (!email) {
                resultElement.textContent = 'Please enter an email address';
                return;
            }
            
            resultElement.textContent = 'Sending request...';
            
            try {
                const response = await fetch('/api/debug/send-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email })
                });
                
                const data = await response.json();
                resultElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultElement.textContent = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>