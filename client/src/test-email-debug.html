<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NeuroElevate Email Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f7fb;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #4a6cf7;
      margin-top: 0;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="email"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    button {
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      flex: 1;
    }
    button.primary {
      background-color: #4a6cf7;
      color: white;
    }
    button.secondary {
      background-color: #10b981;
      color: white;
    }
    button.warning {
      background-color: #f59e0b;
      color: white;
    }
    .results {
      margin-top: 20px;
      padding: 15px;
      background-color: #f3f4f6;
      border-radius: 4px;
      border-left: 4px solid #4a6cf7;
    }
    .error {
      color: #ef4444;
      margin-top: 5px;
    }
    .loading {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
      margin-right: 8px;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>NeuroElevate Email Test</h1>
    <p>Use this utility to test email delivery with different templates.</p>
    
    <div class="form-group">
      <label for="email">Email Address</label>
      <input type="email" id="email" placeholder="Enter email address" autofocus>
      <div id="emailError" class="error"></div>
    </div>
    
    <div class="button-group">
      <button id="testPasswordReset" class="primary">
        Test Password Reset Email
      </button>
      <button id="testTemplate" class="secondary">
        Test Template Email
      </button>
      <button id="testDirect" class="warning">
        Test Direct Email
      </button>
    </div>
    
    <div id="results" class="results" style="display: none;">
      <h3>Test Results</h3>
      <pre id="resultsContent"></pre>
    </div>
  </div>
  
  <script>
    // Helper for CSRF token
    async function getCsrfToken() {
      try {
        const response = await fetch('/api/csrf-token');
        const data = await response.json();
        return data.csrfToken;
      } catch (error) {
        console.error('Failed to get CSRF token:', error);
        return null;
      }
    }
    
    // Generic function to handle button clicks
    async function handleTestClick(type) {
      const emailInput = document.getElementById('email');
      const emailError = document.getElementById('emailError');
      const results = document.getElementById('results');
      const resultsContent = document.getElementById('resultsContent');
      const button = document.getElementById(`test${type}`);
      const originalText = button.textContent;
      
      // Validate email
      if (!emailInput.value) {
        emailError.textContent = 'Please enter an email address';
        return;
      }
      emailError.textContent = '';
      
      // Show loading state
      button.innerHTML = `<span class="loading"></span>Testing...`;
      button.disabled = true;
      
      try {
        // Get CSRF token
        const csrfToken = await getCsrfToken();
        
        // Make API request
        const response = await fetch('/api/debug/test-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
          },
          body: JSON.stringify({
            email: emailInput.value,
            testType: type.toLowerCase()
          })
        });
        
        const data = await response.json();
        
        // Display results
        results.style.display = 'block';
        resultsContent.textContent = JSON.stringify(data, null, 2);
        
      } catch (error) {
        console.error(`Error testing ${type} email:`, error);
        results.style.display = 'block';
        resultsContent.textContent = `Error: ${error.message}`;
      } finally {
        // Reset button state
        button.innerHTML = originalText;
        button.disabled = false;
      }
    }
    
    // Add event listeners to buttons
    document.getElementById('testPasswordReset').addEventListener('click', () => handleTestClick('PasswordReset'));
    document.getElementById('testTemplate').addEventListener('click', () => handleTestClick('Template'));
    document.getElementById('testDirect').addEventListener('click', () => handleTestClick('Direct'));
    
    // Clear error when typing
    document.getElementById('email').addEventListener('input', () => {
      document.getElementById('emailError').textContent = '';
    });
  </script>
</body>
</html>