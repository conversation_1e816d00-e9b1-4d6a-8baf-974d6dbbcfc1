import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useQuery, useMutation } from "@tanstack/react-query";
import { User } from "@shared/schema";
import { apiRequest, queryClient } from "../lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";

// Define schemas
const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

const registerSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  fullName: z.string().min(1, "Full name is required"),
  role: z.enum(["admin", "assessor", "school", "parent", "assessee", "university"]),
  phone: z.string().nullable().optional(),
  organization: z.string().nullable().optional(),
});

type LoginData = z.infer<typeof loginSchema>;
type RegisterData = z.infer<typeof registerSchema>;

// Create the context interface
interface AuthContextValue {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  login: (data: LoginData) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
}

// Create the context with a default value
export const AuthContext = createContext<AuthContextValue | null>(null);

// Create a provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // Fetch the user data
  const { 
    data: userData, 
    isLoading: isUserLoading,
    refetch: refetchUser
  } = useQuery({
    queryKey: ['/api/user'],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', '/api/user');
        if (res.status === 401) {
          return null;
        }
        if (!res.ok) {
          throw new Error('Failed to fetch user');
        }
        return res.json();
      } catch (error) {
        console.error('Error fetching user:', error);
        return null;
      }
    },
    retry: false,
  });

  // Update the user state when userData changes
  useEffect(() => {
    setUser(userData || null);
  }, [userData]);

  // Login function
  const login = async (credentials: LoginData) => {
    try {
      setError(null);
      const res = await apiRequest('POST', '/api/login', credentials);
      if (!res.ok) {
        throw new Error('Invalid username or password');
      }
      const data = await res.json();
      
      // Set user data in state and query client
      setUser(data);
      queryClient.setQueryData(['/api/user'], data);
      
      // Show success toast
      toast({
        title: 'Login successful',
        description: `Welcome back, ${data.fullName}`,
      });
      
      // Check if we need to redirect after login
      if (data.redirect) {
        console.log(`Redirecting after login to: ${data.redirect}`);
        // Use a slight delay to ensure the React state updates first
        setTimeout(() => {
          window.location.href = data.redirect;
        }, 100);
      }
      
      return data;
    } catch (error: any) {
      setError(error);
      toast({
        title: 'Login failed',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }
  };

  // Register function
  const register = async (data: RegisterData) => {
    try {
      setError(null);
      const res = await apiRequest('POST', '/api/register', data);
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.message || 'Registration failed');
      }
      const user = await res.json();
      setUser(user);
      queryClient.setQueryData(['/api/user'], user);
      toast({
        title: 'Registration successful',
        description: `Welcome, ${user.fullName}`,
      });
    } catch (error: any) {
      setError(error);
      toast({
        title: 'Registration failed',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setError(null);
      const res = await apiRequest('POST', '/api/logout');
      if (!res.ok) {
        throw new Error('Logout failed');
      }
      setUser(null);
      queryClient.setQueryData(['/api/user'], null);
      toast({
        title: 'Logged out successfully',
      });
    } catch (error: any) {
      setError(error);
      toast({
        title: 'Logout failed',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }
  };

  // Provide the context value
  const contextValue: AuthContextValue = {
    user,
    isLoading: isUserLoading,
    error,
    login,
    register,
    logout,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}