import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { User } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { apiRequest, queryClient } from '../lib/queryClient';

// Auth context type definition
type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  register: (userData: any) => Promise<void>;
};

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  // Load user on initial mount
  useEffect(() => {
    const loadUser = async () => {
      try {
        setIsLoading(true);
        const res = await apiRequest('GET', '/api/user');

        if (res.status === 401) {
          setUser(null);
          return;
        }

        if (!res.ok) {
          throw new Error('Failed to fetch user data');
        }

        const userData = await res.json();
        setUser(userData);
      } catch (err) {
        console.error('Error loading user:', err);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // Login function
  const login = async (username: string, password: string): Promise<void> => {
    try {
      setError(null);
      setIsLoading(true);
      
      const res = await apiRequest('POST', '/api/login', { username, password });
      
      if (!res.ok) {
        throw new Error('Invalid username or password');
      }
      
      const userData = await res.json();
      setUser(userData);
      
      toast({
        title: 'Login successful',
        description: `Welcome back, ${userData.fullName}`,
      });
      
    } catch (err: any) {
      setError(err);
      toast({
        title: 'Login failed',
        description: err.message || 'An error occurred during login',
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData: any): Promise<void> => {
    try {
      setError(null);
      setIsLoading(true);
      
      const res = await apiRequest('POST', '/api/register', userData);
      
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.message || 'Registration failed');
      }
      
      const user = await res.json();
      setUser(user);
      
      toast({
        title: 'Registration successful',
        description: `Welcome, ${user.fullName}`,
      });
      
    } catch (err: any) {
      setError(err);
      toast({
        title: 'Registration failed',
        description: err.message || 'An error occurred during registration',
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      setError(null);
      setIsLoading(true);
      
      const res = await apiRequest('POST', '/api/logout');
      
      if (!res.ok) {
        throw new Error('Logout failed');
      }
      
      setUser(null);
      
      toast({
        title: 'Logged out successfully',
      });
      
    } catch (err: any) {
      setError(err);
      toast({
        title: 'Logout failed',
        description: err.message || 'An error occurred during logout',
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Context value
  const value = {
    user,
    isLoading,
    error,
    login,
    logout,
    register,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook to use the auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}