import React, { useState } from 'react';
import AppLayout from '../components/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { 
  ArrowLeft, 
  FileCheck, 
  FileText, 
  CalendarDays, 
  Clock, 
  Users, 
  CreditCard,
  PencilLine,
  FileUp,
  Plus,
  Edit,
  Check,
  X,
  Save,
  Upload,
  Download,
  Trash2
} from 'lucide-react';
import { Link, useRoute } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { User, Form } from '@shared/schema';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

// Assessment Notes Component
interface AssessmentNote {
  id: number;
  assessmentId: number;
  userId: number;
  note: string;
  createdAt: string;
  updatedAt: string;
  user: {
    fullName: string;
    role: string;
  };
}

interface AssessmentNotesSectionProps {
  assessmentId: number;
  user?: User | null;
}

function AssessmentNotesSection({ assessmentId, user }: AssessmentNotesSectionProps) {
  const [newNote, setNewNote] = useState('');
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [editingNoteId, setEditingNoteId] = useState<number | null>(null);
  const [editingNoteText, setEditingNoteText] = useState('');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get notes for this assessment
  const { data: notes = [], isLoading: notesLoading } = useQuery<AssessmentNote[]>({
    queryKey: [`/api/assessments/${assessmentId}/notes`],
    enabled: !!assessmentId && !!user,
  });

  // Create note mutation
  const createNoteMutation = useMutation({
    mutationFn: async (noteText: string) => {
      const res = await apiRequest("POST", `/api/assessments/${assessmentId}/notes`, { note: noteText });
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || "Failed to create note");
      }
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Note added",
        description: "Your note has been added to the assessment.",
      });
      setNewNote('');
      setIsAddingNote(false);
      queryClient.invalidateQueries({ queryKey: [`/api/assessments/${assessmentId}/notes`] });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to add note",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Update note mutation
  const updateNoteMutation = useMutation({
    mutationFn: async ({ noteId, noteText }: { noteId: number; noteText: string }) => {
      const res = await apiRequest("PATCH", `/api/assessments/${assessmentId}/notes/${noteId}`, { note: noteText });
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || "Failed to update note");
      }
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Note updated",
        description: "The note has been updated.",
      });
      setEditingNoteId(null);
      setEditingNoteText('');
      queryClient.invalidateQueries({ queryKey: [`/api/assessments/${assessmentId}/notes`] });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update note",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Delete note mutation
  const deleteNoteMutation = useMutation({
    mutationFn: async (noteId: number) => {
      const res = await apiRequest("DELETE", `/api/assessments/${assessmentId}/notes/${noteId}`);
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || "Failed to delete note");
      }
    },
    onSuccess: () => {
      toast({
        title: "Note deleted",
        description: "The note has been deleted.",
      });
      queryClient.invalidateQueries({ queryKey: [`/api/assessments/${assessmentId}/notes`] });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete note",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const handleAddNote = () => {
    if (!newNote.trim()) return;
    createNoteMutation.mutate(newNote.trim());
  };

  const handleUpdateNote = (noteId: number) => {
    if (!editingNoteText.trim()) return;
    updateNoteMutation.mutate({ noteId, noteText: editingNoteText.trim() });
  };

  const handleDeleteNote = (noteId: number) => {
    if (window.confirm('Are you sure you want to delete this note?')) {
      deleteNoteMutation.mutate(noteId);
    }
  };

  const startEditingNote = (note: AssessmentNote) => {
    setEditingNoteId(note.id);
    setEditingNoteText(note.note);
  };

  const cancelEditing = () => {
    setEditingNoteId(null);
    setEditingNoteText('');
  };

  const canAddNotes = ['admin', 'assessor'].includes(user?.role || '');
  const canEditNote = (note: AssessmentNote) => user?.role === 'admin' || note.userId === user?.id;

  if (notesLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Assessment Notes</h3>
        </div>
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Assessment Notes</h3>
        {canAddNotes && !isAddingNote && (
          <Button size="sm" onClick={() => setIsAddingNote(true)}>
            <Plus className="h-4 w-4 mr-1" />
            Add Note
          </Button>
        )}
      </div>

      {/* Add new note form */}
      {isAddingNote && (
        <Card className="border-primary/20">
          <CardContent className="p-4">
            <div className="space-y-3">
              <textarea
                className="w-full min-h-[100px] p-3 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-primary/20"
                placeholder="Add your note here..."
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
              />
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setIsAddingNote(false);
                    setNewNote('');
                  }}
                  disabled={createNoteMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleAddNote}
                  disabled={!newNote.trim() || createNoteMutation.isPending}
                >
                  {createNoteMutation.isPending ? 'Adding...' : 'Add Note'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notes list */}
      <div className="space-y-3">
        {notes.length > 0 ? (
          notes.map((note) => (
            <Card key={note.id} className="border-l-4 border-l-primary/30">
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <span className="font-medium text-foreground">{note.user.fullName}</span>
                      <span>•</span>
                      <span className="capitalize">{note.user.role}</span>
                      <span>•</span>
                      <span>{format(new Date(note.createdAt), "MMM d, yyyy 'at' h:mm a")}</span>
                      {note.updatedAt !== note.createdAt && (
                        <>
                          <span>•</span>
                          <span className="italic">edited</span>
                        </>
                      )}
                    </div>
                    {canEditNote(note) && (
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => startEditingNote(note)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                          onClick={() => handleDeleteNote(note.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                  
                  {editingNoteId === note.id ? (
                    <div className="space-y-3">
                      <textarea
                        className="w-full min-h-[100px] p-3 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-primary/20"
                        value={editingNoteText}
                        onChange={(e) => setEditingNoteText(e.target.value)}
                      />
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelEditing}
                          disabled={updateNoteMutation.isPending}
                        >
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleUpdateNote(note.id)}
                          disabled={!editingNoteText.trim() || updateNoteMutation.isPending}
                        >
                          {updateNoteMutation.isPending ? 'Updating...' : 'Update'}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm whitespace-pre-wrap">{note.note}</div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-8">
            <PencilLine className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No notes have been added yet</p>
            {canAddNotes && (
              <p className="text-sm text-muted-foreground mt-1">Click "Add Note" to get started</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

interface AssessmentDetailPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function AssessmentDetailPage({ user, logout }: AssessmentDetailPageProps) {
  const [_, params] = useRoute('/assessments/:id');
  const id = params?.id;
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Editable fields state
  const [isEditing, setIsEditing] = useState(false);
  const [editedAssesseeData, setEditedAssesseeData] = useState({
    fullName: '',
    dateOfBirth: null as Date | null,
    email: '',
    phone: '',
    address: ''
  });
  const [editedAssessmentData, setEditedAssessmentData] = useState({
    assessorId: null as number | null,
    scheduledDate: null as Date | null
  });
  const [scheduleCalendarOpen, setScheduleCalendarOpen] = useState(false)
  
  // Document upload state
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState('');
  const [documentName, setDocumentName] = useState('');

  // Get assessors for dropdown
  const { data: assessors = [] } = useQuery({
    queryKey: ['/api/users'],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/admin/users?role=assessor");
      if (!res.ok) {
        return [];
      }
      return res.json();
    },
    enabled: !!user && ['admin', 'assessor'].includes(user.role || ''),
  });

  // Define explicit types for assessment data to handle null safely
  interface AssesseeData {
    fullName?: string;
    dateOfBirth?: string;
    email?: string;
    phone?: string;
    address?: string;
  }

  interface AssessmentData {
    id?: number;
    assesseeId?: number;
    assessorId?: number | null;
    scheduledDate?: string | null;
    assessee?: AssesseeData;
    forms?: Form[];
  }

  // Get assessment data with proper typing
  const { data: assessment, isLoading, refetch } = useQuery<AssessmentData>({
    queryKey: [`/api/assessments/${id}`],
    enabled: !!id && !!user,
  });

  // Get documents for this assessment
  const { data: documents = [] } = useQuery<any[]>({
    queryKey: [`/api/assessments/${id}/documents`],
    enabled: !!id && !!user,
  });

  // Update editable fields when assessment data changes
  React.useEffect(() => {
    if (assessment) {
      // Handle case where assessee exists
      if (assessment.assessee) {
        setEditedAssesseeData({
          fullName: assessment.assessee.fullName || '',
          dateOfBirth: assessment.assessee.dateOfBirth ? new Date(assessment.assessee.dateOfBirth) : null,
          email: assessment.assessee.email || '',
          phone: assessment.assessee.phone || '',
          address: assessment.assessee.address || ''
        });
      } else {
        // Handle new assessment case - initialize with empty values
        setEditedAssesseeData({
          fullName: '',
          dateOfBirth: null,
          email: '',
          phone: '',
          address: ''
        });
      }
      
      setEditedAssessmentData({
        assessorId: assessment.assessorId || null,
        scheduledDate: assessment.scheduledDate ? new Date(assessment.scheduledDate) : null
      });
    }
  }, [assessment]);

  // Update assessment mutation
  const updateAssessmentMutation = useMutation({
    mutationFn: async (data: any) => {
      const res = await apiRequest("PATCH", `/api/assessments/${id}`, data);
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || "Failed to update assessment");
      }
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Assessment updated",
        description: "The assessment details have been successfully updated.",
      });
      setIsEditing(false);
      queryClient.invalidateQueries({ queryKey: [`/api/assessments/${id}`] });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Update failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Handle saving edited data
  const handleSaveEdit = () => {
    updateAssessmentMutation.mutate({
      assessorId: editedAssessmentData.assessorId,
      scheduledDate: editedAssessmentData.scheduledDate
        ? editedAssessmentData.scheduledDate.toISOString()
        : null,
      assessee: {
        fullName: editedAssesseeData.fullName,
        dateOfBirth: editedAssesseeData.dateOfBirth
          ? editedAssesseeData.dateOfBirth.toISOString()
          : null,
        email: editedAssesseeData.email,
        phone: editedAssesseeData.phone,
        address: editedAssesseeData.address
      }
    });
  };

  // Document upload mutation
  const uploadDocumentMutation = useMutation({
    mutationFn: async ({ file, type, name }: { file: File; type: string; name: string }) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      formData.append('name', name);

      const response = await fetch(`/api/assessments/${id}/documents`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to upload document');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Document uploaded",
        description: "The document has been successfully uploaded.",
      });
      setUploadDialogOpen(false);
      setSelectedFile(null);
      setDocumentType('');
      setDocumentName('');
      queryClient.invalidateQueries({ queryKey: [`/api/assessments/${id}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/assessments/${id}/documents`] });
      refetch();
    },
    onError: (error: Error) => {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      if (!documentName) {
        setDocumentName(file.name);
      }
    }
  };

  // Handle document upload
  const handleDocumentUpload = () => {
    if (!selectedFile || !documentType || !documentName) {
      toast({
        title: "Missing information",
        description: "Please select a file, document type, and provide a name.",
        variant: "destructive",
      });
      return;
    }

    uploadDocumentMutation.mutate({
      file: selectedFile,
      type: documentType,
      name: documentName
    });
  };

  // Cancel editing
  const handleCancelEdit = () => {
    if (assessment) {
      // Reset to original values
      if (assessment.assessee) {
        setEditedAssesseeData({
          fullName: assessment.assessee.fullName || '',
          dateOfBirth: assessment.assessee.dateOfBirth ? new Date(assessment.assessee.dateOfBirth) : null,
          email: assessment.assessee.email || '',
          phone: assessment.assessee.phone || '',
          address: assessment.assessee.address || ''
        });
      } else {
        // Handle new assessment case - reset to empty values
        setEditedAssesseeData({
          fullName: '',
          dateOfBirth: null,
          email: '',
          phone: '',
          address: ''
        });
      }
      
      setEditedAssessmentData({
        assessorId: assessment.assessorId || null,
        scheduledDate: assessment.scheduledDate ? new Date(assessment.scheduledDate) : null
      });
    }
    setIsEditing(false);
  };

  // Mock assessment data for UI preview
  const mockAssessment = {
    id: parseInt(id || '1'),
    status: 'scheduled',
    referralType: 'school',
    createdAt: '2023-04-01T10:30:00Z',
    updatedAt: '2023-04-10T14:45:00Z',
    paymentStatus: 'deposit_paid',
    depositAmount: 15000, // in pennies
    finalAmount: 120000, // in pennies
    depositPaidAt: '2023-04-05T09:30:00Z',
    finalPaidAt: null,
    scheduledDate: '2023-05-15T14:00:00Z',
    completedDate: null,
    notes: 'Mother reports concerns about reading speed and comprehension. School has provided additional support but progress is limited.',
    assessee: {
      id: 1,
      fullName: 'Alex Johnson',
      dateOfBirth: '2008-05-10',
      email: '<EMAIL>',
      phone: '+44 7700 900123',
      address: '123 Education Lane, London, UK',
    },
    assessor: {
      id: 2,
      fullName: 'Dr. Sarah Wilson',
      email: '<EMAIL>',
      phone: '+44 7700 900456',
    },
    referringUser: {
      id: 3,
      fullName: 'Jane Smith',
      email: '<EMAIL>',
      organization: 'Westfield Primary School',
    },
    forms: [
      {
        id: 1,
        formType: 'parent',
        status: 'completed',
        completedAt: '2023-04-07T16:20:00Z',
      },
      {
        id: 2,
        formType: 'school',
        status: 'completed',
        completedAt: '2023-04-06T11:30:00Z',
      },
      {
        id: 3,
        formType: 'assessor',
        status: 'not_started',
        completedAt: null,
      }
    ],
    documents: [
      {
        id: 1,
        name: 'School Report.pdf',
        type: 'report',
        createdAt: '2023-04-06T11:35:00Z',
      },
      {
        id: 2,
        name: 'Previous Assessment.pdf',
        type: 'assessment',
        createdAt: '2023-04-07T16:25:00Z',
      }
    ],
    activities: [
      {
        id: 1,
        action: 'Assessment created',
        details: 'Referral received from Westfield Primary School',
        userId: 3,
        createdAt: '2023-04-01T10:30:00Z',
      },
      {
        id: 2,
        action: 'Form completed',
        details: 'School form submitted',
        userId: 3,
        createdAt: '2023-04-06T11:30:00Z',
      },
      {
        id: 3,
        action: 'Document uploaded',
        details: 'School Report.pdf',
        userId: 3,
        createdAt: '2023-04-06T11:35:00Z',
      },
      {
        id: 4,
        action: 'Form completed',
        details: 'Parent form submitted',
        userId: 4,
        createdAt: '2023-04-07T16:20:00Z',
      },
      {
        id: 5,
        action: 'Document uploaded',
        details: 'Previous Assessment.pdf',
        userId: 4,
        createdAt: '2023-04-07T16:25:00Z',
      },
      {
        id: 6,
        action: 'Deposit paid',
        details: '£150.00',
        userId: 4,
        createdAt: '2023-04-05T09:30:00Z',
      },
      {
        id: 7,
        action: 'Assessment scheduled',
        details: 'May 15, 2023 at 2:00 PM',
        userId: 2,
        createdAt: '2023-04-10T14:45:00Z',
      }
    ]
  };

  // Format date to readable format (without time)
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Format date with time when needed
  const formatDateWithTime = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (amount === null) return '£0.00';
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount / 100);
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const statusConfig: Record<string, { color: string, label: string }> = {
      VerificationPending: { color: 'bg-blue-100 text-blue-800', label: 'Verification Pending' },
      pre_assessment: { color: 'bg-purple-100 text-purple-800', label: 'Pre-Assessment' },
      scheduled: { color: 'bg-yellow-100 text-yellow-800', label: 'Scheduled' },
      assessment_complete: { color: 'bg-green-100 text-green-800', label: 'Assessment Complete' },
      report_writing: { color: 'bg-orange-100 text-orange-800', label: 'Report Writing' },
      qa_review: { color: 'bg-pink-100 text-pink-800', label: 'QA Review' },
      completed: { color: 'bg-green-100 text-green-800', label: 'Completed' },
    };

    const { color, label } = statusConfig[status] || { color: 'bg-gray-100 text-gray-800', label: status };

    return (
      <span className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${color}`}>
        {label}
      </span>
    );
  };

  // Format activity action to be more readable
  const formatActivityAction = (action: string) => {
    // Convert snake_case to Title Case with spaces
    const formatted = action
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    // Replace specific actions with more user-friendly descriptions
    switch(action) {
      case 'approve_referral':
        return 'Referral Approved';
      case 'university_referral_created':
        return 'University Referral Created';
      case 'individual_referral_created':
        return 'Individual Referral Created';
      case 'school_referral_created':
        return 'School Referral Created';
      case 'create_assessment':
        return 'Assessment Created';
      case 'update_assessment':
        return 'Assessment Updated';
      case 'update_status':
        return 'Status Updated';
      case 'payment_received':
        return 'Payment Received';
      case 'assessment_scheduled':
        return 'Assessment Scheduled';
      case 'report_complete':
        return 'Report Completed';
      case 'payment_reminder':
        return 'Payment Reminder Sent';
      case 'form_completed':
        return 'Form Completed';
      case 'document_uploaded':
        return 'Document Uploaded';
      case 'reject_referral':
        return 'Referral Rejected';
      default:
        return formatted;
    }
  };

  // Wait for real data to be loaded and don't use mockAssessment as fallback
  // If data is still loading, display a loading state instead
  const displayData = assessment as any;

  // Show loading state if data is not yet available
  if (isLoading || !displayData || !displayData.assessee) {
    return (
      <AppLayout user={user} logout={logout}>
        <div className="space-y-6">
          <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
            <div className="flex items-center gap-2">
              <Link href="/assessments">
                  <Button variant="outline" size="icon">
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
              </Link>
              <h1 className="text-2xl font-bold">Loading assessment...</h1>
            </div>
          </div>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
          <div className="flex items-center gap-2">
            <Link href="/assessments">
                <Button variant="outline" size="icon">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
            </Link>
            <h1 className="text-2xl font-bold">
              {displayData.assessee?.fullName ? `Assessment: ${displayData.assessee.fullName}` : 'New Assessment'}
            </h1>
          </div>
          <StatusBadge status={displayData.status} />
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">Assessee Details</CardTitle>
              {!isEditing && ['admin', 'assessor'].includes(user?.role || '') && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setIsEditing(true)}
                  className="h-8 w-8 p-0"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              {isEditing ? (
                // Editable fields
                <>
                  <div>
                    <p className="text-sm font-medium">Name</p>
                    <Input 
                      value={editedAssesseeData.fullName}
                      onChange={(e) => setEditedAssesseeData({...editedAssesseeData, fullName: e.target.value})}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Date of Birth</p>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal mt-1",
                            !editedAssesseeData.dateOfBirth && "text-muted-foreground"
                          )}
                        >
                          <CalendarDays className="mr-2 h-4 w-4" />
                          {editedAssesseeData.dateOfBirth ? format(editedAssesseeData.dateOfBirth, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={editedAssesseeData.dateOfBirth || undefined}
                          onSelect={(date: Date | undefined) => setEditedAssesseeData({...editedAssesseeData, dateOfBirth: date || null})}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <Input 
                      value={editedAssesseeData.email}
                      onChange={(e) => setEditedAssesseeData({...editedAssesseeData, email: e.target.value})}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Phone</p>
                    <Input 
                      value={editedAssesseeData.phone}
                      onChange={(e) => setEditedAssesseeData({...editedAssesseeData, phone: e.target.value})}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Address</p>
                    <Input 
                      value={editedAssesseeData.address}
                      onChange={(e) => setEditedAssesseeData({...editedAssesseeData, address: e.target.value})}
                      className="mt-1"
                    />
                  </div>
                </>
              ) : (
                // Read-only fields
                <>
                  <div>
                    <p className="text-sm font-medium">Name</p>
                    <p className="text-sm text-muted-foreground">{displayData.assessee?.fullName || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Date of Birth</p>
                    <p className="text-sm text-muted-foreground">{formatDate(displayData.assessee?.dateOfBirth)}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Contact</p>
                    <p className="text-sm text-muted-foreground">{displayData.assessee?.email || 'N/A'}</p>
                    <p className="text-sm text-muted-foreground">{displayData.assessee?.phone || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Address</p>
                    <p className="text-sm text-muted-foreground">{displayData.assessee?.address || 'N/A'}</p>
                  </div>
                </>
              )}
            </CardContent>
            {isEditing && (
              <CardFooter className="flex justify-end space-x-2">
                <Button variant="outline" onClick={handleCancelEdit}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button onClick={handleSaveEdit}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            )}
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">Assessment Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium">Referral Type</p>
                <p className="text-sm text-muted-foreground capitalize">{displayData.referralType}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Referred By</p>
                <p className="text-sm text-muted-foreground">{displayData.referringUser?.fullName || 'N/A'}</p>
                <p className="text-sm text-muted-foreground">{displayData.referringUser?.organization || ''}</p>
              </div>
              {isEditing ? (
                <>
                  <div>
                    <p className="text-sm font-medium">Assessor</p>
                    <Select
                      value={editedAssessmentData.assessorId?.toString() || "null"}
                      onValueChange={(value) => setEditedAssessmentData({
                        ...editedAssessmentData, 
                        assessorId: value === "null" ? null : parseInt(value)
                      })}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select an assessor" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="null">None</SelectItem>
                        {assessors
                          .filter((assessor: any) => assessor.role === 'assessor')
                          .map((assessor: any) => (
                            <SelectItem key={assessor.id} value={assessor.id.toString()}>
                              {assessor.fullName || assessor.username}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Scheduled Date & Time</p>
                    <div className="grid grid-cols-1 gap-2 mt-1">
                      {/* Date Picker */}
                      <Popover open={scheduleCalendarOpen} onOpenChange={setScheduleCalendarOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !editedAssessmentData.scheduledDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarDays className="mr-2 h-4 w-4" />
                            {editedAssessmentData.scheduledDate 
                              ? format(editedAssessmentData.scheduledDate, "PPP") 
                              : <span>Select date</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 min-w-[280px]" align="start">
                          <Calendar
                            mode="single"
                            selected={editedAssessmentData.scheduledDate || undefined}
                            onSelect={(date: Date | undefined) => {
                              if (date) {
                                // Preserve time if already set, otherwise set to noon
                                const currentDate = editedAssessmentData.scheduledDate;
                                if (currentDate) {
                                  const hours = currentDate.getHours();
                                  const minutes = currentDate.getMinutes();
                                  date.setHours(hours, minutes);
                                } else {
                                  date.setHours(12, 0);
                                }
                              }
                              setEditedAssessmentData({ ...editedAssessmentData, scheduledDate: date || null });
                              if (date) {
                                setScheduleCalendarOpen(false)
                              }
                            }}
                            initialFocus
                            className="rounded-md border"
                          />
                        </PopoverContent>
                      </Popover>
                      
                      {/* Time Picker */}
                      <div className="grid grid-cols-2 gap-2">
                        <Select
                          value={editedAssessmentData.scheduledDate 
                            ? editedAssessmentData.scheduledDate.getHours().toString().padStart(2, '0')
                            : "12"}
                          onValueChange={(value) => {
                            if (editedAssessmentData.scheduledDate) {
                              const newDate = new Date(editedAssessmentData.scheduledDate);
                              newDate.setHours(parseInt(value));
                              setEditedAssessmentData({...editedAssessmentData, scheduledDate: newDate});
                            } else {
                              // Create new date at noon if no date selected
                              const newDate = new Date();
                              newDate.setHours(parseInt(value), 0, 0, 0);
                              setEditedAssessmentData({...editedAssessmentData, scheduledDate: newDate});
                            }
                          }}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Hour" />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({ length: 24 }, (_, i) => (
                              <SelectItem 
                                key={i} 
                                value={i.toString().padStart(2, '0')}
                              >
                                {i.toString().padStart(2, '0')}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        
                        <Select
                          value={editedAssessmentData.scheduledDate 
                            ? editedAssessmentData.scheduledDate.getMinutes().toString().padStart(2, '0')
                            : "00"}
                          onValueChange={(value) => {
                            if (editedAssessmentData.scheduledDate) {
                              const newDate = new Date(editedAssessmentData.scheduledDate);
                              newDate.setMinutes(parseInt(value));
                              setEditedAssessmentData({...editedAssessmentData, scheduledDate: newDate});
                            } else {
                              // Create new date at the selected minute if no date selected
                              const newDate = new Date();
                              newDate.setMinutes(parseInt(value), 0, 0);
                              setEditedAssessmentData({...editedAssessmentData, scheduledDate: newDate});
                            }
                          }}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Minute" />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({ length: 12 }, (_, i) => i * 5).map((minute) => (
                              <SelectItem 
                                key={minute} 
                                value={minute.toString().padStart(2, '0')}
                              >
                                {minute.toString().padStart(2, '0')}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      {/* Add to calendar button */}
                      {editedAssessmentData.scheduledDate && (
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="mt-1"
                          onClick={() => {
                            if (editedAssessmentData.scheduledDate) {
                              const startTime = editedAssessmentData.scheduledDate.toISOString();
                              const endTime = new Date(editedAssessmentData.scheduledDate);
                              endTime.setHours(endTime.getHours() + 2); // Assume 2 hour duration
                              
                              const title = `Assessment: ${displayData.assessee?.fullName || 'Assessment'}`;
                              const details = `Assessment for ${displayData.assessee?.fullName || 'client'}`;
                              
                              // Create Google Calendar link
                              const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(title)}&details=${encodeURIComponent(details)}&dates=${startTime.replace(/[-:]/g, '').replace(/\.\d+/g, '')}/${endTime.toISOString().replace(/[-:]/g, '').replace(/\.\d+/g, '')}`;
                              
                              window.open(googleCalendarUrl, '_blank');
                            }
                          }}
                        >
                          <CalendarDays className="h-4 w-4 mr-2" />
                          Add to Google Calendar
                        </Button>
                      )}
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div>
                    <p className="text-sm font-medium">Assessor</p>
                    <p className="text-sm text-muted-foreground">{displayData.assessor?.fullName || 'Not assigned'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Scheduled Date</p>
                    <p className="text-sm text-muted-foreground">{formatDate(displayData.scheduledDate)}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Payment Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium">Status</p>
                <p className="text-sm text-muted-foreground capitalize">{displayData.paymentStatus?.replace('_', ' ') || 'Not paid'}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Deposit</p>
                <p className="text-sm text-muted-foreground">
                  {formatCurrency(displayData.depositAmount)}
                  {displayData.depositPaidAt ? ` (Paid on ${formatDate(displayData.depositPaidAt)})` : ' (Not paid)'}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium">Final Amount</p>
                <p className="text-sm text-muted-foreground">
                  {formatCurrency(displayData.finalAmount)}
                  {displayData.finalPaidAt ? ` (Paid on ${formatDate(displayData.finalPaidAt)})` : ' (Not paid)'}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium">Total</p>
                <p className="text-sm font-bold">
                  {formatCurrency((displayData.depositAmount || 0) + (displayData.finalAmount || 0))}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Assessment Details</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="forms">
              <TabsList className="mb-4">
                <TabsTrigger value="forms" className="flex items-center gap-1">
                  <FileCheck className="h-4 w-4" />
                  Forms
                </TabsTrigger>
                <TabsTrigger value="documents" className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  Documents
                </TabsTrigger>
                <TabsTrigger value="activity" className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  Activity
                </TabsTrigger>
                <TabsTrigger value="notes" className="flex items-center gap-1">
                  <PencilLine className="h-4 w-4" />
                  Notes
                </TabsTrigger>
              </TabsList>

              <TabsContent value="forms">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Assessment Forms</h3>
                    {['admin', 'assessor'].includes(user?.role || '') && (
                      <Button size="sm">
                        <Plus className="h-4 w-4 mr-1" />
                        Add Form
                      </Button>
                    )}
                  </div>

                  {displayData.forms && displayData.forms.length > 0 ? (
                    <div className="grid gap-4">
                      {displayData.forms.map((form: any) => (
                        <Link key={form.id} href={`/forms/${form.id}`}>
                            <Card className="hover:bg-gray-50 transition-colors">
                              <CardContent className="p-4">
                                <div className="grid grid-cols-3 gap-4">
                                  <div>
                                    <p className="text-sm font-medium capitalize">{form.formType.replace('_', ' ')} Form</p>
                                    <p className="text-xs text-muted-foreground">
                                      {form.formType === 'parent_under_16' ? 'For Parent/Guardian' : 
                                       form.formType === 'assessee_over_16' ? 'For Assessee' :
                                       form.formType === 'school' ? 'For School' :
                                       form.formType === 'assessor' ? 'For Assessor' : 'Other Form'}
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium">Status</p>
                                    <p className="text-xs text-muted-foreground capitalize">{form.status.replace('_', ' ')}</p>
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium">Completed</p>
                                    <p className="text-xs text-muted-foreground">
                                      {form.completedAt ? formatDate(form.completedAt) : 'Not completed'}
                                    </p>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No forms available</p>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="documents">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Documents</h3>
                    <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm">
                          <FileUp className="h-4 w-4 mr-1" />
                          Upload
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>Upload Document</DialogTitle>
                          <DialogDescription>
                            Upload a document for this assessment. Supported formats: PDF, DOC, DOCX, JPG, PNG.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid gap-2">
                            <Label htmlFor="file">Select File</Label>
                            <Input
                              id="file"
                              type="file"
                              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                              onChange={handleFileSelect}
                              className="cursor-pointer"
                            />
                            {selectedFile && (
                              <p className="text-sm text-muted-foreground">
                                Selected: {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)
                              </p>
                            )}
                          </div>
                          <div className="grid gap-2">
                            <Label htmlFor="documentName">Document Name</Label>
                            <Input
                              id="documentName"
                              placeholder="Enter document name"
                              value={documentName}
                              onChange={(e) => setDocumentName(e.target.value)}
                            />
                          </div>
                          <div className="grid gap-2">
                            <Label htmlFor="documentType">Document Type</Label>
                            <Select value={documentType} onValueChange={setDocumentType}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select document type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="report">School Report</SelectItem>
                                <SelectItem value="assessment">Previous Assessment</SelectItem>
                                <SelectItem value="medical">Medical Record</SelectItem>
                                <SelectItem value="educational">Educational Record</SelectItem>
                                <SelectItem value="referral">Referral Letter</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <DialogFooter>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setUploadDialogOpen(false)}
                            disabled={uploadDocumentMutation.isPending}
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={handleDocumentUpload}
                            disabled={uploadDocumentMutation.isPending || !selectedFile || !documentType || !documentName}
                          >
                            {uploadDocumentMutation.isPending ? (
                              <>
                                <Upload className="h-4 w-4 mr-2 animate-spin" />
                                Uploading...
                              </>
                            ) : (
                              <>
                                <Upload className="h-4 w-4 mr-2" />
                                Upload
                              </>
                            )}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>

                  {documents && documents.length > 0 ? (
                    <div className="grid gap-4">
                      {documents.map((doc: any) => (
                        <Card key={doc.id} className="hover:bg-gray-50 transition-colors">
                          <CardContent className="p-4">
                            <div className="grid grid-cols-3 gap-4">
                              <div>
                                <p className="text-sm font-medium">{doc.name}</p>
                                <p className="text-xs text-muted-foreground capitalize">{doc.type}</p>
                              </div>
                              <div>
                                <p className="text-sm font-medium">Uploaded</p>
                                <p className="text-xs text-muted-foreground">{formatDate(doc.createdAt)}</p>
                              </div>
                              <div className="flex justify-end gap-2">
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => window.open(`/api/documents/${doc.id}/download`, '_blank')}
                                >
                                  <Download className="h-4 w-4 mr-1" />
                                  Download
                                </Button>
                                {['admin', 'assessor'].includes(user?.role || '') && (
                                  <Button variant="outline" size="sm">
                                    <Trash2 className="h-4 w-4 mr-1" />
                                    Delete
                                  </Button>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">No documents uploaded yet</p>
                      <p className="text-sm text-muted-foreground mt-1">Upload documents related to this assessment</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="activity">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Activity Timeline</h3>

                  {displayData.activities && displayData.activities.length > 0 ? (
                    <div className="space-y-4">
                      {displayData.activities.map((activity: any) => (
                        <div key={activity.id} className="flex gap-4">
                          <div className="mt-1 flex h-2 w-2 rounded-full bg-primary" />
                          <div>
                            <p className="text-sm font-medium">{formatActivityAction(activity.action)}</p>
                            <p className="text-xs text-muted-foreground">{activity.details}</p>
                            <p className="text-xs text-muted-foreground">{formatDate(activity.createdAt)}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No activity recorded</p>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="notes">
                <AssessmentNotesSection 
                  assessmentId={displayData.id} 
                  user={user} 
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}