import React from 'react';
import AppLayout from '@/components/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, FileTex<PERSON>, <PERSON><PERSON><PERSON>, Plus } from 'lucide-react';
import { <PERSON><PERSON><PERSON> as RechartsBarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { User } from '@shared/schema';
import { Link } from 'wouter';

interface ReportsPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function ReportsPage({ user, logout }: ReportsPageProps = {}) {

  // Empty data for charts - will be populated with real data from API
  const assessmentsByMonth: { name: string; count: number }[] = [];
  const assessmentsByStatus: { name: string; value: number; color: string }[] = [];
  const assessmentsByType: { name: string; value: number; color: string }[] = [];

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Reports & Analytics</h1>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Assessments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                No assessments yet
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Completed Assessments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                0% completion rate
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Average Time to Complete</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">-</div>
              <p className="text-xs text-muted-foreground">
                No completed assessments yet
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Assessments by Month</CardTitle>
              <CardDescription>
                Number of assessments started each month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex flex-col items-center justify-center">
                <BarChart className="h-16 w-16 text-muted-foreground mb-4 opacity-40" />
                <p className="text-muted-foreground text-sm">No assessment data available</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Assessments by Status</CardTitle>
              <CardDescription>
                Current distribution of assessment statuses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex flex-col items-center justify-center">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground mb-4 opacity-40">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                  <path d="M12 8V16M8 12H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <p className="text-muted-foreground text-sm">No status data available</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Assessments by Type</CardTitle>
              <CardDescription>
                Distribution of assessment referral types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex flex-col items-center justify-center">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground mb-4 opacity-40">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                  <path d="M12 12L12 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                  <path d="M12 12L16 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                </svg>
                <p className="text-muted-foreground text-sm">No type data available</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Reports</CardTitle>
              <CardDescription>
                Recently completed assessment reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-10">
                <FileText className="h-12 w-12 text-muted-foreground mb-4 opacity-40" />
                <p className="text-center text-muted-foreground">No completed reports yet</p>
                <p className="text-center text-xs text-muted-foreground mt-1 mb-4">
                  Reports will appear here once assessments are completed
                </p>
                {['admin', 'assessor'].includes(user?.role || '') && (
                  <div className="mt-2">
                    <Link href="/assessments/new">
                      <Button variant="outline" size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Assessment
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}