import React from "react";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Home, Mail } from "lucide-react";

export default function RegistrationPendingPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <div className="w-12 h-12 bg-primary/10 mx-auto mb-4 rounded-full flex items-center justify-center">
            <CheckCircle className="h-6 w-6 text-primary" />
          </div>
          <CardTitle className="text-2xl">Registration Successful</CardTitle>
          <CardDescription>
            Your account is pending admin approval
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-4">
            Thank you for registering with NeuroElevate. Your account has been created and is currently awaiting administrator approval.
          </p>
          <div className="bg-muted p-4 rounded-md mb-4 text-left">
            <h3 className="font-medium mb-2 flex items-center">
              <Mail className="h-4 w-4 mr-2 text-primary" />
              Next Steps
            </h3>
            <ul className="space-y-2 text-sm">
              <li>• You'll receive an email notification when your account is approved</li>
              <li>• Once approved, you can log in with your credentials</li>
              <li>• This process typically takes 1-2 business days</li>
            </ul>
          </div>
          <p className="text-sm text-muted-foreground">
            If you have any questions or need assistance, please contact support at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>
          </p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Link href="/auth">
            <Button variant="outline" className="w-full">
              <Home className="h-4 w-4 mr-2" />
              Return to Login
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}