import React from 'react';
import AppLayout from '@/components/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CreditCard, Download, Wallet, Plus } from 'lucide-react';
import { User } from '@shared/schema';
import { Link } from 'wouter';

interface PaymentsPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function PaymentsPage({ user, logout }: PaymentsPageProps = {}) {

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Payments</h1>
        
        <Card>
          <CardHeader>
            <CardTitle>Payment History</CardTitle>
            <CardDescription>
              View your payment history and manage invoices for assessment services.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center p-12">
              <Wallet className="h-16 w-16 text-muted-foreground mb-4 opacity-40" />
              <h3 className="text-lg font-medium mb-1">No Payment History</h3>
              <p className="text-muted-foreground text-center mb-6 max-w-sm">
                You don't have any payments recorded yet. Payment information will appear here once you have active assessments.
              </p>
              {['admin', 'assessor'].includes(user?.role || '') && (
                <Link href="/assessments/new">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Assessment
                  </Button>
                </Link>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}