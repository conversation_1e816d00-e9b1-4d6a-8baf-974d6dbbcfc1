import { useState, useEffect } from "react";
import { useMessaging } from "@/hooks/use-messaging";
import { useNotifications } from "@/hooks/use-notifications";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { UserSelect } from "@/components/user-select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Loader2, Send, Plus, X, CheckCircle, UserIcon, Bell, MessageSquare, Settings, ArrowLeft } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { User } from "@shared/schema";
import AppLayout from "@/components/layouts/app-layout";

// Form schema for creating a new conversation
const createConversationSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  participantIds: z.array(z.number()).min(1, { message: "Select at least one participant" }),
  assessmentId: z.number().optional(),
  createdById: z.number(),
});

type CreateConversationFormValues = z.infer<typeof createConversationSchema>;

// Form schema for sending a message
const sendMessageSchema = z.object({
  content: z.string().min(1, { message: "Message cannot be empty" }),
});

type SendMessageFormValues = z.infer<typeof sendMessageSchema>;

const NotificationBadge = () => {
  const { unreadCount, isLoadingUnreadCount } = useNotifications();
  
  if (isLoadingUnreadCount) {
    return <div className="h-2 w-2 rounded-full bg-muted animate-pulse"></div>;
  }
  
  if (!unreadCount) return null;
  
  return (
    <Badge variant="destructive" className="absolute -top-1 -right-1 px-1.5 py-0.5 text-[10px]">
      {unreadCount}
    </Badge>
  );
};

export default function CommunicationCenterPage({ user, logout }: { user: User | null, logout: () => Promise<void> }) {
  const { toast } = useToast();
  const { 
    conversations, 
    isLoadingConversations, 
    getConversation, 
    createConversationMutation, 
    sendMessageMutation,
    setupMessagePolling 
  } = useMessaging();
  
  const {
    notifications,
    isLoadingNotifications,
    markAsReadMutation,
    markAllAsReadMutation,
    setupNotificationPolling
  } = useNotifications();
  
  const [activeTab, setActiveTab] = useState("messages");
  const [selectedConversation, setSelectedConversation] = useState<number | null>(null);
  const [isNewConversationDialogOpen, setIsNewConversationDialogOpen] = useState(false);
  const [messageSearchQuery, setMessageSearchQuery] = useState("");
  
  // Get data for the selected conversation
  const {
    data: conversationData,
    isLoading: isLoadingConversation,
  } = getConversation(selectedConversation || 0);
  
  // Filter messages by search query if applicable
  const filteredMessages = conversationData?.messages?.filter((message: any) => {
    if (!messageSearchQuery) return true;
    return message.content.toLowerCase().includes(messageSearchQuery.toLowerCase());
  });
  
  // Set up form for creating a new conversation
  const createConversationForm = useForm<CreateConversationFormValues>({
    resolver: zodResolver(createConversationSchema),
    defaultValues: {
      title: "",
      participantIds: [],
      createdById: user?.id || 0, // Will be updated when user is loaded
    },
  });
  
  // Update form values when user is loaded
  useEffect(() => {
    if (user?.id) {
      createConversationForm.setValue('createdById', user.id);
    }
  }, [user, createConversationForm]);
  
  // Set up form for sending a message
  const sendMessageForm = useForm<SendMessageFormValues>({
    resolver: zodResolver(sendMessageSchema),
    defaultValues: {
      content: "",
    },
  });
  
  // Handle creating a new conversation
  const handleCreateConversation = (values: CreateConversationFormValues) => {
    // Make sure we have a valid user ID
    if (!user?.id) {
      toast({
        title: "Authentication error",
        description: "You must be logged in to create a conversation",
        variant: "destructive"
      });
      return;
    }
    
    // Ensure createdById matches the current user
    values.createdById = user.id;
    
    createConversationMutation.mutate(values, {
      onSuccess: (data) => {
        toast({
          title: "Conversation created",
          description: "Your new conversation has been created",
        });
        setIsNewConversationDialogOpen(false);
        createConversationForm.reset();
        setSelectedConversation(data.id);
        setActiveTab("messages");
      },
      onError: (error) => {
        toast({
          title: "Failed to create conversation",
          description: error.message || "Please try again later",
          variant: "destructive"
        });
      }
    });
  };
  
  // Handle sending a message
  const handleSendMessage = (values: SendMessageFormValues) => {
    if (!selectedConversation) {
      toast({
        title: "No conversation selected",
        description: "Please select a conversation to send a message",
        variant: "destructive"
      });
      return;
    }
    
    if (!user?.id) {
      toast({
        title: "Authentication error",
        description: "You must be logged in to send a message",
        variant: "destructive"
      });
      return;
    }
    
    sendMessageMutation.mutate({
      conversationId: selectedConversation,
      content: values.content
    }, {
      onSuccess: () => {
        toast({
          title: "Message sent",
          description: "Your message has been sent successfully"
        });
        sendMessageForm.reset();
      },
      onError: (error) => {
        toast({
          title: "Failed to send message",
          description: error.message || "Please try again later",
          variant: "destructive"
        });
      }
    });
  };
  
  // Set up polling for new messages and notifications
  useEffect(() => {
    const clearMessagePolling = selectedConversation ? setupMessagePolling(selectedConversation) : undefined;
    const clearNotificationPolling = setupNotificationPolling();
    
    return () => {
      if (clearMessagePolling) clearMessagePolling();
      clearNotificationPolling();
    };
  }, [selectedConversation, setupMessagePolling, setupNotificationPolling]);
  
  // If a new conversation is selected, mark all messages in that conversation as read
  useEffect(() => {
    if (selectedConversation && conversationData) {
      // TODO: Implement marking all messages as read in a conversation
    }
  }, [selectedConversation, conversationData]);
  
  // Redirect if user is not authenticated
  if (!user) {
    return (
      <div className="container mx-auto py-10 text-center">
        <Loader2 className="h-10 w-10 animate-spin text-primary mx-auto mb-4" />
        <p>Loading user information...</p>
      </div>
    );
  }

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Communication Center</h1>
          <Button 
            onClick={() => setIsNewConversationDialogOpen(true)} 
            className="flex items-center gap-2"
            disabled={createConversationMutation.isPending}
          >
            {createConversationMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Creating...
              </>
            ) : (
              <>
                <Plus size={16} /> New Conversation
              </>
            )}
          </Button>
        </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="messages" className="relative">
            <MessageSquare className="mr-2 h-4 w-4" />
            Messages
            {conversations && conversations.length > 0 && (
              <Badge variant="outline" className="ml-2">{conversations.length}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="notifications" className="relative">
            <Bell className="mr-2 h-4 w-4" />
            Notifications
            <NotificationBadge />
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="mr-2 h-4 w-4" />
            Notification Settings
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="messages" className="mt-0">
          <div className="grid grid-cols-12 gap-6 h-[70vh]">
            {/* Conversations List */}
            <Card className="col-span-4 h-full">
              <CardHeader className="pb-2">
                <CardTitle>Conversations</CardTitle>
                <CardDescription>
                  {isLoadingConversations ? "Loading conversations..." : `${conversations?.length || 0} conversations`}
                </CardDescription>
              </CardHeader>
              <Separator />
              <CardContent className="p-0">
                <ScrollArea className="h-[calc(70vh-8rem)]">
                  {isLoadingConversations ? (
                    <div className="flex items-center justify-center h-full">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : conversations && conversations.length > 0 ? (
                    <div className="divide-y">
                      {conversations.map((conversation: any) => (
                        <div 
                          key={conversation.id}
                          className={`p-4 cursor-pointer hover:bg-secondary/50 transition-colors ${selectedConversation === conversation.id ? 'bg-secondary' : ''}`}
                          onClick={() => setSelectedConversation(conversation.id)}
                        >
                          <div className="flex items-center justify-between">
                            <h3 className="font-medium">{conversation.title}</h3>
                            {conversation.unreadCount > 0 && (
                              <Badge>{conversation.unreadCount}</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-1 truncate">
                            {conversation.latestMessage ? conversation.latestMessage.content : "No messages yet"}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <div className="flex space-x-1">
                              {conversation.participants?.slice(0, 3).map((participant: any) => (
                                <div key={participant.id} className="h-6 w-6 rounded-full bg-primary flex items-center justify-center text-[10px] text-primary-foreground ring-1 ring-background">
                                  {participant.email?.charAt(0).toUpperCase()}
                                </div>
                              ))}
                              {conversation.participants?.length > 3 && (
                                <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-[10px] ring-1 ring-background">
                                  +{conversation.participants.length - 3}
                                </div>
                              )}
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {conversation.updatedAt && formatDistanceToNow(new Date(conversation.updatedAt), { addSuffix: true })}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-6 text-center">
                      <p className="text-muted-foreground mb-4">No conversations yet</p>
                      <Button variant="outline" onClick={() => setIsNewConversationDialogOpen(true)} className="flex items-center gap-2">
                        <Plus size={16} /> Start a conversation
                      </Button>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
            
            {/* Conversation Detail */}
            <Card className="col-span-8 h-full">
              {selectedConversation && conversationData ? (
                <>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-8 w-8 md:hidden" 
                          onClick={() => setSelectedConversation(null)}
                        >
                          <ArrowLeft className="h-4 w-4" />
                          <span className="sr-only">Back to conversations</span>
                        </Button>
                        <div>
                          <CardTitle>{conversationData.conversation.title}</CardTitle>
                          <CardDescription>
                            {conversationData.conversation.participants?.length} participants
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="relative">
                          <Input
                            placeholder="Search messages..."
                            value={messageSearchQuery}
                            onChange={(e) => setMessageSearchQuery(e.target.value)}
                            className="h-9 pr-8 w-[200px]"
                          />
                          {messageSearchQuery && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-full w-8"
                              onClick={() => setMessageSearchQuery("")}
                            >
                              <X className="h-4 w-4" />
                              <span className="sr-only">Clear search</span>
                            </Button>
                          )}
                        </div>
                        <Button variant="outline" size="sm">
                          Add Participant
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <Separator />
                  <CardContent className="p-4 h-[calc(70vh-16rem)]">
                    <ScrollArea className="h-full pr-4">
                      {/* Show search results count if search is active */}
                      {messageSearchQuery && filteredMessages && (
                        <div className="mb-4 p-2 bg-muted/50 rounded-md text-sm">
                          {filteredMessages.length === 0 ? (
                            <p className="text-center text-muted-foreground">No messages match your search</p>
                          ) : (
                            <p className="text-center">Found {filteredMessages.length} {filteredMessages.length === 1 ? 'message' : 'messages'} matching "{messageSearchQuery}"</p>
                          )}
                        </div>
                      )}
                      
                      {conversationData.messages && conversationData.messages.length > 0 ? (
                        <div className="space-y-4">
                          {(messageSearchQuery ? filteredMessages : conversationData.messages).map((message: any) => {
                            const isCurrentUser = message.senderId === user?.id;
                            return (
                              <div key={message.id} className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                                <div className={`max-w-[80%] ${isCurrentUser ? 'bg-primary text-primary-foreground' : 'bg-secondary'} p-3 rounded-lg`}>
                                  {!isCurrentUser && (
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className="h-6 w-6 rounded-full bg-primary flex items-center justify-center text-[10px] text-primary-foreground">
                                        {message.sender?.email?.charAt(0).toUpperCase() || 'U'}
                                      </div>
                                      <span className="text-xs font-medium">{message.sender?.email || "Unknown user"}</span>
                                    </div>
                                  )}
                                  <p className="whitespace-pre-wrap break-words">{message.content}</p>
                                  <div className="flex items-center justify-end gap-1 mt-1">
                                    <span className="text-xs opacity-70">
                                      {message.createdAt && formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                                    </span>
                                    {message.editedAt && (
                                      <span className="text-xs opacity-70">(edited)</span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <div className="text-center">
                            <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <p className="text-muted-foreground">No messages yet</p>
                            <p className="text-sm text-muted-foreground mt-1">Start the conversation by sending a message below</p>
                          </div>
                        </div>
                      )}
                    </ScrollArea>
                  </CardContent>
                  <Separator />
                  <CardFooter className="p-4">
                    <Form {...sendMessageForm}>
                      <form onSubmit={sendMessageForm.handleSubmit(handleSendMessage)} className="w-full">
                        <div className="flex gap-2">
                          <FormField
                            control={sendMessageForm.control}
                            name="content"
                            render={({ field }) => (
                              <FormItem className="flex-grow">
                                <FormControl>
                                  <Input 
                                    placeholder="Type your message..." 
                                    {...field} 
                                    className="h-12"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <Button type="submit" size="icon" className="h-12 w-12" disabled={sendMessageMutation.isPending}>
                            {sendMessageMutation.isPending ? <Loader2 className="h-5 w-5 animate-spin" /> : <Send className="h-5 w-5" />}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardFooter>
                </>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center max-w-md mx-auto p-6">
                    <MessageSquare className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-medium mb-2">Select a conversation</h3>
                    <p className="text-muted-foreground mb-6">
                      Choose an existing conversation from the list or start a new one to begin messaging.
                    </p>
                    <Button onClick={() => setIsNewConversationDialogOpen(true)} className="flex items-center gap-2">
                      <Plus size={16} /> New Conversation
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="notifications" className="mt-0">
          <Card className="h-[70vh]">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Notifications</CardTitle>
                  <CardDescription>
                    {isLoadingNotifications ? "Loading notifications..." : `${notifications?.length || 0} unread notifications`}
                  </CardDescription>
                </div>
                {notifications && notifications.length > 0 && (
                  <Button variant="outline" size="sm" onClick={() => markAllAsReadMutation.mutate()}>
                    Mark all as read
                  </Button>
                )}
              </div>
            </CardHeader>
            <Separator />
            <CardContent className="p-0">
              <ScrollArea className="h-[calc(70vh-5rem)]">
                {isLoadingNotifications ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : notifications && notifications.length > 0 ? (
                  <div className="divide-y">
                    {notifications.map((notification: any) => (
                      <div key={notification.id} className="p-4 hover:bg-secondary/50 transition-colors">
                        <div className="flex items-start gap-4">
                          <div className="rounded-full p-2 bg-primary/10">
                            {notification.type === 'message' ? (
                              <MessageSquare className="h-5 w-5 text-primary" />
                            ) : notification.type === 'assessment' ? (
                              <CheckCircle className="h-5 w-5 text-primary" />
                            ) : (
                              <Bell className="h-5 w-5 text-primary" />
                            )}
                          </div>
                          <div className="flex-grow">
                            <h3 className="font-medium">{notification.title}</h3>
                            <p className="text-sm text-muted-foreground mt-1">{notification.content}</p>
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-xs text-muted-foreground">
                                {notification.createdAt && formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                              </span>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-7 px-2"
                                onClick={() => markAsReadMutation.mutate(notification.id)}
                                disabled={markAsReadMutation.isPending}
                              >
                                Mark as read
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No unread notifications</p>
                      <p className="text-sm text-muted-foreground mt-1">You're all caught up!</p>
                    </div>
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="settings" className="mt-0">
          <Card className="h-[70vh]">
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>Configure how and when you'd like to be notified</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Email Notifications</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Email notification settings would go here */}
                    <p className="text-muted-foreground col-span-1 md:col-span-2">
                      Email notification preferences will be implemented in a future update.
                    </p>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-medium mb-2">In-App Notifications</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* In-app notification settings would go here */}
                    <p className="text-muted-foreground col-span-1 md:col-span-2">
                      In-app notification preferences will be implemented in a future update.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* New Conversation Dialog */}
      <Dialog open={isNewConversationDialogOpen} onOpenChange={setIsNewConversationDialogOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Create New Conversation</DialogTitle>
            <DialogDescription>
              Start a conversation with one or more participants.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...createConversationForm}>
            <form onSubmit={createConversationForm.handleSubmit(handleCreateConversation)} className="space-y-6">
              <FormField
                control={createConversationForm.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conversation Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter a title for the conversation" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={createConversationForm.control}
                name="participantIds"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Participants</FormLabel>
                    <FormControl>
                      <UserSelect
                        selectedUsers={field.value}
                        onSelect={(userId) => {
                          const currentIds = [...field.value];
                          field.onChange([...currentIds, userId]);
                        }}
                        onRemove={(userId) => {
                          const currentIds = [...field.value];
                          field.onChange(currentIds.filter(id => id !== userId));
                        }}
                        disabled={createConversationMutation.isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsNewConversationDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={createConversationMutation.isPending || !createConversationForm.formState.isValid}
                >
                  {createConversationMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Conversation"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      </div>
    </AppLayout>
  );
}