import { AllReferralsTable } from "@/components/admin/all-referrals-table";
import AppLayout from "@/components/layouts/app-layout";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { Link } from "wouter";
import { User } from "@shared/schema";

interface AllReferralsPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function AllReferralsPage({ user, logout }: AllReferralsPageProps = {}) {
  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Link href="/admin">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">All Referrals</h1>
        </div>
        <AllReferralsTable />
      </div>
    </AppLayout>
  );
}