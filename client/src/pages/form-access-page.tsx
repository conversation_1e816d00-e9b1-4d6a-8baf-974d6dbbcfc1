import { useState, useEffect, useMemo } from "react";
import { useLocation, useParams } from "wouter";
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertTriangle, CheckCircle2, FileX, Loader2 } from "lucide-react";
import { ParentUnder16Form } from "@/components/forms/parent-under-16-form";
import { AssesseeOver16Form } from "@/components/forms/assessee-over-16-form";
import { SchoolForm } from "@/components/forms/school-form";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface ApiFormResponse {
  form: {
    id: number;
    assessmentId: number;
    formType: string;
    status: string;
    data?: string | null;
    accessToken: string;
    accessTokenExpiresAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
  };
  assessment: {
    id: number;
    assesseeId: number;
    status: string;
  };
  assessee: {
    id: number;
    fullName: string;
    dateOfBirth?: string;
    age?: number;
    [key: string]: any; // Include other properties
  };
  questions: any[];
  responses: any[];
}

/**
 * Helper function to get a human-readable form title based on form type
 */
function getFormTitle(formType: string): string {
  switch (formType) {
    case 'parent_under_16':
      return 'Parent/Guardian Questionnaire (Under 16 Years)';
    case 'assessee_over_16':
      return 'Assessee Questionnaire (Over 16 Years)';
    case 'school':
      return 'School Pre-Assessment Form';
    default:
      return formType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) + ' Form';
  }
}

// Map UI form field names to database question keys
function getFieldNameMapping(formType: string): Record<string, string> {
  if (formType === 'school') {
    return {
      childFullName: 'pupil_name',
      school: 'school_name',
      yearGroup: 'pupil_year_group',
      personCompletingName: 'school_email',
      personCompletingRole: 'pupil_class'
    };
  } else if (formType === 'parent_under_16') {
    return {
      childName: 'child_name',
      childDateOfBirth: 'child_dob',
      childSchool: 'child_school',
      childYearGroup: 'child_year_group',
      parentName: 'parent_name',
      relationship: 'parent_relationship',
      address: 'parent_address',
      contactPhone: 'parent_phone',
      contactEmail: 'parent_email'
    };
  } else if (formType === 'assessee_over_16') {
    return {
      fullName: 'full_name',
      preferredName: 'preferred_name',
      dateOfBirth: 'date_of_birth',
      address: 'address',
      contactPhone: 'phone',
      contactPhoneWork: 'phone_work',
      contactEmail: 'email',
      currentInstitution: 'current_institution',
      courseOfStudy: 'course_of_study',
      highestQualification: 'highest_qualification',
      gender: 'gender',
      preferredPronoun: 'preferred_pronoun',
      hearAbout: 'hear_about',
      hearAboutOther: 'hear_about_other'
    };
  }
  return {};
}

// Convert normalized responses to a flat object for the form
function formatResponsesForForm(responses: any[] | undefined, questions: any[] | undefined, formType: string) {
  if (!responses || !questions) return {};

  const formData: Record<string, any> = {};
  const fieldMapping = getFieldNameMapping(formType);
  const reverseMapping: Record<string, string> = {};
  Object.entries(fieldMapping).forEach(([ui, db]) => {
    reverseMapping[db] = ui;
  });

  responses.forEach((resp) => {
    const question = questions.find((q) => q.id === resp.questionId);
    if (!question) return;
    const dbKey = question.questionKey;
    let value = null;
    if (resp.responseText !== undefined && resp.responseText !== null) value = resp.responseText;
    else if (resp.responseNumber !== undefined && resp.responseNumber !== null) value = resp.responseNumber;
    else if (resp.responseBoolean !== undefined && resp.responseBoolean !== null) value = resp.responseBoolean;
    else if (resp.responseOptions !== undefined && resp.responseOptions !== null) value = resp.responseOptions;

    formData[dbKey] = value;
    const uiField = reverseMapping[dbKey];
    if (uiField) formData[uiField] = value;
  });

  return formData;
}

// Convert flat form values to an array of response objects using the defined
// form questions. This mirrors the logic used on the authenticated form page.
function convertFormDataToResponses(
  values: Record<string, any>,
  questions: any[] | undefined,
  formType: string,
  formId: number
) {
  if (!questions || questions.length === 0) return [];

  const fieldMapping = getFieldNameMapping(formType);

  return questions
    .map((question) => {
      let value = values[question.questionKey];

      if (value === undefined) {
        const uiField = Object.entries(fieldMapping).find(([_ui, db]) => db === question.questionKey)?.[0];
        if (uiField) value = values[uiField];
      }

      const response: any = { formId, questionId: question.id };

      if (question.questionType === 'text' || question.questionType === 'textarea' || question.questionType === 'date') {
        response.responseText = value;
      } else if (question.questionType === 'number') {
        response.responseNumber = value ? parseFloat(value) : null;
      } else if (question.questionType === 'boolean' || question.questionType === 'checkbox') {
        response.responseBoolean = value === 'true' || value === true;
      } else if (question.questionType === 'select' || question.questionType === 'radio') {
        response.responseOptions = value;
        if (question.questionKey === 'gender' || question.questionKey === 'preferred_pronoun') {
          response.responseText = value;
        }
      }

      return response;
    })
    .filter((resp) =>
      resp.responseText !== undefined ||
      resp.responseNumber !== undefined ||
      resp.responseBoolean !== undefined ||
      resp.responseOptions !== undefined
    );
}

interface FormData {
  id: number;
  assessmentId: number;
  formType: string;
  status: string;
  title: string;
  assesseeName: string;
  assesseeId: number;
  data?: string | null;
  responses?: any[];
  questions?: any[];
  assessee?: {
    id: number;
    fullName: string;
    dateOfBirth?: string;
    age?: number;
    [key: string]: any; // Allow other assessee fields
  };
}

/**
 * Form Access Page
 * 
 * This page is used to access forms via token links without requiring login.
 * It validates the token with the server and displays the appropriate form.
 */
export default function FormAccessPage() {
  const params = useParams<{ token: string }>();
  const token = params.token;
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  
  // Form loading state
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expired, setExpired] = useState(false);
  const [formData, setFormData] = useState<FormData | null>(null);

  const formInitialData = useMemo(() => {
    if (!formData) return {};
    if (formData.data) {
      try {
        return JSON.parse(formData.data);
      } catch (e) {
        console.error('Error parsing form data:', e);
      }
    }
    if (formData.responses && formData.questions) {
      return formatResponsesForForm(formData.responses, formData.questions, formData.formType);
    }
    return {};
  }, [formData]);
  
  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  
  // Load the form data using the token
  useEffect(() => {
    const getFormByToken = async () => {
      if (!token) {
        setError("No access token provided");
        setIsLoading(false);
        return;
      }
      
      try {
        const res = await fetch(`/api/forms/access/${token}`);
        
        if (res.status === 401) {
          const data = await res.json();
          if (data.expired) {
            setExpired(true);
          } else {
            setError("The form access link is invalid");
          }
          setIsLoading(false);
          return;
        }
        
        if (!res.ok) {
          throw new Error("Failed to load the form");
        }
        
        const apiResponse = await res.json() as ApiFormResponse;
        console.log("API response loaded:", apiResponse);
        
        // Convert API response to FormData format with all assessee information
        const formData: FormData = {
          id: apiResponse.form.id,
          assessmentId: apiResponse.form.assessmentId,
          formType: apiResponse.form.formType,
          status: apiResponse.form.status,
          title: getFormTitle(apiResponse.form.formType),
          assesseeName: apiResponse.assessee.fullName,
          assesseeId: apiResponse.assessee.id,
          data: apiResponse.form.data,
          responses: apiResponse.responses || [],
          questions: apiResponse.questions || [],
          // Add the complete assessee information for the form to use
          assessee: apiResponse.assessee  // This adds all assessee fields including fullName, dateOfBirth, etc.
        };
        
        console.log("Processed form data:", formData);
        setFormData(formData);
      } catch (err) {
        console.error("Error loading form:", err);
        setError("An error occurred while loading the form");
      } finally {
        setIsLoading(false);
      }
    };
    
    getFormByToken();
  }, [token]);
  
  // Handle form submission
  const handleFormSubmit = async (responseData: any) => {
    if (!formData || !token) return;

    try {
      setIsSubmitting(true);
      console.log("Form submission data:", responseData);

      let responses: any[] = [];
      if (Array.isArray(responseData)) {
        responses = responseData;
      } else if (responseData.responses && Array.isArray(responseData.responses)) {
        responses = responseData.responses;
      } else if (typeof responseData === 'object') {
        if (formData.questions && formData.questions.length > 0) {
          responses = convertFormDataToResponses(
            responseData,
            formData.questions,
            formData.formType,
            formData.id
          );
        } else {
          responses = Object.entries(responseData).map(([key, value]) => ({
            formId: formData.id,
            questionKey: key,
            value: value as any
          }));
        }
      }

      console.log("Submitting form with responses:", responses);

      const formattedResponses = responses;
      
      // Create a proper data object to store ALL form data
      const formDataObject: Record<string, any> = {};
      
      // First add any existing data from the form (if available)
      let existingData = {};
      try {
        if (formData && formData.data) {
          existingData = JSON.parse(formData.data);
          console.log("Existing form data found:", existingData);
          
          // Merge existing data with new form data
          Object.assign(formDataObject, existingData);
        }
      } catch (e) {
        console.error("Error parsing existing form data:", e);
      }
      
      // Merge the submitted values with any existing data
      Object.assign(formDataObject, responseData);
      
      // Add submission timestamp
      formDataObject["submittedAt"] = new Date().toISOString();
      
      // Add debug logging
      console.log("Complete form data object created with", Object.keys(formDataObject).length, "fields");
      
      // Make sure form data is properly stringified and saved
      const formDataString = JSON.stringify(formDataObject);
      console.log(`SENDING FORM DATA (${formDataString.length} chars):`, formDataString);
      
      const submitData = {
        responses: formattedResponses,
        status: "completed",
        data: formDataString
      };
      
      console.log("Final submission data:", submitData);
      
      const res = await apiRequest("POST", `/api/forms/submit/${token}`, submitData);
      
      // Get response text for debugging
      const responseText = await res.text();
      console.log("Form submission response:", res.status, responseText);
      
      if (!res.ok) {
        throw new Error(`Failed to submit form: ${responseText}`);
      }
      
      setSubmitted(true);
      
      toast({
        title: "Form Submitted",
        description: "Your form has been submitted successfully",
      });
    } catch (err) {
      console.error("Error submitting form:", err);
      toast({
        title: "Submission Failed",
        description: "Could not submit the form. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="container max-w-4xl mx-auto py-8">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-2/3" />
            <Skeleton className="h-4 w-full mt-2" />
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <div className="container max-w-4xl mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-destructive flex items-center">
              <FileX className="mr-2 h-6 w-6" />
              Form Access Error
            </CardTitle>
            <CardDescription>
              There was a problem accessing the form.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={() => window.location.href = "/"}>
              Go to homepage
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  // Render expired token state
  if (expired) {
    return (
      <div className="container max-w-4xl mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-amber-500 flex items-center">
              <AlertTriangle className="mr-2 h-6 w-6" />
              Form Access Link Expired
            </CardTitle>
            <CardDescription>
              The link you are using has expired. Form access links are valid for 30 days or until the form has been submitted.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }
  
  // Render submitted state
  if (submitted) {
    return (
      <div className="container max-w-4xl mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-green-500 flex items-center">
              <CheckCircle2 className="mr-2 h-6 w-6" />
              Form Submitted Successfully
            </CardTitle>
            <CardDescription>
              Thank you for completing the form. You will be notified about the next steps in the assessment process.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }
  
  // Render the appropriate form based on formType
  // If formData is still null or undefined, show a loading indicator
  if (!formData) {
    return (
      <div className="container max-w-4xl mx-auto py-8 flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="container max-w-4xl mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold">
            {formData.title || (formData.formType ? `${formData.formType.replace(/_/g, ' ')} Form` : 'Assessment Form')}
          </CardTitle>
          <CardDescription>
            This form is for the assessment of {formData.assesseeName || 'the assessee'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {formData && formData.formType === 'parent_under_16' && (
            <ParentUnder16Form
              formId={formData.id}
              initialData={formInitialData}
              formData={formData}
              onSubmit={handleFormSubmit}
              onSaveDraft={(data) => {
                console.log("Saving draft:", data);
                // No-op in token-based access mode
              }}
            />
          )}
          
          {formData && formData.formType === 'assessee_over_16' && (
            <AssesseeOver16Form
              formId={formData.id}
              initialData={formInitialData}
              formData={formData}
              onSubmit={handleFormSubmit}
              onSaveDraft={(data) => {
                console.log("Saving draft:", data);
                // No-op in token-based access mode
              }}
            />
          )}
          
          {formData && formData.formType === 'school' && (
            <SchoolForm
              formId={formData.id}
              initialData={formInitialData}
              formData={formData}
              onSubmit={handleFormSubmit}
              onSaveDraft={(data) => {
                console.log("Saving draft:", data);
                // No-op in token-based access mode
              }}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}