import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { User, type Assessee, insertAssessmentSchema, assessmentStatusEnum, paymentStatusEnum, referralTypeEnum } from "@shared/schema";
import { useLocation } from "wouter";
import AppLayout from "../components/layouts/app-layout";
import { ArrowLeft, Calendar, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "wouter";
import { format } from "date-fns";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

// Define a client-side validation schema
const addAssessmentSchema = z.object({
  assesseeId: z.number({
    required_error: "Assessee is required",
  }),
  assessorId: z.number().optional(),
  referringUserId: z.number().optional(),
  referralType: z.enum(['individual', 'school', 'university'], {
    required_error: "Referral type is required",
  }),
  status: z.enum(['VerificationPending', 'pre_assessment', 'scheduled', 'assessment_complete', 'report_writing', 'qa_review', 'completed']).default('VerificationPending'),
  paymentStatus: z.enum(['unpaid', 'deposit_paid', 'fully_paid']).default('unpaid'),
  depositAmount: z.coerce.number().min(0, "Deposit amount cannot be negative").optional(),
  finalAmount: z.coerce.number().min(0, "Final amount cannot be negative").optional(),
  scheduledDate: z.date().nullable().optional(),
  notes: z.string().optional(),
});

type AddAssessmentFormData = z.infer<typeof addAssessmentSchema>;

interface AddAssessmentPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function AddAssessmentPage({ user, logout }: AddAssessmentPageProps = {}) {
  const { toast } = useToast();
  const [_, navigate] = useLocation();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!user) {
    return (
      <AppLayout user={user} logout={logout}>
        <div className="flex flex-col items-center justify-center min-h-[500px] space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </AppLayout>
    );
  }

  // Get all assessees to select from
  const assesseesQueryEnabled = Boolean(user && user.id && user.role);

  const { data: assessees = [], isLoading: assesseesLoading, error: assesseesError, isError: assesseesIsError } = useQuery<Assessee[]>({
    queryKey: ["/api/assessees"],
    enabled: assesseesQueryEnabled,
    retry: 3,
    retryDelay: 1000,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    queryFn: async () => {
      if (!user) {
        throw new Error('User not available');
      }

      const response = await apiRequest('GET', '/api/assessees');

      if (response.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      }

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch assessees: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    },
  });



  // Get all assessors if user is admin
  const { data: assessors = [], isLoading: assessorsLoading } = useQuery<User[]>({
    queryKey: ["/api/users?role=assessor"],
    enabled: !!user && user.role === "admin",
  });

  const form = useForm<AddAssessmentFormData>({
    resolver: zodResolver(addAssessmentSchema),
    defaultValues: {
      assesseeId: undefined,
      assessorId: user?.role === "assessor" ? user.id : undefined,
      referringUserId: user?.role !== "assessor" && user?.role !== "admin" ? user.id : undefined,
      referralType: "individual",
      status: "VerificationPending", // Using a value from assessmentStatusEnum
      paymentStatus: "unpaid",
      depositAmount: 100,
      finalAmount: 450,
      scheduledDate: null,
      notes: "",
    },
  });

  const mutation = useMutation({
    mutationFn: async (data: AddAssessmentFormData) => {
      const response = await apiRequest("POST", "/api/assessments", data);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to create assessment");
      }

      return await response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Assessment created successfully",
        description: "The assessment has been created and associated forms have been generated."
      });
      queryClient.invalidateQueries({ queryKey: ["/api/assessments"] });
      navigate(`/assessments/${data.id}`);
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create assessment",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const onSubmit = async (data: AddAssessmentFormData) => {
    try {
      setIsSubmitting(true);
      // Format date fields to ISO strings for the server
      const formattedData = {
        ...data,
        scheduledDate: data.scheduledDate ? data.scheduledDate.toISOString() : null
      };
      await mutation.mutateAsync(formattedData as any);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show error state if there's an error fetching assessees
  if (assesseesIsError) {
    return (
      <AppLayout user={user} logout={logout}>
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Link href="/">
              <Button variant="outline" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">Create New Assessment</h1>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Error Loading Assessees</CardTitle>
              <CardDescription>
                There was an error loading the assessees list
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Error: {assesseesError?.message || 'Unknown error occurred'}
                </p>
                <div className="flex gap-2">
                  <Button
                    onClick={() => window.location.reload()}
                    variant="outline"
                  >
                    Refresh Page
                  </Button>
                  <Link href="/assessees/new">
                    <Button>
                      Add Assessee First
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  if (assesseesLoading) {
    return (
      <AppLayout user={user} logout={logout}>
        <div className="flex flex-col items-center justify-center min-h-[500px] space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading assessees...</p>
        </div>
      </AppLayout>
    );
  }

  if (assessees.length === 0) {
    return (
      <AppLayout user={user} logout={logout}>
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Link href="/">
              <Button variant="outline" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">Create New Assessment</h1>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>No Assessees Available</CardTitle>
              <CardDescription>
                You need to add at least one assessee before creating an assessment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/assessees/new">
                <Button>
                  Add Assessee
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Link href="/">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Create New Assessment</h1>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Assessment Details</CardTitle>
            <CardDescription>
              Create a new assessment for an assessee
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="assesseeId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Assessee</FormLabel>
                      <Select 
                        onValueChange={(value) => field.onChange(parseInt(value))}
                        defaultValue={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an assessee" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {assessees.map((assessee) => (
                            <SelectItem key={assessee.id} value={assessee.id.toString()}>
                              {assessee.fullName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {user?.role === "admin" && (
                  <FormField
                    control={form.control}
                    name="assessorId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Assessor</FormLabel>
                        <Select 
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          defaultValue={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select an assessor" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {assessors.map((assessor) => (
                              <SelectItem key={assessor.id} value={assessor.id.toString()}>
                                {assessor.fullName || assessor.username || 'Unknown Assessor'}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Assign an assessor to conduct this assessment
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                
                <FormField
                  control={form.control}
                  name="referralType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Referral Type</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select referral type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {(referralTypeEnum.enumValues as string[]).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.charAt(0).toUpperCase() + type.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {(assessmentStatusEnum.enumValues as string[]).map((status) => (
                            <SelectItem key={status} value={status}>
                              {status.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="depositAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Deposit Amount (£)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="finalAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Final Amount (£)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="paymentStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payment Status</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {(paymentStatusEnum.enumValues as string[]).map((status) => (
                            <SelectItem key={status} value={status}>
                              {status.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="scheduledDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Scheduled Date</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          value={field.value ? format(field.value, "yyyy-MM-dd") : ""}
                          onChange={(e) => {
                            const date = e.target.value ? new Date(e.target.value) : null;
                            field.onChange(date);
                          }}
                          className="w-full"
                        />
                      </FormControl>
                      <FormDescription>
                        Date when the assessment is scheduled to take place
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Add any notes about this assessment" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <Button type="submit" disabled={isSubmitting} className="w-full">
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Assessment...
                    </>
                  ) : (
                    "Create Assessment"
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}