import React, { useState, useRef } from 'react';
import AppLayout from '@/components/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { User, Issue, InsertIssue } from '@shared/schema';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/query-client';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { 
  Bug, 
  AlertTriangle, 
  Plus, 
  Paperclip, 
  X, 
  Calendar,
  MessageSquare,
  User as UserIcon,
  Filter,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';

interface IssuesPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

// Form validation schema
const issueFormSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  description: z.string().min(10, "Description must be at least 10 characters").max(2000, "Description too long"),
  priority: z.enum(['low', 'medium', 'high', 'critical']),
  category: z.enum(['bug', 'feature_request', 'enhancement', 'other']),
  attachments: z.array(z.string()).optional(),
  metadata: z.any().optional()
});

type IssueFormData = z.infer<typeof issueFormSchema>;

const priorityColors = {
  low: 'bg-green-100 text-green-800 border-green-200',
  medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  high: 'bg-orange-100 text-orange-800 border-orange-200',
  critical: 'bg-red-100 text-red-800 border-red-200'
};

const statusColors = {
  open: 'bg-blue-100 text-blue-800 border-blue-200',
  in_progress: 'bg-purple-100 text-purple-800 border-purple-200',
  resolved: 'bg-green-100 text-green-800 border-green-200',
  closed: 'bg-gray-100 text-gray-800 border-gray-200'
};

const categoryIcons = {
  bug: Bug,
  feature_request: Plus,
  enhancement: AlertTriangle,
  other: AlertCircle
};

export default function IssuesPage({ user, logout }: IssuesPageProps = {}) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState<Issue | null>(null);
  const [isResolveDialogOpen, setIsResolveDialogOpen] = useState(false);
  const [resolvingIssueId, setResolvingIssueId] = useState<number | null>(null);
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    priority: 'all',
    category: 'all'
  });
  const [attachments, setAttachments] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Check if user has access
  if (!user || (user.role !== 'admin' && user.role !== 'assessor')) {
    return (
      <AppLayout user={user} logout={logout}>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-center">Access Denied</CardTitle>
              <CardDescription className="text-center">
                Only admin and assessor roles can access the Issues page.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </AppLayout>
    );
  }

  // Form setup
  const form = useForm<IssueFormData>({
    resolver: zodResolver(issueFormSchema),
    defaultValues: {
      title: '',
      description: '',
      priority: 'medium',
      category: 'bug',
      attachments: [],
      metadata: {}
    }
  });

  // Fetch issues
  const { data: issues = [], isLoading } = useQuery({
    queryKey: ['/api/issues', filters],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (filters.status && filters.status !== 'all') params.append('status', filters.status);
      if (filters.priority && filters.priority !== 'all') params.append('priority', filters.priority);
      if (filters.category && filters.category !== 'all') params.append('category', filters.category);
      
      const response = await fetch(`/api/issues?${params}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch issues');
      }
      
      return response.json();
    }
  });

  // Create issue mutation
  const createIssueMutation = useMutation({
    mutationFn: async (data: IssueFormData) => {
      // Store attachment filenames instead of full file data to prevent payload size issues
      const attachmentInfo: string[] = attachments.map(file => 
        `${file.name} (${(file.size / 1024).toFixed(1)}KB)`
      );

      const issueData = {
        ...data,
        attachments: attachmentInfo,
        metadata: {
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString(),
          browser: {
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled
          }
        }
      };

      return apiRequest('/api/issues', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(issueData)
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/issues'] });
      setIsCreateDialogOpen(false);
      setAttachments([]);
      form.reset();
      toast({
        title: "Issue Created",
        description: "Your issue has been successfully logged."
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create issue",
        variant: "destructive"
      });
    }
  });

  // Resolve issue mutation
  const resolveIssueMutation = useMutation({
    mutationFn: async ({ issueId, resolutionNotes }: { issueId: number; resolutionNotes?: string }) => {
      const response = await fetch(`/api/issues/${issueId}/resolve`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ resolutionNotes })
      });
      if (!response.ok) throw new Error('Failed to resolve issue');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/issues'] });
      toast({
        title: "Issue Resolved",
        description: "The issue has been marked as resolved."
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to resolve issue",
        variant: "destructive"
      });
    }
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = (data: IssueFormData) => {
    createIssueMutation.mutate(data);
  };

  const handleResolveIssue = (issueId: number) => {
    setResolvingIssueId(issueId);
    setResolutionNotes('');
    setIsResolveDialogOpen(true);
  };

  const confirmResolveIssue = () => {
    if (resolvingIssueId) {
      resolveIssueMutation.mutate({ 
        issueId: resolvingIssueId, 
        resolutionNotes: resolutionNotes.trim() || undefined 
      });
      setIsResolveDialogOpen(false);
      setResolvingIssueId(null);
      setResolutionNotes('');
    }
  };

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Issues Management</h1>
            <p className="text-muted-foreground">
              Log and track application issues, bugs, and feature requests
            </p>
          </div>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Report Issue
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Report New Issue</DialogTitle>
                <DialogDescription>
                  Provide detailed information about the issue you're experiencing
                </DialogDescription>
              </DialogHeader>
              
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Brief description of the issue" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="bug">Bug</SelectItem>
                              <SelectItem value="feature_request">Feature Request</SelectItem>
                              <SelectItem value="enhancement">Enhancement</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="priority"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Priority</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="low">Low</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="critical">Critical</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Detailed description of the issue, including steps to reproduce..."
                            className="min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Please provide as much detail as possible, including steps to reproduce the issue
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div>
                    <Label>Attachments</Label>
                    <div className="mt-2">
                      <input
                        ref={fileInputRef}
                        type="file"
                        multiple
                        accept="image/*,.pdf,.doc,.docx,.txt"
                        onChange={handleFileSelect}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full"
                      >
                        <Paperclip className="h-4 w-4 mr-2" />
                        Add Screenshots or Files
                      </Button>
                    </div>
                    
                    {attachments.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {attachments.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm truncate">{file.name}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeAttachment(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsCreateDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={createIssueMutation.isPending}
                    >
                      {createIssueMutation.isPending ? "Creating..." : "Create Issue"}
                    </Button>
                  </div>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Status</Label>
                <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Priority</Label>
                <Select value={filters.priority} onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All priorities</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Category</Label>
                <Select value={filters.category} onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All categories</SelectItem>
                    <SelectItem value="bug">Bug</SelectItem>
                    <SelectItem value="feature_request">Feature Request</SelectItem>
                    <SelectItem value="enhancement">Enhancement</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Issues List */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="grid gap-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : issues.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Bug className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">No Issues Found</h3>
                <p className="text-muted-foreground">
                  No issues match your current filters. Try adjusting the filters or create a new issue.
                </p>
              </CardContent>
            </Card>
          ) : (
            issues.map((issue: any) => {
              const CategoryIcon = categoryIcons[issue.category as keyof typeof categoryIcons];
              return (
                <Card key={issue.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <CategoryIcon className="h-5 w-5 text-gray-500" />
                          <h3 className="text-lg font-semibold">{issue.title}</h3>
                          <Badge className={priorityColors[issue.priority as keyof typeof priorityColors]}>
                            {issue.priority}
                          </Badge>
                          <Badge className={statusColors[issue.status as keyof typeof statusColors]}>
                            {issue.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        
                        <p className="text-muted-foreground mb-3 line-clamp-2">
                          {issue.description}
                        </p>
                        
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center">
                            <UserIcon className="h-4 w-4 mr-1" />
                            {issue.reportedBy?.fullName || 'Unknown'}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {format(new Date(issue.createdAt), 'MMM d, yyyy')}
                          </div>
                          {issue.attachments && issue.attachments.length > 0 && (
                            <div className="flex items-center">
                              <Paperclip className="h-4 w-4 mr-1" />
                              {issue.attachments.length} attachment{issue.attachments.length > 1 ? 's' : ''}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex space-x-2 ml-4">
                        {issue.status === 'open' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleResolveIssue(issue.id)}
                            disabled={resolveIssueMutation.isPending}
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Resolve
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedIssue(issue)}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>

        {/* Issue Details Modal */}
        {selectedIssue && (
          <Dialog 
            open={!!selectedIssue} 
            onOpenChange={(open) => {
              // Prevent toast disappearing from closing modal
              if (!open) {
                setTimeout(() => setSelectedIssue(null), 100);
              }
            }}
          >
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {categoryIcons[selectedIssue.category as keyof typeof categoryIcons] && 
                      React.createElement(categoryIcons[selectedIssue.category as keyof typeof categoryIcons], { className: "h-5 w-5" })
                    }
                    <span>Issue #{selectedIssue.id}: {selectedIssue.title}</span>
                  </div>
                  <div className="flex space-x-2">
                    <Badge className={priorityColors[selectedIssue.priority as keyof typeof priorityColors]}>
                      {selectedIssue.priority}
                    </Badge>
                    <Badge className={statusColors[selectedIssue.status as keyof typeof statusColors]}>
                      {selectedIssue.status.replace('_', ' ')}
                    </Badge>
                  </div>
                </DialogTitle>
                <DialogDescription>
                  View complete issue details, track progress with comments, and manage issue resolution.
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6">
                {/* Issue Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="font-semibold">Reported By</Label>
                    <p className="text-sm text-muted-foreground">
                      {selectedIssue.reportedBy?.fullName || 'Unknown'}
                    </p>
                  </div>
                  <div>
                    <Label className="font-semibold">Created</Label>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(selectedIssue.createdAt), 'MMM d, yyyy HH:mm')}
                    </p>
                  </div>
                  <div>
                    <Label className="font-semibold">Category</Label>
                    <p className="text-sm text-muted-foreground capitalize">
                      {selectedIssue.category.replace('_', ' ')}
                    </p>
                  </div>
                  <div>
                    <Label className="font-semibold">Priority</Label>
                    <p className="text-sm text-muted-foreground capitalize">
                      {selectedIssue.priority}
                    </p>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <Label className="font-semibold">Description</Label>
                  <div className="mt-2 p-3 bg-gray-50 rounded-md">
                    <p className="text-sm whitespace-pre-wrap">{selectedIssue.description}</p>
                  </div>
                </div>

                {/* Attachments */}
                {selectedIssue.attachments && selectedIssue.attachments.length > 0 && (
                  <div>
                    <Label className="font-semibold">Attachments</Label>
                    <div className="mt-2 space-y-2">
                      {selectedIssue.attachments.map((attachment: string, index: number) => {
                        const isImage = /\.(jpg|jpeg|png|gif|webp|bmp)$/i.test(attachment);
                        const isPdf = /\.pdf$/i.test(attachment);
                        
                        return (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded border">
                            <div className="flex items-center space-x-2">
                              <Paperclip className="h-4 w-4" />
                              <span className="text-sm font-medium">{attachment}</span>
                              {isImage && <Badge variant="outline" className="text-xs">Image</Badge>}
                              {isPdf && <Badge variant="outline" className="text-xs">PDF</Badge>}
                            </div>
                            <div className="flex space-x-2">
                              {isImage && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    // Create a modal to view the image
                                    const imageUrl = `/api/issues/${selectedIssue.id}/attachments/${attachment}`;
                                    window.open(imageUrl, '_blank');
                                  }}
                                >
                                  View
                                </Button>
                              )}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // Download the attachment
                                  const downloadUrl = `/api/issues/${selectedIssue.id}/attachments/${attachment}?download=true`;
                                  const link = document.createElement('a');
                                  link.href = downloadUrl;
                                  link.download = attachment;
                                  document.body.appendChild(link);
                                  link.click();
                                  document.body.removeChild(link);
                                }}
                              >
                                Download
                              </Button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Comments Section */}
                <IssueCommentsSection issueId={selectedIssue.id} />

                {/* Action Buttons */}
                <div className="flex justify-end space-x-2 pt-4 border-t">
                  {selectedIssue.status === 'open' && (
                    <Button
                      onClick={() => handleResolveIssue(selectedIssue.id)}
                      disabled={resolveIssueMutation.isPending}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Mark as Resolved
                    </Button>
                  )}
                  <Button variant="outline" onClick={() => setSelectedIssue(null)}>
                    Close
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Resolve Issue Dialog */}
        <Dialog open={isResolveDialogOpen} onOpenChange={setIsResolveDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Resolve Issue</DialogTitle>
              <DialogDescription>
                Mark this issue as resolved. You can optionally add resolution notes.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label>Resolution Notes (Optional)</Label>
                <Textarea
                  placeholder="Describe how the issue was resolved..."
                  value={resolutionNotes}
                  onChange={(e) => setResolutionNotes(e.target.value)}
                  className="mt-2"
                />
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsResolveDialogOpen(false);
                    setResolvingIssueId(null);
                    setResolutionNotes('');
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={confirmResolveIssue}
                  disabled={resolveIssueMutation.isPending}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {resolveIssueMutation.isPending ? 'Resolving...' : 'Mark as Resolved'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  );
}

// Issue Comments Component
function IssueCommentsSection({ issueId }: { issueId: number }) {
  const [newComment, setNewComment] = useState('');
  const [isAddingComment, setIsAddingComment] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch comments
  const { data: comments = [], isLoading: commentsLoading } = useQuery({
    queryKey: ['issue-comments', issueId],
    queryFn: () => fetch(`/api/issues/${issueId}/comments`).then(res => res.json()),
  });

  // Add comment mutation
  const addCommentMutation = useMutation({
    mutationFn: async (comment: string) => {
      const response = await fetch(`/api/issues/${issueId}/comments`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ comment, commentType: 'update' })
      });
      if (!response.ok) throw new Error('Failed to add comment');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['issue-comments', issueId] });
      setNewComment('');
      setIsAddingComment(false);
      toast({
        title: "Comment added",
        description: "Your comment has been added to the issue.",
        duration: 2000, // Shorter duration to prevent modal interference
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add comment. Please try again.",
        variant: "destructive"
      });
    }
  });

  const handleAddComment = () => {
    if (newComment.trim()) {
      addCommentMutation.mutate(newComment.trim());
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="font-semibold">Comments & Updates</Label>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsAddingComment(!isAddingComment)}
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          Add Comment
        </Button>
      </div>

      {/* Add Comment Form */}
      {isAddingComment && (
        <div className="space-y-3 p-4 border rounded-lg bg-gray-50">
          <Textarea
            placeholder="Add a comment or update about this issue..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            className="min-h-[100px]"
          />
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsAddingComment(false);
                setNewComment('');
              }}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleAddComment}
              disabled={!newComment.trim() || addCommentMutation.isPending}
            >
              {addCommentMutation.isPending ? 'Adding...' : 'Add Comment'}
            </Button>
          </div>
        </div>
      )}

      {/* Comments List */}
      <div className="space-y-3">
        {commentsLoading ? (
          <div className="space-y-3">
            {[1, 2].map((i) => (
              <div key={i} className="animate-pulse p-3 border rounded-lg">
                <div className="h-4 bg-gray-200 rounded mb-2 w-1/4"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No comments yet. Add the first comment to track progress on this issue.</p>
          </div>
        ) : (
          comments.map((comment: any) => (
            <div key={comment.id} className="p-3 border rounded-lg bg-white">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">
                      {comment.user.fullName?.charAt(0) || 'U'}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm font-medium">{comment.user.fullName}</p>
                    <p className="text-xs text-muted-foreground">
                      {format(new Date(comment.createdAt), 'MMM d, yyyy HH:mm')}
                    </p>
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {comment.commentType === 'update' ? 'Update' : 'Status Change'}
                </Badge>
              </div>
              <p className="text-sm whitespace-pre-wrap">{comment.comment}</p>
            </div>
          ))
        )}
      </div>
    </div>
  );
}