import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { apiRequest } from "@/lib/queryClient";
import { format, differenceInYears } from "date-fns";
import { CalendarIcon, LoaderCircle, AlertTriangle } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import CSRFToken from "@/components/csrf-token";
import ReCaptchaV3 from "@/components/recaptcha-v3";
import neuroelevateLogo from "@/assets/neuroelevate-logo.png";

// Define the public individual referral schema
const publicIndividualReferSchema = z
  .object({
    // School Information
    schoolName: z.string().optional(),
    schoolEmail: z.preprocess(
      (val) => (val === "" ? undefined : val),
      z.string().email("Please enter a valid school email address").optional()
    ),
    schoolPhone: z.string().optional(),
    department: z.string().optional(),
    staffName: z.string().optional(),
    staffPosition: z.string().optional(),

    // Parent/Guardian Information
    parentName: z.string().optional(),
    parentEmail: z.preprocess(
      (val) => (val === "" ? undefined : val),
      z.string().email("Please enter a valid email address").optional()
    ),
    parentPhone: z.string().optional(),
    relationshipWithAssessee: z.string().optional(),

    // Personal Information
    assesseeFullName: z.string().min(1, "Assessee's name is required"),
    assesseeEmail: z.preprocess(
      (val) => (val === "" ? undefined : val),
      z.string().email("Please enter a valid email address").optional()
    ),
    assesseePhone: z.string().optional(),
    dateOfBirth: z.date({
      required_error: "Date of birth is required",
    }),
    studentYear: z.string().optional(),
    reasonForReferral: z.string().min(1, "Reason for assessment is required"),
    assessmentConcerns: z.array(z.string()).optional(),
    previousAssessment: z.enum(["yes", "no"]),
    previousAssessmentDetails: z.string().optional(),
    additionalNotes: z.string().optional(),
    schoolContactConsent: z.boolean().optional(),
  })
  .superRefine((data, ctx) => {
    const ageDiff = Date.now() - data.dateOfBirth.getTime();
    const age = Math.floor(ageDiff / (1000 * 60 * 60 * 24 * 365.25));
    if (age < 16) {
      if (!data.parentName) {
        ctx.addIssue({ code: "custom", message: "Parent/guardian name is required", path: ["parentName"] });
      }
      if (!data.parentEmail) {
        ctx.addIssue({ code: "custom", message: "Parent/guardian email is required", path: ["parentEmail"] });
      }
      if (!data.parentPhone) {
        ctx.addIssue({ code: "custom", message: "Parent/guardian phone number is required", path: ["parentPhone"] });
      }
      if (!data.relationshipWithAssessee) {
        ctx.addIssue({ code: "custom", message: "Relationship with assessee is required", path: ["relationshipWithAssessee"] });
      }
      if (!data.schoolName) {
        ctx.addIssue({ code: "custom", message: "School name is required", path: ["schoolName"] });
      }
      if (!data.schoolEmail) {
        ctx.addIssue({ code: "custom", message: "School email is required", path: ["schoolEmail"] });
      }
      if (!data.schoolPhone) {
        ctx.addIssue({ code: "custom", message: "School phone number is required", path: ["schoolPhone"] });
      }
      if (!data.department) {
        ctx.addIssue({ code: "custom", message: "Department is required", path: ["department"] });
      }
      if (!data.staffName) {
        ctx.addIssue({ code: "custom", message: "Staff member name is required", path: ["staffName"] });
      }
      if (!data.staffPosition) {
        ctx.addIssue({ code: "custom", message: "Staff position is required", path: ["staffPosition"] });
      }
      if (!data.schoolContactConsent) {
        ctx.addIssue({ code: "custom", message: "Consent to contact school is required", path: ["schoolContactConsent"] });
      }
    }
  });

type PublicIndividualReferFormData = z.infer<typeof publicIndividualReferSchema>;

export default function PublicIndividualReferPage() {
  const { toast } = useToast();
  const [submitted, setSubmitted] = useState(false);
  const [trackingId, setTrackingId] = useState("");
  const [csrfToken, setCsrfToken] = useState<string | null>(null);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const [formBlocked, setFormBlocked] = useState<boolean>(false);
  
  // Initialize the form
  const form = useForm<PublicIndividualReferFormData>({
    resolver: zodResolver(publicIndividualReferSchema),
    defaultValues: {
      schoolName: "",
      schoolEmail: "",
      schoolPhone: "",
      department: "",
      staffName: "",
      staffPosition: "",
      parentName: "",
      parentEmail: "",
      parentPhone: "",
      relationshipWithAssessee: "",
      assesseeFullName: "",
      assesseeEmail: "",
      assesseePhone: "",
      studentYear: "",
      reasonForReferral: "",
      assessmentConcerns: [],
      previousAssessment: "no",
      previousAssessmentDetails: "",
      additionalNotes: "",
      schoolContactConsent: false,
    },
  });

  const dob = form.watch("dateOfBirth");
  const under16 = dob ? differenceInYears(new Date(), dob) < 16 : false;

  // Clear validation errors for parent and school fields when they are hidden
  useEffect(() => {
    if (!under16) {
      form.clearErrors([
        "parentName",
        "parentEmail",
        "parentPhone",
        "relationshipWithAssessee",
        "schoolName",
        "schoolEmail",
        "schoolPhone",
        "department",
        "staffName",
        "staffPosition",
        "schoolContactConsent",
      ]);
    }
  }, [under16, form]);
  
  // Handle recaptcha expiration
  const handleRecaptchaExpired = () => {
    setRecaptchaToken(null);
    setFormBlocked(true);
    toast({
      title: "reCAPTCHA Expired",
      description: "Please verify that you are not a robot again",
      variant: "destructive"
    });
  };
  
  // Mutation for submitting the individual referral
  const referralMutation = useMutation({
    mutationFn: async (data: PublicIndividualReferFormData & { recaptchaToken: string }) => {
      // Ensure we have CSRF and reCAPTCHA tokens
      if (!csrfToken) {
        throw new Error("Security token missing. Please refresh the page and try again.");
      }
      
      if (!data.recaptchaToken) {
        throw new Error("Please complete the reCAPTCHA verification.");
      }
      
      // Add tokens to the form data
      const completeData = {
        ...data,
        _csrf: csrfToken,
        recaptchaToken: data.recaptchaToken
      };
      
      const referralResponse = await apiRequest("POST", "/api/public/individual-referral", completeData);
      
      if (!referralResponse.ok) {
        const errorData = await referralResponse.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to submit referral");
      }
      
      return referralResponse.json();
    },
    onSuccess: (data) => {
      setTrackingId(data.trackingId);
      setSubmitted(true);
      form.reset();
      toast({
        title: "Referral Submitted",
        description: "Your individual referral has been successfully submitted.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
      
      // Handle CSRF token errors by getting a new token
      if (error.message.includes("CSRF")) {
        toast({
          title: "Session Expired",
          description: "Your session has expired. Please try again.",
          variant: "destructive"
        });
      }
    },
  });
  
  // Form submission handler
  const onSubmit = async (data: PublicIndividualReferFormData) => {
    const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;
    const grecaptchaObj = window.grecaptcha?.enterprise || window.grecaptcha;

    try {
      if (grecaptchaObj && siteKey) {
        const freshToken = await grecaptchaObj.execute(siteKey, { action: "submit" });
        setRecaptchaToken(freshToken);
        referralMutation.mutate({ ...data, recaptchaToken: freshToken });
      } else {
        throw new Error("reCAPTCHA not ready");
      }
    } catch (err) {
      toast({
        title: "Verification Error",
        description: "Failed to complete reCAPTCHA verification.",
        variant: "destructive"
      });
      setFormBlocked(true);
    }
  };
  
  if (submitted) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-muted/30">
        <Card className="w-[800px] max-w-[90vw]">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              <img src={neuroelevateLogo} alt="NeuroElevate Logo" className="h-24 mx-auto" />
            </div>
            <CardTitle className="text-2xl font-bold text-primary">Referral Submitted Successfully</CardTitle>
            <CardDescription>Thank you for your referral</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertTitle>Your tracking ID</AlertTitle>
              <AlertDescription>
                Please save this tracking ID for future reference: <span className="font-bold">{trackingId}</span>
              </AlertDescription>
            </Alert>
            <p>
              We have received your individual referral and will process it shortly. A confirmation email has been sent to the email address you provided.
            </p>
            <p>
              If you need to check the status of this referral, please use the tracking ID above.
            </p>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={() => setSubmitted(false)}>Submit Another Referral</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-muted/30 py-8">
      <Card className="w-[1000px] max-w-[95vw]">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            <img src={neuroelevateLogo} alt="NeuroElevate Logo" className="h-24 mx-auto" />
          </div>
          <CardTitle className="text-2xl font-bold text-primary">Individual Referral Form</CardTitle>
          <CardDescription>
            Referral Form for a Specific Learning Difficulty (SpLD) assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Assessee Section */}
              <div>
                <h3 className="text-lg font-semibold mt-8">Personal Information</h3>
                <Separator className="my-2" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <FormField
                    control={form.control}
                    name="assesseeFullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Full name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="dateOfBirth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date of Birth</FormLabel>
                        <FormControl>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? format(field.value, "PPP") : <span>Pick a date</span>}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <div className="py-1.5 px-3 border-b border-border">
                                <div className="flex justify-center space-x-2">
                                  <Select
                                    onValueChange={(value) => {
                                      const currentDate = field.value || new Date();
                                      const newDate = new Date(currentDate);
                                      newDate.setFullYear(parseInt(value));
                                      field.onChange(newDate);
                                    }}
                                    value={field.value ? field.value.getFullYear().toString() : undefined}
                                  >
                                    <SelectTrigger className="w-[110px] h-8">
                                      <SelectValue placeholder="Year" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {Array.from({ length: (new Date().getFullYear() - 1940) + 1 }, (_, i) => (
                                        <SelectItem 
                                          key={i} 
                                          value={(new Date().getFullYear() - i).toString()}
                                        >
                                          {new Date().getFullYear() - i}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={(date) => {
                                  if (date) {
                                    // Preserve the selected year when picking a date
                                    const selectedYear = field.value ? field.value.getFullYear() : new Date().getFullYear();
                                    const newDate = new Date(date);
                                    newDate.setFullYear(selectedYear);
                                    field.onChange(newDate);
                                  } else {
                                    field.onChange(date);
                                  }
                                }}
                                defaultMonth={field.value}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="assesseeEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            type="email" 
                            placeholder="<EMAIL>" 
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="assesseePhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input
                            type="tel" 
                            placeholder="Phone number" 
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  </div>
                </div>

              {under16 && (
                <>
                  {/* Parent/Guardian Information */}
                  <div>
                    <h3 className="text-lg font-semibold mt-8">Parent/Guardian Information</h3>
                    <Separator className="my-2" />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <FormField
                        control={form.control}
                        name="parentName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Parent/Guardian Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Full name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="relationshipWithAssessee"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Relationship with Assessee</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g. Parent, Guardian, etc." {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="parentEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="parentPhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input
                                type="tel"
                                placeholder="Phone number"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* School Information */}
                  <div>
                    <h3 className="text-lg font-semibold mt-8">School Information</h3>
                    <Separator className="my-2" />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <FormField
                        control={form.control}
                        name="schoolName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Name</FormLabel>
                            <FormControl>
                              <Input placeholder="School name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="department"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Department</FormLabel>
                            <FormControl>
                              <Input placeholder="Department" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="schoolEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Email</FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="schoolPhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input
                                type="tel"
                                placeholder="Phone number"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="staffName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff Member Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Staff member name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="staffPosition"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Position/Role</FormLabel>
                            <FormControl>
                              <Input placeholder="Position/role" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="studentYear"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Year/Grade (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g., Year 10" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="mt-4">
                      <FormField
                        control={form.control}
                        name="schoolContactConsent"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <input type="checkbox" {...field} checked={field.value} className="h-4 w-4" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              I consent to NeuroElevate contacting the school provided above regarding this assessment
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </>
              )}

              {/* Assessment Details */}
              <div>
                <h3 className="text-lg font-semibold mt-8">Assessment Details</h3>
                <Separator className="my-2" />
                <div className="space-y-4 mt-4">
                  <FormField
                    control={form.control}
                    name="reasonForReferral"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reason for Assessment</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Please provide a brief overview of why you are seeking an assessment"
                            {...field}
                            className="min-h-[100px]"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="assessmentConcerns"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Select any suspected difficulties</FormLabel>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 mt-2">
                          {[
                            { value: "Dyslexia", label: "Dyslexia" },
                            { value: "Dyspraxia/DCD", label: "Dyspraxia/DCD" },
                            { value: "ADHD", label: "ADHD" },
                          ].map((option) => (
                            <label key={option.value} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                value={option.value}
                                checked={field.value?.includes(option.value) || false}
                                onChange={(e) => {
                                  const checked = e.target.checked;
                                  const newValue = checked
                                    ? [...(field.value || []), option.value]
                                    : (field.value || []).filter((v) => v !== option.value);
                                  field.onChange(newValue);
                                }}
                                className="h-4 w-4"
                              />
                              <span className="text-sm">{option.label}</span>
                            </label>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="previousAssessment"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Has the assessee had a previous SpLD assessment?</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex flex-row space-x-4"
                          >
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="yes" />
                              </FormControl>
                              <FormLabel className="font-normal">Yes</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="no" />
                              </FormControl>
                              <FormLabel className="font-normal">No</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch("previousAssessment") === "yes" && (
                    <FormField
                      control={form.control}
                      name="previousAssessmentDetails"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Previous Assessment Details</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Please provide details of the previous assessment (when, where, outcome)"
                              {...field}
                              className="min-h-[80px]"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <FormField
                    control={form.control}
                    name="additionalNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Additional Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Any additional information that might be relevant"
                            {...field}
                            className="min-h-[80px]"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <p className="text-sm text-muted-foreground">
                    Once we have received your referral, we will send you a questionnaire to complete so that we can collect further background information to help us with your assessment. For learners under 16, we also gather information from their school, in order to clarify their learning needs and to provide a wider context in which to place these needs.
                  </p>
                </div>
              </div>
              
              {/* Security Components */}
              <div className="mt-8 border rounded-md p-4 bg-muted/30">
                <h3 className="text-lg font-semibold mb-4">Security Verification</h3>
                
                {formBlocked && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Action Required</AlertTitle>
                    <AlertDescription>
                      Please complete the reCAPTCHA verification below to continue.
                    </AlertDescription>
                  </Alert>
                )}
                
                {/* Hidden CSRF Token */}
                <CSRFToken onTokenReceived={(token) => setCsrfToken(token)} />
                
                {/* reCAPTCHA */}
                <div className="my-4">
                  <p className="text-sm text-muted-foreground mb-2">
                    Please verify that you are not a robot by completing the reCAPTCHA below:
                  </p>
                  <ReCaptchaV3
                    onChange={setRecaptchaToken}
                    onExpired={handleRecaptchaExpired}
                    action="individual_referral"
                  />
                </div>
              </div>
              
              <div className="mt-8 flex justify-end">
                <Button
                  type="submit"
                  size="lg"
                  className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-700 px-8"
                  disabled={referralMutation.isPending}
                >
                  {referralMutation.isPending ? (
                    <>
                      <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    "Submit Referral"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}