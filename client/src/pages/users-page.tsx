import React, { useState } from 'react';
import AppLayout from '@/components/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, PlusCircle, Edit, Trash } from 'lucide-react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { User } from '@shared/schema';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

interface UsersPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

// Define form schemas
const editUserSchema = z.object({
  id: z.number(),
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Please enter a valid email address"),
  role: z.string().min(1, "Please select a role"),
  status: z.string().min(1, "Please select a status"),
});

const deleteUserSchema = z.object({
  id: z.number(),
  confirm: z.boolean().refine(val => val === true, {
    message: "You must confirm this action"
  })
});

type EditUserFormData = z.infer<typeof editUserSchema>;
type DeleteUserFormData = z.infer<typeof deleteUserSchema>;

export default function UsersPage({ user, logout }: UsersPageProps = {}) {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Query to get all users
  const { data: users = [], isLoading } = useQuery({
    queryKey: ['/api/users'],
    enabled: !!user && user.role === 'admin',
  });
  
  // Mutation to update a user
  const updateUserMutation = useMutation({
    mutationFn: async (data: EditUserFormData) => {
      const response = await apiRequest(
        "PATCH", 
        `/api/admin/users/${data.id}`, 
        data
      );
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update user");
      }
      return response.json();
    },
    onSuccess: () => {
      setIsEditDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      toast({
        title: "User updated",
        description: "The user has been updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update user",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation to delete a user
  const deleteUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest(
        "DELETE", 
        `/api/admin/users/${userId}`
      );
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete user");
      }
      return response.json();
    },
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      toast({
        title: "User deleted",
        description: "The user has been deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete user",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Forms
  const editForm = useForm<EditUserFormData>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      id: 0,
      username: "",
      email: "",
      role: "",
      status: "",
    },
  });

  const deleteForm = useForm<DeleteUserFormData>({
    resolver: zodResolver(deleteUserSchema),
    defaultValues: {
      id: 0,
      confirm: false,
    },
  });

  // Handlers
  const handleEditSubmit = (data: EditUserFormData) => {
    updateUserMutation.mutate(data);
  };

  const handleDeleteSubmit = (data: DeleteUserFormData) => {
    if (data.confirm) {
      deleteUserMutation.mutate(data.id);
    }
  };

  const handleEditClick = (user: User) => {
    setSelectedUser(user);
    editForm.reset({
      id: user.id,
      username: user.username || "",
      email: user.email || "",
      role: user.role || "",
      status: user.status || "active",
    });
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (user: User) => {
    setSelectedUser(user);
    deleteForm.reset({
      id: user.id,
      confirm: false,
    });
    setIsDeleteDialogOpen(true);
  };

  // Mock data for UI preview
  const mockUsers = [
    {
      id: 1,
      fullName: 'Dr. Sarah Wilson',
      email: '<EMAIL>',
      role: 'assessor',
      createdAt: '2022-09-15T10:00:00Z',
    },
    {
      id: 2,
      fullName: 'Jane Smith',
      email: '<EMAIL>',
      role: 'school',
      organization: 'Westfield Primary School',
      createdAt: '2022-10-22T14:30:00Z',
    },
    {
      id: 3,
      fullName: 'John Parker',
      email: '<EMAIL>',
      role: 'university',
      organization: 'Metropolitan University',
      createdAt: '2022-11-05T09:15:00Z',
    },
    {
      id: 4,
      fullName: 'Michael Johnson',
      email: '<EMAIL>',
      role: 'parent',
      createdAt: '2023-01-10T16:45:00Z',
    },
    {
      id: 5,
      fullName: 'Alex Thompson',
      email: '<EMAIL>',
      role: 'assessee',
      createdAt: '2023-02-18T11:20:00Z',
    },
    {
      id: 6,
      fullName: 'Dr. Michael Brown',
      email: '<EMAIL>',
      role: 'assessor',
      createdAt: '2022-08-30T13:10:00Z',
    },
  ];

  // Filter users based on search query
  const filteredUsers = searchQuery
    ? (Array.isArray(users) && users.length > 0 ? users : mockUsers).filter(user =>
        (user.fullName?.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
        (user.email?.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
        (user.role?.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
        (user.organization?.toLowerCase().includes(searchQuery.toLowerCase()) || false)
      )
    : (Array.isArray(users) && users.length > 0 ? users : mockUsers);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Role badge component
  const RoleBadge = ({ role }: { role: string }) => {
    const roleConfig: Record<string, { color: string, label: string }> = {
      admin: { color: 'bg-red-100 text-red-800', label: 'Admin' },
      assessor: { color: 'bg-blue-100 text-blue-800', label: 'Assessor' },
      school: { color: 'bg-green-100 text-green-800', label: 'School' },
      university: { color: 'bg-purple-100 text-purple-800', label: 'University' },
      parent: { color: 'bg-yellow-100 text-yellow-800', label: 'Parent' },
      assessee: { color: 'bg-orange-100 text-orange-800', label: 'Assessee' },
    };

    const { color, label } = roleConfig[role] || { color: 'bg-gray-100 text-gray-800', label: role };

    return (
      <span className={`inline-flex rounded-full px-2.5 py-0.5 text-xs font-medium ${color}`}>
        {label}
      </span>
    );
  };

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">User Management</h1>
          <Button>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>

        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search users..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Users</CardTitle>
            <CardDescription>
              Manage users with access to the system.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative overflow-x-auto">
              <table className="w-full text-sm text-left text-gray-500">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3">Name</th>
                    <th scope="col" className="px-6 py-3">Email</th>
                    <th scope="col" className="px-6 py-3">Role</th>
                    <th scope="col" className="px-6 py-3">Organization</th>
                    <th scope="col" className="px-6 py-3">Created</th>
                    <th scope="col" className="px-6 py-3">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="bg-white border-b hover:bg-gray-50">
                      <td className="px-6 py-4 font-medium text-gray-900">{user.fullName}</td>
                      <td className="px-6 py-4">{user.email}</td>
                      <td className="px-6 py-4">
                        <RoleBadge role={user.role} />
                      </td>
                      <td className="px-6 py-4">{user.organization || '-'}</td>
                      <td className="px-6 py-4">{formatDate(user.createdAt)}</td>
                      <td className="px-6 py-4">
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEditClick(user)}
                          >
                            <Edit className="h-3.5 w-3.5" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="text-red-500 hover:text-red-700"
                            onClick={() => handleDeleteClick(user)}
                          >
                            <Trash className="h-3.5 w-3.5" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Make changes to the user account. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditSubmit)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input placeholder="username" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="admin">Administrator</SelectItem>
                        <SelectItem value="assessor">Assessor</SelectItem>
                        <SelectItem value="school">School</SelectItem>
                        <SelectItem value="university">University</SelectItem>
                        <SelectItem value="parent">Parent</SelectItem>
                        <SelectItem value="assessee">Assessee</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="email_verification_pending">Email Verification Pending</SelectItem>
                        <SelectItem value="admin_approval_pending">Admin Approval Pending</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="submit" disabled={updateUserMutation.isPending}>
                  {updateUserMutation.isPending ? "Saving..." : "Save changes"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <Form {...deleteForm}>
            <form onSubmit={deleteForm.handleSubmit(handleDeleteSubmit)} className="space-y-4">
              {selectedUser && (
                <div className="p-4 border rounded-md bg-muted">
                  <p><strong>Username:</strong> {selectedUser.username}</p>
                  <p><strong>Email:</strong> {selectedUser.email}</p>
                  <p><strong>Role:</strong> {selectedUser.role}</p>
                </div>
              )}
              <FormField
                control={deleteForm.control}
                name="confirm"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="h-4 w-4 mt-1"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        I confirm that I want to delete this user
                      </FormLabel>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  variant="destructive" 
                  disabled={deleteUserMutation.isPending || !deleteForm.watch("confirm")}
                >
                  {deleteUserMutation.isPending ? "Deleting..." : "Delete User"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}