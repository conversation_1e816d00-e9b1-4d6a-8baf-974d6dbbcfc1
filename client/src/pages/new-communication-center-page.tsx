import { useState, useEffect, useRef } from "react";
import { useMessaging } from "@/hooks/use-messaging";
import { useNotifications } from "@/hooks/use-notifications";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { UserSelect } from "@/components/user-select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Loader2, Send, Plus, X, CheckCircle, UserIcon, Bell, MessageSquare, Settings, ArrowLeft } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { User } from "@shared/schema";
import AppLayout from "@/components/layouts/app-layout";

// Form schema for creating a new conversation
const createConversationSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  participantIds: z.array(z.number()).min(1, { message: "Select at least one participant" }),
  assessmentId: z.number().optional(),
  createdById: z.number(),
});

type CreateConversationFormValues = z.infer<typeof createConversationSchema>;

// Form schema for sending a message
const sendMessageSchema = z.object({
  content: z.string().min(1, { message: "Message cannot be empty" }),
});

type SendMessageFormValues = z.infer<typeof sendMessageSchema>;

const NotificationBadge = () => {
  const { unreadCount, isLoadingUnreadCount } = useNotifications();
  
  if (isLoadingUnreadCount) {
    return <div className="h-2 w-2 rounded-full bg-muted animate-pulse"></div>;
  }
  
  if (!unreadCount) return null;
  
  return (
    <Badge variant="destructive" className="absolute -top-1 -right-1 px-1.5 py-0.5 text-[10px]">
      {unreadCount}
    </Badge>
  );
};

export default function CommunicationCenterPage({ user, logout }: { user: User | null, logout: () => Promise<void> }) {
  const { toast } = useToast();
  const { 
    conversations, 
    isLoadingConversations, 
    getConversation, 
    createConversationMutation, 
    sendMessageMutation,
    setupMessagePolling 
  } = useMessaging();
  
  const {
    notifications,
    isLoadingNotifications,
    markAsReadMutation,
    markAllAsReadMutation,
    setupNotificationPolling,
    refetchNotifications,
    refetchUnreadCount
  } = useNotifications();
  
  const [activeTab, setActiveTab] = useState("messages");
  const [selectedConversation, setSelectedConversation] = useState<number | null>(null);
  const [isNewConversationDialogOpen, setIsNewConversationDialogOpen] = useState(false);
  const [messageSearchQuery, setMessageSearchQuery] = useState("");
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  
  // Get data for the selected conversation
  const {
    data: conversationData,
    isLoading: isLoadingConversation,
  } = getConversation(selectedConversation || 0);
  
  // Filter messages by search query if applicable
  const filteredMessages = conversationData?.messages?.filter((message: any) => {
    if (!messageSearchQuery) return true;
    return message.content.toLowerCase().includes(messageSearchQuery.toLowerCase());
  });
  
  // Set up form for creating a new conversation
  const createConversationForm = useForm<CreateConversationFormValues>({
    resolver: zodResolver(createConversationSchema),
    defaultValues: {
      title: "",
      participantIds: [],
      createdById: user?.id || 0, // Will be updated when user is loaded
    },
  });
  
  // Update form values when user is loaded
  useEffect(() => {
    if (user?.id) {
      createConversationForm.setValue('createdById', user.id);
    }
  }, [user, createConversationForm]);
  
  // Set up form for sending a message
  const sendMessageForm = useForm<SendMessageFormValues>({
    resolver: zodResolver(sendMessageSchema),
    defaultValues: {
      content: "",
    },
  });
  
  // Handle creating a new conversation
  const handleCreateConversation = (values: CreateConversationFormValues) => {
    // Make sure we have a valid user ID
    if (!user?.id) {
      toast({
        title: "Authentication error",
        description: "You must be logged in to create a conversation",
        variant: "destructive"
      });
      return;
    }
    
    // Ensure createdById matches the current user
    values.createdById = user.id;
    
    createConversationMutation.mutate(values, {
      onSuccess: (data) => {
        toast({
          title: "Conversation created",
          description: "Your new conversation has been created",
        });
        setIsNewConversationDialogOpen(false);
        createConversationForm.reset();
        setSelectedConversation(data.id);
        setActiveTab("messages");
      },
      onError: (error) => {
        toast({
          title: "Failed to create conversation",
          description: error.message || "Please try again later",
          variant: "destructive"
        });
      }
    });
  };
  
  // Handle sending a message
  const handleSendMessage = (values: SendMessageFormValues) => {
    if (!selectedConversation) {
      toast({
        title: "No conversation selected",
        description: "Please select a conversation to send a message",
        variant: "destructive"
      });
      return;
    }
    
    if (!user?.id) {
      toast({
        title: "Authentication error",
        description: "You must be logged in to send a message",
        variant: "destructive"
      });
      return;
    }
    
    sendMessageMutation.mutate({
      conversationId: selectedConversation,
      content: values.content
    }, {
      onSuccess: (newMessage) => {
        toast({
          title: "Message sent",
          description: "Your message has been sent successfully"
        });
        sendMessageForm.reset();
        
        // Manual update to add sender info for the new message
        if (conversationData) {
          // Create a new message with sender information
          const messageWithSender = {
            ...newMessage,
            sender: user
          };
          
          // Update local state to include this new message with proper sender info
          queryClient.setQueryData(
            ['/api/conversations', selectedConversation], 
            {
              ...conversationData,
              messages: [
                ...conversationData.messages || [],
                messageWithSender
              ]
            }
          );
        }
      },
      onError: (error) => {
        toast({
          title: "Failed to send message",
          description: error.message || "Please try again later",
          variant: "destructive"
        });
      }
    });
  };
  
  // Set up polling for new messages and notifications
  useEffect(() => {
    const clearMessagePolling = selectedConversation ? setupMessagePolling(selectedConversation) : undefined;
    const clearNotificationPolling = setupNotificationPolling();

    return () => {
      if (clearMessagePolling) clearMessagePolling();
      clearNotificationPolling();
    };
  }, [selectedConversation, setupMessagePolling, setupNotificationPolling]);

  // Scroll to the latest message whenever the message list changes
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversationData?.messages?.length, selectedConversation]);
  
  // If a new conversation is selected, mark all messages in that conversation as read
  useEffect(() => {
    if (selectedConversation && conversationData) {
      // TODO: Implement marking all messages as read in a conversation
    }
  }, [selectedConversation, conversationData]);
  
  // Redirect if user is not authenticated
  if (!user) {
    return (
      <div className="container mx-auto py-10 text-center">
        <Loader2 className="h-10 w-10 animate-spin text-primary mx-auto mb-4" />
        <p>Loading user information...</p>
      </div>
    );
  }

  // Main content wrapped in the AppLayout
  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Conversations</h1>
          <Button 
            onClick={() => setIsNewConversationDialogOpen(true)} 
            className="flex items-center gap-2"
            disabled={createConversationMutation.isPending}
          >
            {createConversationMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Creating...
              </>
            ) : (
              <>
                <Plus size={16} /> New Conversation
              </>
            )}
          </Button>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" defaultValue="messages">
          <TabsList className="mb-6">
            <TabsTrigger value="messages" className="relative" onClick={() => setActiveTab("messages")}>
              <MessageSquare className="mr-2 h-4 w-4" />
              Conversations
              {conversations && conversations.length > 0 && (
                <Badge variant="outline" className="ml-2">{conversations.length}</Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="notifications" className="relative" onClick={() => setActiveTab("notifications")}>
              <Bell className="mr-2 h-4 w-4" />
              Notifications
              <NotificationBadge />
            </TabsTrigger>
            <TabsTrigger value="settings" onClick={() => setActiveTab("settings")}>
              <Settings className="mr-2 h-4 w-4" />
              Notification Settings
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="messages" className="mt-0">
            <div className="grid grid-cols-12 gap-6 h-[70vh]">
              {/* Conversations List */}
              <Card className="col-span-4 h-full">
                <CardHeader className="pb-2">
                  <CardTitle>Conversations</CardTitle>
                  <CardDescription>
                    {isLoadingConversations ? "Loading conversations..." : `${conversations?.length || 0} conversations`}
                  </CardDescription>
                </CardHeader>
                <Separator />
                <CardContent className="p-0">
                  <ScrollArea className="h-[calc(70vh-8rem)]">
                    {isLoadingConversations ? (
                      <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : conversations && conversations.length > 0 ? (
                      <div className="divide-y">
                        {conversations.map((conversation: any) => (
                          <div 
                            key={conversation.id}
                            className={`p-4 cursor-pointer hover:bg-secondary/50 transition-colors ${selectedConversation === conversation.id ? 'bg-secondary' : ''}`}
                            onClick={() => setSelectedConversation(conversation.id)}
                          >
                            <div className="flex items-center justify-between">
                              <h3 className="font-medium">{conversation.title}</h3>
                              {conversation.unreadCount > 0 && (
                                <Badge>{conversation.unreadCount}</Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mt-1 truncate">
                              {conversation.latestMessage ? conversation.latestMessage.content : "No messages yet"}
                            </p>
                            <div className="flex items-center justify-between mt-2">
                              <div className="flex space-x-1">
                                {conversation.participants?.slice(0, 3).map((participant: any) => (
                                  <div key={participant.id} className="h-6 w-6 rounded-full bg-primary flex items-center justify-center text-[10px] text-primary-foreground ring-1 ring-background">
                                    {participant.email?.charAt(0).toUpperCase()}
                                  </div>
                                ))}
                                {conversation.participants?.length > 3 && (
                                  <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-[10px] ring-1 ring-background">
                                    +{conversation.participants.length - 3}
                                  </div>
                                )}
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {conversation.updatedAt && formatDistanceToNow(new Date(conversation.updatedAt), { addSuffix: true })}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-6 text-center">
                        <p className="text-muted-foreground mb-4">No conversations yet</p>
                        <Button variant="outline" onClick={() => setIsNewConversationDialogOpen(true)} className="flex items-center gap-2">
                          <Plus size={16} /> Start a conversation
                        </Button>
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
              
              {/* Conversation Detail */}
              <Card className="col-span-8 h-full">
                {isLoadingConversation ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : selectedConversation && conversationData ? (
                  <>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-8 w-8 md:hidden" 
                            onClick={() => setSelectedConversation(null)}
                          >
                            <ArrowLeft className="h-4 w-4" />
                            <span className="sr-only">Back to conversations</span>
                          </Button>
                          <div>
                            <CardTitle>{conversationData.conversation.title}</CardTitle>
                            <CardDescription>
                              {conversationData.conversation.participants?.length} participants
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="relative">
                            <Input
                              placeholder="Search messages..."
                              value={messageSearchQuery}
                              onChange={(e) => setMessageSearchQuery(e.target.value)}
                              className="h-9 pr-8 w-[200px]"
                            />
                            {messageSearchQuery && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="absolute right-0 top-0 h-full w-8"
                                onClick={() => setMessageSearchQuery("")}
                              >
                                <X className="h-4 w-4" />
                                <span className="sr-only">Clear search</span>
                              </Button>
                            )}
                          </div>
                          <Button variant="outline" size="sm">
                            Add Participant
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <Separator />
                    <CardContent className="p-4 h-[calc(70vh-16rem)]">
                      <ScrollArea className="h-full pr-4">
                        {/* Show search results count if search is active */}
                        {messageSearchQuery && filteredMessages && (
                          <div className="mb-4 p-2 bg-muted/50 rounded-md text-sm">
                            {filteredMessages.length === 0 ? (
                              <p className="text-center text-muted-foreground">No messages match your search</p>
                            ) : (
                              <p className="text-center">Found {filteredMessages.length} {filteredMessages.length === 1 ? 'message' : 'messages'} matching "{messageSearchQuery}"</p>
                            )}
                          </div>
                        )}
                        
                        {conversationData.messages && conversationData.messages.length > 0 ? (
                          <div className="space-y-4">
                            {(messageSearchQuery ? filteredMessages : conversationData.messages).map((message: any) => {
                              const isCurrentUser = message.senderId === user?.id;
                              return (
                                <div key={message.id} className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                                  <div className={`max-w-[80%] ${isCurrentUser ? 'bg-blue-100 border border-blue-200' : 'bg-purple-50 border border-purple-100'} p-3 rounded-lg`}>
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className={`h-6 w-6 rounded-full ${isCurrentUser ? 'bg-blue-500' : 'bg-purple-500'} flex items-center justify-center text-[10px] text-white`}>
                                        {message.sender?.fullName?.charAt(0).toUpperCase() || 
                                         message.sender?.email?.charAt(0).toUpperCase() || 
                                         (user && isCurrentUser ? user.fullName?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() : 'U')}
                                      </div>
                                      <span className="text-xs font-medium text-gray-700">
                                        {isCurrentUser 
                                          ? (message.sender?.fullName || message.sender?.email || user?.fullName || user?.email || "You") + " (You)"
                                          : message.sender?.fullName || message.sender?.email || "Unknown user"}
                                      </span>
                                    </div>
                                    <p className="whitespace-pre-wrap break-words text-gray-800">{message.content}</p>
                                    <div className="flex items-center justify-end gap-1 mt-1">
                                      <span className="text-xs text-gray-500">
                                        {message.createdAt && formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                                      </span>
                                      {message.editedAt && (
                                        <span className="text-xs text-gray-500">(edited)</span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                            <div ref={messagesEndRef} />
                          </div>
                        ) : (
                          <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                              <p className="text-muted-foreground">No messages yet</p>
                              <p className="text-sm text-muted-foreground mt-1">Start the conversation by sending a message below</p>
                            </div>
                          </div>
                        )}
                      </ScrollArea>
                    </CardContent>
                    <Separator />
                    <CardFooter className="p-4">
                      <Form {...sendMessageForm}>
                        <form onSubmit={sendMessageForm.handleSubmit(handleSendMessage)} className="w-full">
                          <div className="flex gap-2">
                            <FormField
                              control={sendMessageForm.control}
                              name="content"
                              render={({ field }) => (
                                <FormItem className="flex-1">
                                  <FormControl>
                                    <Input 
                                      placeholder="Type your message..."
                                      {...field}
                                      disabled={sendMessageMutation.isPending}
                                      autoComplete="off"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <Button 
                              type="submit" 
                              size="icon" 
                              disabled={sendMessageMutation.isPending || !sendMessageForm.formState.isValid}
                            >
                              {sendMessageMutation.isPending ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Send className="h-4 w-4" />
                              )}
                              <span className="sr-only">Send message</span>
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </CardFooter>
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center p-8">
                      <MessageSquare className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-xl font-medium mb-2">Select a conversation</h3>
                      <p className="text-muted-foreground mb-6">Choose an existing conversation or start a new one</p>
                      <Button 
                        onClick={() => setIsNewConversationDialogOpen(true)} 
                        className="flex items-center gap-2"
                      >
                        <Plus size={16} /> New Conversation
                      </Button>
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="notifications" className="mt-0">
            <Card className="h-[70vh]">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <div>
                  <CardTitle>Notifications</CardTitle>
                  <CardDescription>
                    {isLoadingNotifications ? "Loading notifications..." : `${notifications?.length || 0} notifications`}
                    {/* Debug notification IDs */}
                    {notifications && notifications.length > 0 && !isLoadingNotifications && (
                      <div className="text-xs text-muted-foreground mt-1">
                        IDs: {notifications.map((n: any) => n.id).join(', ')}
                      </div>
                    )}
                  </CardDescription>
                </div>
                {notifications && notifications.length > 0 && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => {
                      console.log("Mark all as read clicked");
                      try {
                        // Optimistically update UI to mark all notifications as read
                        queryClient.setQueryData(['/api/notifications'], (old: any[]) => {
                          if (!old) return old;
                          
                          return old.map((n: any) => 
                            n.status === 'unread' ? { ...n, status: 'read', readAt: new Date() } : n
                          );
                        });
                        
                        // Update unread count optimistically to zero
                        queryClient.setQueryData(['/api/notifications/count'], { count: 0 });
                        
                        markAllAsReadMutation.mutate(undefined, {
                          onSuccess: () => {
                            console.log("Mark all as read success");
                            // Only invalidate the queries, don't force refetch which may cause UI issues
                            queryClient.invalidateQueries({ queryKey: ['/api/notifications/count'] });
                            queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
                            // Skip forcing refetch which may cause UI to reset
                            // refetchNotifications();
                            // refetchUnreadCount();
                            toast({
                              title: "Success",
                              description: "All notifications marked as read",
                            });
                          },
                          onError: (error) => {
                            console.error("Mark all as read error:", error);
                            toast({
                              title: "Error",
                              description: "Failed to mark notifications as read",
                              variant: "destructive"
                            });
                          }
                        });
                      } catch (error) {
                        console.error("Mark all as read exception:", error);
                      }
                    }}
                    disabled={markAllAsReadMutation.isPending}
                  >
                    {markAllAsReadMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    )}
                    Mark all as read
                  </Button>
                )}
              </CardHeader>
              <Separator />
              <CardContent className="p-0">
                <ScrollArea className="h-[calc(70vh-5rem)]">
                  {isLoadingNotifications ? (
                    <div className="flex items-center justify-center h-full">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : notifications && notifications.length > 0 ? (
                    <div className="divide-y">
                      {notifications.map((notification: any) => (
                        <div 
                          key={notification.id}
                          className={`p-4 ${notification.status === 'unread' ? 'bg-muted/30' : ''}`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3">
                              <div className={`rounded-full p-2 ${notification.status === 'unread' ? 'bg-primary/10' : 'bg-muted/50'}`}>
                                {notification.type === 'message' ? (
                                  <MessageSquare className="h-4 w-4 text-primary" />
                                ) : notification.type === 'assessment' ? (
                                  <CheckCircle className="h-4 w-4 text-primary" />
                                ) : (
                                  <Bell className="h-4 w-4 text-primary" />
                                )}
                              </div>
                              <div>
                                <h3 className="font-medium">{notification.title}</h3>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {/* Content is cleaned at the hook level */}
                                  {notification.content}
                                </p>
                                <p className="text-xs text-muted-foreground mt-2">
                                  {notification.createdAt && formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                                </p>
                              </div>
                            </div>
                            {notification.status === 'unread' && (
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="h-8"
                                onClick={() => {
                                  console.log("Mark as read clicked for notification:", notification.id);
                                  try {
                                    // Optimistically mark notification as read in the UI
                                    const oldNotifications = queryClient.getQueryData(['/api/notifications']);
                                    queryClient.setQueryData(['/api/notifications'], (old: any[]) => {
                                      if (!old) return old;
                                      
                                      return old.map((n: any) => 
                                        n.id === notification.id ? { ...n, status: 'read', readAt: new Date() } : n
                                      );
                                    });
                                    
                                    // Update unread count optimistically
                                    const oldCount = queryClient.getQueryData(['/api/notifications/count']);
                                    if (typeof oldCount === 'object' && oldCount !== null && 'count' in oldCount) {
                                      const newCount = Math.max(0, (oldCount as any).count - 1);
                                      queryClient.setQueryData(['/api/notifications/count'], { count: newCount });
                                    }
                                    
                                    markAsReadMutation.mutate(notification.id, {
                                      onSuccess: () => {
                                        console.log("Mark as read success for notification:", notification.id);
                                        // Only invalidate the queries, don't force refetch which may cause UI issues
                                        queryClient.invalidateQueries({ queryKey: ['/api/notifications/count'] });
                                        queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
                                        // Skip forcing refetch which may cause UI to reset
                                        // refetchNotifications();
                                        // refetchUnreadCount();
                                        toast({
                                          title: "Success",
                                          description: "Notification marked as read",
                                        });
                                      },
                                      onError: (error) => {
                                        console.error("Mark as read error:", error);
                                        toast({
                                          title: "Error",
                                          description: "Failed to mark notification as read",
                                          variant: "destructive"
                                        });
                                      }
                                    });
                                  } catch (error) {
                                    console.error("Mark as read exception:", error);
                                  }
                                }}
                                disabled={markAsReadMutation.isPending}
                              >
                                {markAsReadMutation.isPending && markAsReadMutation.variables === notification.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                                ) : (
                                  <CheckCircle className="h-4 w-4 mr-1" />
                                )}
                                Mark as read
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">No notifications</h3>
                        <p className="text-muted-foreground">You don't have any notifications yet</p>
                      </div>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="settings" className="mt-0">
            <Card className="h-[70vh]">
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Customize how and when you receive notifications
                </CardDescription>
              </CardHeader>
              <Separator />
              <CardContent className="py-6">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Email Notifications</h3>
                    <div className="space-y-4">
                      {/* Email notification settings would go here */}
                      <p className="text-muted-foreground">Email notification settings will be available soon.</p>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-4">In-App Notifications</h3>
                    <div className="space-y-4">
                      {/* In-app notification settings would go here */}
                      <p className="text-muted-foreground">In-app notification settings will be available soon.</p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <Separator />
              <CardFooter className="py-4">
                <Button variant="outline" className="w-full">
                  Save Settings
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
        
        {/* New Conversation Dialog */}
        <Dialog open={isNewConversationDialogOpen} onOpenChange={setIsNewConversationDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>New Conversation</DialogTitle>
              <DialogDescription>
                Create a new conversation with one or more participants
              </DialogDescription>
            </DialogHeader>
            
            <Form {...createConversationForm}>
              <form onSubmit={createConversationForm.handleSubmit(handleCreateConversation)} className="space-y-6">
                <FormField
                  control={createConversationForm.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Conversation Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter a title for the conversation" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={createConversationForm.control}
                  name="participantIds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Participants</FormLabel>
                      <FormControl>
                        <UserSelect
                          selectedUsers={field.value}
                          onSelect={(userId) => {
                            const currentIds = [...field.value];
                            field.onChange([...currentIds, userId]);
                          }}
                          onRemove={(userId) => {
                            const currentIds = [...field.value];
                            field.onChange(currentIds.filter(id => id !== userId));
                          }}
                          disabled={createConversationMutation.isPending}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsNewConversationDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={createConversationMutation.isPending || !createConversationForm.formState.isValid}
                  >
                    {createConversationMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Conversation"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  );
}