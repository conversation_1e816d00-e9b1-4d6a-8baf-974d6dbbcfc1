import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { apiRequest } from "@/lib/queryClient";
import { Link, useLocation } from "wouter";
import { AlertCircle, ArrowLeft, CheckCircle } from "lucide-react";
import CSRFToken from "@/components/csrf-token";

export default function ForgotPasswordPage() {
  const [, setLocation] = useLocation();
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [csrfToken, setCsrfToken] = useState<string | null>(null);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast({
        title: "Email Required",
        description: "Please enter your email address.",
        variant: "destructive"
      });
      return;
    }
    
    if (!csrfToken) {
      toast({
        title: "Security Error",
        description: "Please try again in a moment. The page is still initializing.",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    setErrorMessage("");
    
    try {
      const response = await apiRequest("POST", "/api/forgot-password", { 
        email,
        _csrf: csrfToken
      });
      const data = await response.json();
      
      if (data.success) {
        setIsSuccess(true);
      } else {
        setErrorMessage(data.message || "Failed to process request. Please try again.");
      }
    } catch (error) {
      console.error("Forgot password error:", error);
      setErrorMessage(error instanceof Error ? error.message : "An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-muted/20">
      <div className="w-full max-w-md px-4">
        {/* NeuroElevate Logo and Branding */}
        <div className="text-center mb-4">
          <h1 className="text-3xl font-bold text-primary mb-0">NeuroElevate</h1>
          <p className="text-muted-foreground text-sm">Assessment Platform</p>
        </div>

        <Card className="w-full">
          <CardHeader className="space-y-1">
            <Button 
              variant="ghost" 
              size="sm" 
              className="w-fit h-fit p-0 absolute left-4 top-4"
              onClick={() => setLocation("/auth")}
            >
              <ArrowLeft className="h-5 w-5 mr-1" />
              <span className="text-sm">Back</span>
            </Button>
            
            <div className="pt-6">
              <CardTitle className="text-2xl font-bold text-center">Forgot Password</CardTitle>
              <CardDescription className="text-center">
                {!isSuccess 
                  ? "Enter your email address to receive a password reset link" 
                  : "Password reset link sent. Check your email"}
              </CardDescription>
            </div>
          </CardHeader>
          
          <CardContent>
            {isSuccess ? (
              <div className="flex flex-col items-center justify-center space-y-4 text-center py-4">
                <CheckCircle className="h-12 w-12 text-primary" />
                <div className="space-y-2">
                  <h3 className="font-medium">Reset Link Sent</h3>
                  <p className="text-sm text-muted-foreground">
                    We've sent a password reset link to <strong>{email}</strong>. 
                    Please check your email and follow the instructions to reset your password.
                  </p>
                  <p className="text-xs text-muted-foreground mt-4">
                    If you don't see the email in your inbox, check your spam folder. 
                    The link will expire in 1 hour.
                  </p>
                  <Button
                    className="mt-4"
                    onClick={() => setLocation("/auth")}
                  >
                    Return to Login
                  </Button>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Fetch CSRF token */}
                <CSRFToken onTokenReceived={(token) => setCsrfToken(token)} />
                
                {errorMessage && (
                  <div className="flex items-center p-3 text-sm border rounded-md bg-destructive/10 border-destructive/30 text-destructive">
                    <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                    <p>{errorMessage}</p>
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    required
                  />
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isLoading || !csrfToken}
                >
                  {isLoading ? "Sending Request..." : "Send Reset Link"}
                </Button>
              </form>
            )}
          </CardContent>
          
          {!isSuccess && (
            <CardFooter className="flex justify-center">
              <div className="text-sm text-muted-foreground">
                <Link href="/auth" className="hover:text-primary underline underline-offset-4">
                  Return to login
                </Link>
              </div>
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  );
}