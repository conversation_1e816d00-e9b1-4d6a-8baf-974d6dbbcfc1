import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardT<PERSON><PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Link, useLocation } from "wouter";

export default function TestResetTokenPage() {
  const [, setLocation] = useLocation();
  const [tokenData, setTokenData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const generateTestToken = async () => {
    setIsLoading(true);
    try {
      const response = await apiRequest("GET", "/api/test-reset-token");
      const data = await response.json();
      console.log("Generated test token data:", data);
      setTokenData(data);
      
      if (data.success) {
        toast({
          title: "Token generated successfully",
          description: "You can now click the reset URL to test the password reset flow",
        });
      } else {
        toast({
          title: "Error generating token",
          description: data.message,
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error generating test token:", error);
      toast({
        title: "Error generating token",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const navigateToResetUrl = () => {
    if (tokenData?.resetUrl) {
      // Extract the path part from the full URL
      const url = new URL(tokenData.resetUrl);
      setLocation(url.pathname);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-muted/20 p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="border-b pb-3">
          <CardTitle className="text-2xl font-bold">Test Password Reset Flow</CardTitle>
        </CardHeader>
        
        <CardContent className="pt-6 space-y-6">
          <p className="text-sm text-muted-foreground">
            This page lets you generate a test password reset token to verify the password reset functionality.
          </p>
          
          <Button
            className="w-full bg-primary"
            onClick={generateTestToken}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Generating Token...
              </>
            ) : (
              "Generate Test Reset Token"
            )}
          </Button>
          
          {tokenData && (
            <div className="mt-6 p-4 bg-muted rounded-md">
              <h3 className="font-semibold mb-2">Generated Token Information:</h3>
              <div className="space-y-2 text-xs font-mono overflow-x-auto">
                <p>User ID: {tokenData.userId}</p>
                <p>Token: {tokenData.token ? tokenData.token.substring(0, 8) + "..." : "N/A"}</p>
                <p className="break-all">Reset URL: {tokenData.resetUrl || "N/A"}</p>
              </div>
              
              <Button 
                className="w-full mt-4"
                onClick={navigateToResetUrl}
                disabled={!tokenData?.resetUrl}
              >
                Navigate to Reset Password Page
              </Button>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="border-t pt-4 flex justify-between">
          <Link href="/">
            <Button variant="outline">Return to Dashboard</Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}