import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { apiRequest } from "@/lib/queryClient";
import { Link, useLocation } from "wouter";
import { CheckCircle, XCircle } from "lucide-react";
import CSRFToken from "@/components/csrf-token";

export default function ResetPasswordPage() {
  const [, setLocation] = useLocation();
  
  console.log("⭐ RESET PASSWORD PAGE LOADED ⭐");
  console.log("Current URL:", window.location.href);
  
  // Extract token directly from URL path
  const pathSegments = window.location.pathname.split('/');
  let token: string | null = null;
  
  if (pathSegments.length >= 3 && pathSegments[1] === 'reset-password') {
    token = pathSegments[2];
    console.log("🔑 TOKEN FOUND from URL path");
  } else {
    // Fallback to query parameter if available
    token = new URLSearchParams(window.location.search).get("token");
    console.log("Token from query parameter:", token ? "Found" : "Not found");
  }
  
  // For debugging purposes - log a truncated version of the token
  if (token) {
    console.log("Token (first 8 chars only):", token.substring(0, 8) + "...");
    
    // If you're landing on this page directly from the reset link,
    // log additional information to debug the flow
    console.log("Source URL:", document.referrer || "Direct navigation");
  } else {
    console.error("❌ ERROR: No token found in URL");
    
    // Add a special manual override for testing with hardcoded token
    if (window.location.pathname === '/reset-password/test') {
      console.log("🧪 TEST MODE: Using test token");
      token = "test-token-for-debugging";
    }
  }
  
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [isTokenChecking, setIsTokenChecking] = useState(true);
  const [isResetSuccessful, setIsResetSuccessful] = useState(false);
  const [csrfToken, setCsrfToken] = useState<string | null>(null);
  const { toast } = useToast();
  
  useEffect(() => {
    if (!token) {
      console.error("No token found in URL. Current URL:", window.location.href);
    }
  }, [token]);

  // Verify token validity when component mounts
  useEffect(() => {
    async function verifyToken() {
      if (!token) {
        setIsTokenChecking(false);
        return;
      }

      try {
        const response = await apiRequest("GET", `/api/verify-reset-token/${token}`);
        const data = await response.json();
        setIsTokenValid(data.success);
      } catch (error) {
        console.error("Error verifying token:", error);
        setIsTokenValid(false);
      } finally {
        setIsTokenChecking(false);
      }
    }

    verifyToken();
  }, [token]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) {
      toast({
        title: "Invalid Request",
        description: "Password reset token is missing.",
        variant: "destructive"
      });
      return;
    }
    
    if (newPassword.length < 8) {
      toast({
        title: "Password Too Short",
        description: "Password must be at least 8 characters long.",
        variant: "destructive"
      });
      return;
    }
    
    if (newPassword !== confirmPassword) {
      toast({
        title: "Passwords Don't Match",
        description: "Please make sure both passwords match.",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Include the CSRF token in the request
      const response = await apiRequest("POST", "/api/reset-password", { 
        token,
        newPassword,
        _csrf: csrfToken
      });
      
      const data = await response.json();
      
      if (data.success) {
        setIsResetSuccessful(true);
      } else {
        toast({
          title: "Password Reset Failed",
          description: data.message,
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Password reset error:", error);
      toast({
        title: "An error occurred",
        description: error instanceof Error ? error.message : "Failed to reset password",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isTokenChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-muted/20">
        <div className="flex flex-col items-center">
          <div className="h-8 w-8 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-sm text-muted-foreground">Verifying reset token...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-white pt-0">
      <div className="w-full max-w-md px-4 mt-0">
        <div className="text-center mb-2">
          <h1 className="text-2xl font-bold text-primary mb-0">NeuroElevate</h1>
          <p className="text-muted-foreground text-sm">Assessment Platform</p>
        </div>
        <Card className="w-full shadow-lg">
          <CardHeader className="space-y-0 border-b pb-2 pt-4">
            <CardTitle className="text-xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">Reset Password</CardTitle>
            <CardDescription className="text-sm">
              {!isTokenValid && "Invalid or expired password reset token."}
              {isTokenValid && !isResetSuccessful && "Create a new password for your account."}
              {isResetSuccessful && "Your password has been successfully reset."}
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {!isTokenValid && (
              <div className="flex flex-col items-center justify-center space-y-4 text-center pt-4 pb-6">
                <div className="h-16 w-16 rounded-full bg-red-50 flex items-center justify-center">
                  <XCircle className="h-10 w-10 text-destructive" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-destructive">Invalid Reset Link</h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    This password reset link is invalid or has expired. 
                    Please request a new password reset link.
                  </p>
                  <Button
                    className="mt-2 bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-700"
                    onClick={() => setLocation("/forgot-password")}
                  >
                    Request New Link
                  </Button>
                </div>
              </div>
            )}
            
            {isTokenValid && isResetSuccessful && (
              <div className="flex flex-col items-center justify-center space-y-4 text-center pt-4 pb-6">
                <div className="h-16 w-16 rounded-full bg-green-50 flex items-center justify-center">
                  <CheckCircle className="h-10 w-10 text-green-500" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-green-600">Password Reset Successful</h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    Your password has been reset successfully. You can now log in with your new password.
                  </p>
                  <Button
                    className="mt-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
                    onClick={() => setLocation("/auth")}
                  >
                    Go to Login
                  </Button>
                </div>
              </div>
            )}
            
            {isTokenValid && !isResetSuccessful && (
              <form onSubmit={handleSubmit} className="space-y-3 pt-2">
                {/* Fetch and include CSRF token */}
                <CSRFToken onTokenReceived={(token) => setCsrfToken(token)} />
                
                <div className="space-y-1">
                  <Label htmlFor="new-password" className="font-medium text-sm">New Password</Label>
                  <Input
                    id="new-password"
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="Enter your new password"
                    required
                    minLength={8}
                    className="border-primary/20 focus:border-primary h-9"
                  />
                  <p className="text-xs text-muted-foreground mt-0.5">
                    Password must be at least 8 characters long.
                  </p>
                </div>
                
                <div className="space-y-1">
                  <Label htmlFor="confirm-password" className="font-medium text-sm">Confirm Password</Label>
                  <Input
                    id="confirm-password"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm your new password"
                    required
                    className="border-primary/20 focus:border-primary h-9"
                  />
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full mt-4 bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-700" 
                  disabled={isLoading || !csrfToken}
                >
                  {isLoading ? (
                    <>
                      <div className="h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Resetting Password...
                    </>
                  ) : (
                    "Reset Password"
                  )}
                </Button>
              </form>
            )}
          </CardContent>
          
          {/* No footer needed */}
        </Card>
      </div>
    </div>
  );
}