import { useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { apiRequest } from "@/lib/queryClient";
import { Spinner } from "@/components/ui/spinner";
import { CheckCircle, Save } from "lucide-react";

const testFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  age: z.string().min(1, "Age is required"),
  previousAssessments: z.string().optional(),
  learningChallenges: z.string().min(1, "This field is required"),
  additionalInfo: z.string().optional(),
});

type TestFormValues = z.infer<typeof testFormSchema>;

export function TestFormPage() {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  // Create form
  const form = useForm<TestFormValues>({
    resolver: zodResolver(testFormSchema),
    defaultValues: {
      name: "",
      age: "",
      previousAssessments: "",
      learningChallenges: "",
      additionalInfo: "",
    },
  });

  async function onSubmit(data: TestFormValues) {
    setIsSubmitting(true);
    
    try {
      console.log("Submitting form data:", data);
      
      // Send the data to our test endpoint
      const response = await apiRequest("POST", "/api/test-form-submission", data);
      
      if (!response.ok) {
        throw new Error("Failed to submit form");
      }
      
      // Show success state
      setIsSuccess(true);
      toast({
        title: "Form submitted successfully",
        description: "Thank you for submitting the test form.",
      });
      
    } catch (error) {
      console.error("Form submission error:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit form",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isSuccess) {
    return (
      <div className="container max-w-2xl py-12">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center text-xl">
              <CheckCircle className="mr-2 h-6 w-6 text-green-600" />
              Form Submitted Successfully
            </CardTitle>
            <CardDescription>
              Thank you for completing the test form. Your responses have been recorded.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground">
              This is a test form to verify data is properly saved to the database.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container max-w-2xl py-12">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Test Pre-Assessment Form</CardTitle>
          <CardDescription>
            This is a test form to verify database connectivity. All fields marked with * are required.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name *</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter your full name" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="age"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Age *</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter your age" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Separator />

                <FormField
                  control={form.control}
                  name="previousAssessments"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Previous SpLD Assessments</FormLabel>
                      <FormDescription>
                        Please describe any previous assessments you've had
                      </FormDescription>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Describe any previous assessments"
                          rows={3}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="learningChallenges"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Learning Challenges *</FormLabel>
                      <FormDescription>
                        Please describe any learning challenges you experience
                      </FormDescription>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Describe your learning challenges"
                          rows={4}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="additionalInfo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Information</FormLabel>
                      <FormDescription>
                        Any other information you'd like to share
                      </FormDescription>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Any other information"
                          rows={3}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <CardFooter className="flex justify-end px-0 pt-4">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Spinner className="mr-2" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Submit Form
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}

export default TestFormPage;