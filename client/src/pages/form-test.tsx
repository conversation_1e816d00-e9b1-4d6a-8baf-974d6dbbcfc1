import React, { useState, useEffect } from 'react';
import AppLayout from '@/components/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';

export default function FormTestPage() {
  const { toast } = useToast();
  const [currentSection, setCurrentSection] = useState(() => {
    // Try to get saved section from localStorage
    const savedSection = localStorage.getItem('test-form-section');
    return savedSection ? parseInt(savedSection) : 1;
  });
  const totalSections = 5;
  
  // Use useEffect to log when the section changes
  useEffect(() => {
    console.log(`Current section in effect: ${currentSection}`);
  }, [currentSection]);
  
  const goToNextSection = () => {
    if (currentSection < totalSections) {
      try {
        // Save current section to localStorage
        const nextSection = currentSection + 1;
        console.log(`Moving from section ${currentSection} to section ${nextSection}`);
        localStorage.setItem('test-form-section', nextSection.toString());
        
        // Update state to move to next section
        console.log(`Setting current section to ${nextSection}`);
        setCurrentSection(nextSection);
        
        // Double check the section is set correctly - debugging only
        setTimeout(() => {
          console.log(`After state update, currentSection should be ${nextSection}, actually: ${currentSection}`);
          const storedSection = localStorage.getItem('test-form-section');
          console.log(`Stored in localStorage: ${storedSection}`);
        }, 50);
      } catch (error) {
        console.error("Error in goToNextSection:", error);
      }
    }
  };
  
  const goToPreviousSection = () => {
    if (currentSection > 1) {
      try {
        // Save current section to localStorage
        const prevSection = currentSection - 1;
        console.log(`Moving from section ${currentSection} to section ${prevSection}`);
        localStorage.setItem('test-form-section', prevSection.toString());
        
        // Update state to move to previous section
        console.log(`Setting current section to ${prevSection}`);
        setCurrentSection(prevSection);
        
        // Double check the section is set correctly - debugging only
        setTimeout(() => {
          console.log(`After state update, currentSection should be ${prevSection}, actually: ${currentSection}`);
          const storedSection = localStorage.getItem('test-form-section');
          console.log(`Stored in localStorage: ${storedSection}`);
        }, 50);
      } catch (error) {
        console.error("Error in goToPreviousSection:", error);
      }
    }
  };
  
  const handleSave = () => {
    try {
      console.log(`Saving section: ${currentSection}`);
      localStorage.setItem('test-form-section', currentSection.toString());
      
      // Verify that the section was correctly saved
      setTimeout(() => {
        const storedSection = localStorage.getItem('test-form-section');
        console.log(`Stored section in localStorage after save: ${storedSection}`);
      }, 50);
      
      toast({
        title: "Saved",
        description: `Form saved at section ${currentSection}.`,
      });
    } catch (error) {
      console.error("Error in handleSave:", error);
      toast({
        title: "Error saving",
        description: "There was a problem saving your progress.",
        variant: "destructive"
      });
    }
  };
  
  const handleSubmit = () => {
    try {
      console.log(`Submitting from section: ${currentSection}`);
      
      // Clear localStorage data after successful submission
      localStorage.removeItem('test-form-section');
      
      toast({
        title: "Submitted",
        description: "Form submitted successfully.",
      });
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      toast({
        title: "Error submitting",
        description: "There was a problem submitting your form.",
        variant: "destructive"
      });
    }
  };
  
  const renderFormSection = () => {
    console.log(`Rendering form section ${currentSection}`);
    return (
      <Card>
        <CardHeader>
          <CardTitle>Form Section {currentSection}</CardTitle>
          <CardDescription>
            This is section {currentSection} of the test form
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>Test content for section {currentSection}</p>
          <p className="pt-4">Notice whether the correct section is showing and if navigation works properly.</p>
        </CardContent>
      </Card>
    );
  };
  
  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Form Navigation Test</h1>
        
        <div className="mb-6">
          <p className="mb-2">Current section: {currentSection}</p>
          <Progress value={(currentSection / totalSections) * 100} className="mb-2" />
          
          <div className="flex items-center">
            {Array.from({ length: totalSections }).map((_, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className={`rounded-full transition-all duration-500 ease-in-out h-12 w-12 flex items-center justify-center py-3 border-2 ${
                    currentSection > index + 1
                      ? "bg-primary border-primary text-white"
                      : currentSection === index + 1
                      ? "border-primary text-primary"
                      : "border-gray-300 text-gray-500"
                  }`}
                >
                  {index + 1}
                </div>
                {index < totalSections - 1 && (
                  <div 
                    className={`flex-auto border-t-2 transition-all duration-500 ease-in-out ${
                      currentSection > index + 1 ? "border-primary" : "border-gray-300"
                    }`}
                  ></div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        {renderFormSection()}
        
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={handleSave}
          >
            Save
          </Button>
          
          <div className="space-x-2">
            {currentSection > 1 && (
              <Button
                type="button"
                variant="outline"
                onClick={goToPreviousSection}
              >
                Previous
              </Button>
            )}
            
            {currentSection < totalSections ? (
              <Button
                type="button"
                onClick={goToNextSection}
              >
                Next
              </Button>
            ) : (
              <Button
                type="button"
                onClick={handleSubmit}
              >
                Submit
              </Button>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}