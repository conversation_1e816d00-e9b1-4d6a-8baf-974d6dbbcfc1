import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { User, insertAssesseeSchema } from "@shared/schema";
import { useLocation } from "wouter";
import AppLayout from "../components/layouts/app-layout";
import { ArrowLeft, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON> } from "wouter";
import { DatePicker } from "@/components/ui/date-picker";

// Define a client-side validation schema
const addAssesseeSchema = z.object({
  fullName: z.string().min(2, "Full name is required"),
  dateOfBirth: z.date({
    required_error: "Date of birth is required",
  }),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(5, "Phone number must be at least 5 characters"),
  address: z.string().min(5, "Address must be at least 5 characters"),
  parentId: z.number().optional(),
  userId: z.number().optional(),
  schoolId: z.number().optional(),
});

type AddAssesseeFormData = z.infer<typeof addAssesseeSchema>;

interface AddAssesseePageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function AddAssesseePage({ user, logout }: AddAssesseePageProps = {}) {
  const { toast } = useToast();
  const [_, navigate] = useLocation();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<AddAssesseeFormData>({
    resolver: zodResolver(addAssesseeSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      address: "",
      parentId: user?.role === "parent" ? user.id : undefined,
      userId: undefined // Will be set if assessee is over 16 and gets a user account
    },
  });

  const mutation = useMutation({
    mutationFn: async (data: AddAssesseeFormData) => {
      const response = await apiRequest("POST", "/api/assessees", data);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to add assessee");
      }
      
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Assessee added successfully",
        description: "You can now create an assessment for this assessee"
      });
      queryClient.invalidateQueries({ queryKey: ["/api/assessees"] });
      navigate("/assessments");
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to add assessee",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const onSubmit = async (data: AddAssesseeFormData) => {
    try {
      setIsSubmitting(true);
      // Format the date as an ISO string for the server
      const formattedData = {
        ...data,
        dateOfBirth: data.dateOfBirth.toISOString()
      };
      await mutation.mutateAsync(formattedData as any);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Link href="/">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Add New Assessee</h1>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Assessee Details</CardTitle>
            <CardDescription>
              Enter the details of the person who will be assessed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Full name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="dateOfBirth"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Date of Birth</FormLabel>
                        <FormControl>
                          <DatePicker
                            date={field.value}
                            setDate={field.onChange}
                            isBirthDate={true}
                          />
                        </FormControl>
                        <FormDescription>
                          The date of birth is used to determine which forms are required
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Email address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Phone number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input placeholder="Address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <Button type="submit" disabled={isSubmitting} className="w-full">
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Adding Assessee...
                    </>
                  ) : (
                    "Add Assessee"
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}