import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import AppLayout from "../components/layouts/app-layout";
import { User, type Assessee } from "@shared/schema";
import type { AssessmentData } from "../types";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "wouter";
import { Users, Plus } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { DashboardProvider } from "../components/dashboard/widget-context";
import DashboardGrid from "../components/dashboard/dashboard-grid";

// Dashboard page is now a self-contained component
interface DashboardPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function DashboardPage({ user, logout }: DashboardPageProps = {}) {
  // For university users - we need their referrals
  const { data: assessments = [] } = useQuery<AssessmentData[]>({
    queryKey: ["/api/assessments"],
    enabled: !!user && user.role === 'university',
    refetchOnWindowFocus: true,
    staleTime: 30000 // 30 seconds
  });

  const { data: assessees = [] } = useQuery<Assessee[]>({
    queryKey: ["/api/assessees"],
    enabled: !!user && user.role === 'university',
    refetchOnWindowFocus: true,
    staleTime: 30000 // 30 seconds
  });

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">Welcome back, {user?.fullName}</p>
          </div>
          {(user?.role === 'admin' || user?.role === 'assessor') && (
            <Link href="/assessments/new">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Assessment
              </Button>
            </Link>
          )}
          {user?.role === 'university' && (
            <Link href="/university/refer">
              <Button>
                <Users className="mr-2 h-4 w-4" />
                Refer a Student
              </Button>
            </Link>
          )}
        </div>
        
        {user?.role === 'university' && (
          <Card>
            <CardHeader>
              <CardTitle>Your Student Referrals</CardTitle>
              <CardDescription>Students you've referred for assessment</CardDescription>
            </CardHeader>
            <CardContent>
              {Array.isArray(assessments) && assessments.length > 0 ? (
                <div className="rounded-md border">
                  <table className="min-w-full divide-y divide-border">
                    <thead>
                      <tr className="bg-muted/50">
                        <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Student Name</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Date Referred</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Status</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      {assessments.map((assessment: AssessmentData) => {
                        // Use either the assessee data from the assessment object or look it up in the assessees array
                        const assessee = assessment.assessee || assessees.find((a: Assessee) => a.id === assessment.assesseeId);
                        return (
                          <tr key={assessment.id}>
                            <td className="px-4 py-3 text-sm">{assessee?.fullName || 'Unknown'}</td>
                            <td className="px-4 py-3 text-sm">{assessment.createdAt ? new Date(assessment.createdAt).toLocaleDateString() : 'N/A'}</td>
                            <td className="px-4 py-3 text-sm">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                assessment.status === 'VerificationPending' ? 'bg-blue-100 text-blue-800' :
                                assessment.status === 'booked' ? 'bg-green-100 text-green-800' : 
                                assessment.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' : 
                                assessment.status === 'completed' ? 'bg-purple-100 text-purple-800' : 
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {assessment.status?.replace('_', ' ') || 'Unknown'}
                              </span>
                            </td>
                            <td className="px-4 py-3 text-sm">
                              <Button variant="outline" size="sm" asChild>
                                <Link href={`/assessments/${assessment.id}`}>View Details</Link>
                              </Button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">No student referrals found</p>
                  <p className="text-xs text-muted-foreground mt-1">Use the "Refer a Student" button to create a new referral</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
        
        {/* Dashboard with customizable widgets */}
        <DashboardProvider user={user}>
          <DashboardGrid user={user} />
        </DashboardProvider>
      </div>
    </AppLayout>
  );
}