import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";

export default function NotFound() {
  const [isResetPasswordPath, setIsResetPasswordPath] = useState<boolean>(false);
  const [pathInfo, setPathInfo] = useState<{
    path: string;
    segments: string[];
    queryParams: Record<string, string>;
  }>({
    path: "",
    segments: [],
    queryParams: {}
  });
  const [, setLocation] = useLocation();

  useEffect(() => {
    // Check if this is a reset password URL
    const path = window.location.pathname;
    const search = window.location.search;
    const segments = path.split('/').filter(s => s);
    
    setPathInfo({
      path: path,
      segments: segments,
      queryParams: Object.fromEntries(new URLSearchParams(search).entries())
    });
    
    setIsResetPasswordPath(path.startsWith('/reset-password'));
    
    console.log("NotFound page loaded with path:", path);
    console.log("Path segments:", segments);
    console.log("Query parameters:", search);
    
    if (path.startsWith('/reset-password')) {
      // Log additional debug info
      console.log("This appears to be a reset password URL");
      
      // Extract token
      let token = null;
      
      // Check if it's in path
      if (segments.length >= 2 && segments[0] === 'reset-password') {
        token = segments[1];
        console.log("Token found in path:", token.substring(0, 8) + "... (truncated)");
      }
      
      // Check if it's in query string
      const params = new URLSearchParams(search);
      if (params.has('token')) {
        token = params.get('token');
        console.log("Token found in query string:", token?.substring(0, 8) + "... (truncated)");
      }
    }
  }, []);

  const redirectToResetPassword = () => {
    const token = pathInfo.segments.length >= 2 ? pathInfo.segments[1] : null;
    if (token) {
      setLocation(`/reset-password/${token}`);
    } else {
      setLocation("/reset-password");
    }
  };

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="pt-6">
          <div className="flex mb-4 gap-2">
            <AlertCircle className="h-8 w-8 text-red-500" />
            <h1 className="text-2xl font-bold text-gray-900">404 Page Not Found</h1>
          </div>

          <p className="mt-4 text-sm text-gray-600">
            {isResetPasswordPath ? 
              "This appears to be a password reset URL, but the page wasn't found." : 
              "The page you're looking for doesn't exist."}
          </p>
          
          {isResetPasswordPath && (
            <div className="mt-6 space-y-4">
              <div className="bg-blue-50 p-4 rounded text-xs font-mono overflow-auto">
                <h3 className="font-semibold mb-2">Debug Information:</h3>
                <p>Path: {pathInfo.path}</p>
                <p>Segments: {JSON.stringify(pathInfo.segments)}</p>
                <p>Query Params: {JSON.stringify(pathInfo.queryParams)}</p>
              </div>
              
              <Button 
                className="w-full" 
                onClick={redirectToResetPassword}
              >
                Try to Fix Reset Password Navigation
              </Button>
            </div>
          )}
          
          <div className="mt-6">
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setLocation("/")}
            >
              Return to Homepage
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
