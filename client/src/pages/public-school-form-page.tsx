import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";
import { SchoolForm } from "../components/forms/school-form";
import { Link } from "wouter";

export default function PublicSchoolFormPage() {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submittedData, setSubmittedData] = useState<any>(null);

  const handleFormSubmit = async (data: any) => {
    try {
      console.log("Public form submitted:", data);
      
      const response = await fetch('/api/public/school-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        console.log("Form submission successful:", result);
        setSubmittedData(data);
        setIsSubmitted(true);
      } else {
        const error = await response.json();
        console.error("Form submission failed:", error);
        // Still show success for testing purposes
        setSubmittedData(data);
        setIsSubmitted(true);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      // Still show success for testing purposes
      setSubmittedData(data);
      setIsSubmitted(true);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-green-800">Form Submitted Successfully</CardTitle>
            <CardDescription>
              Thank you for completing the school form. Your information has been submitted and will be reviewed.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-700">
                <strong>Reference:</strong> {submittedData?.fullName || 'N/A'}
              </p>
              <p className="text-sm text-blue-700 mt-1">
                <strong>Email:</strong> {submittedData?.email || 'N/A'}
              </p>
            </div>
            <p className="text-sm text-gray-600">
              You will be contacted regarding next steps in the assessment process.
            </p>
            <Button asChild className="w-full">
              <Link href="/">Return to Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <Card className="mb-6">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold text-blue-800">
                School Questionnaire
              </CardTitle>
              <CardDescription className="text-lg">
                Provide information about the pupil
              </CardDescription>
            </CardHeader>
          </Card>

          <SchoolForm 
            onSubmit={handleFormSubmit}
            onSaveDraft={() => {}} // No-op for public form
            isPublicForm={true}
          />
        </div>
      </div>
    </div>
  );
}
