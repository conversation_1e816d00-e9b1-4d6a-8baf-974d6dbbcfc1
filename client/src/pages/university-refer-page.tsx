import { useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { useToast } from "@/hooks/use-toast";
import AppLayout from "@/components/layouts/app-layout";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { DatePicker } from "@/components/ui/date-picker";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { User } from "@shared/schema";
import { CalendarIcon, Loader2, Send } from "lucide-react";

// Define the university referral schema
const universityReferSchema = z.object({
  assesseeFullName: z.string().min(1, "Assessee's name is required"),
  assesseeEmail: z.string().email("Please enter a valid email address"),
  assesseePhone: z.string().min(1, "Assessee's phone number is required"),
  dateOfBirth: z.date({
    required_error: "Date of birth is required",
  }).refine((date) => {
    if (!date) return false;
    const today = new Date();
    const age = today.getFullYear() - date.getFullYear() - 
               (today.getMonth() < date.getMonth() || 
               (today.getMonth() === date.getMonth() && today.getDate() < date.getDate()) ? 1 : 0);
    return age >= 16;
  }, "Student must be at least 16 years old for university referrals"),
  assesseeCourse: z.string().min(1, "Student's course is required"),
  assesseeYear: z.string().min(1, "Year of study is required"),
  reasonForReferral: z.string().min(1, "Reason for assessment is required"),
  previousAssessment: z.enum(["yes", "no"]),
  previousAssessmentDetails: z.string().optional(),
  additionalNotes: z.string().optional(),
});

type UniversityReferFormData = z.infer<typeof universityReferSchema>;

interface UniversityReferPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function UniversityReferPage({ user, logout }: UniversityReferPageProps = {}) {
  const { toast } = useToast();
  const [_, setLocation] = useLocation();
  
  // Initialize the form
  const form = useForm<UniversityReferFormData>({
    resolver: zodResolver(universityReferSchema),
    defaultValues: {
      assesseeFullName: "",
      assesseeEmail: "",
      assesseePhone: "",
      assesseeCourse: "",
      assesseeYear: "",
      reasonForReferral: "",
      previousAssessment: "no",
      previousAssessmentDetails: "",
      additionalNotes: "",
    },
  });

  // Mutation for submitting the university referral
  const referralMutation = useMutation({
    mutationFn: async (data: UniversityReferFormData) => {
      // First, create the assessee
      const assesseeData = {
        fullName: data.assesseeFullName,
        dateOfBirth: data.dateOfBirth,
        email: data.assesseeEmail,
        phone: data.assesseePhone,
        address: "", // We might want to add address fields later
        universityId: user?.id, // Link to the university
      };
      
      const assesseeRes = await apiRequest("POST", "/api/assessees", assesseeData);
      
      if (!assesseeRes.ok) {
        const errorData = await assesseeRes.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to create assessee record");
      }
      
      const assessee = await assesseeRes.json();
      
      // Then, create the assessment with university referral type
      const assessmentData = {
        assesseeId: assessee.id,
        assessorId: null, // Will be assigned later
        referringUserId: user?.id, // Critical for tracking who made the referral
        referralType: "university", // This field is critical - ensures the right payment flow
        status: "VerificationPending", // Using a valid status from the assessmentStatusEnum
        paymentStatus: "unpaid", // For university, this will follow the invoice path
        scheduledDate: null,
        additionalNotes: `University Course: ${data.assesseeCourse}\nYear of Study: ${data.assesseeYear}\nReason for Referral: ${data.reasonForReferral}\nPrevious Assessment: ${data.previousAssessment === "yes" ? "Yes" : "No"}\nPrevious Assessment Details: ${data.previousAssessmentDetails || "N/A"}\nAdditional Notes: ${data.additionalNotes || "N/A"}`,
      };
      
      const assessmentRes = await apiRequest("POST", "/api/assessments", assessmentData);
      
      if (!assessmentRes.ok) {
        const errorData = await assessmentRes.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to create assessment record");
      }
      
      return await assessmentRes.json();
    },
    onSuccess: () => {
      toast({
        title: "Student referral submitted",
        description: "The referral has been successfully submitted for processing",
      });
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["/api/assessments"] });
      queryClient.invalidateQueries({ queryKey: ["/api/assessees"] });
      queryClient.invalidateQueries({ queryKey: ["/api/activities/recent"] });
      queryClient.invalidateQueries({ queryKey: ["/api/dashboard"] });
      
      // Navigate back to dashboard
      setLocation("/");
    },
    onError: (error: Error) => {
      toast({
        title: "Referral submission failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = async (data: UniversityReferFormData) => {
    try {
      await referralMutation.mutateAsync(data);
    } catch (error) {
      console.error("Error in form submission:", error);
    }
  };

  // Wait until user data is available
  if (!user) {
    return null;
  }

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Refer a Student</h1>
          <p className="text-muted-foreground">
            Complete this form to refer a student for a SpLD assessment
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Student Referral Information</CardTitle>
            <CardDescription>
              Provide information about the student you're referring for an assessment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Assessee Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="assesseeFullName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Full Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Full name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="dateOfBirth"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Date of Birth</FormLabel>
                          <DatePicker
                            date={field.value}
                            setDate={field.onChange}
                            isBirthDate={true}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="assesseeEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address</FormLabel>
                          <FormControl>
                            <Input placeholder="Email address" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="assesseePhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <Input placeholder="Phone number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Academic Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="assesseeCourse"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Course/Degree Program</FormLabel>
                          <FormControl>
                            <Input placeholder="Course or degree program" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="assesseeYear"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Year of Study</FormLabel>
                          <FormControl>
                            <Input placeholder="E.g., 1st Year, 2nd Year, etc." {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Referral Information</h3>
                  
                  <FormField
                    control={form.control}
                    name="reasonForReferral"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reason for Referral</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Please provide details of why this student is being referred for assessment"
                            className="min-h-[100px]" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="previousAssessment"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Has the student previously had a SpLD assessment?</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex space-x-4"
                          >
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="yes" />
                              </FormControl>
                              <FormLabel className="font-normal">Yes</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="no" />
                              </FormControl>
                              <FormLabel className="font-normal">No</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {form.watch("previousAssessment") === "yes" && (
                    <FormField
                      control={form.control}
                      name="previousAssessmentDetails"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Previous Assessment Details</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="When and where was the assessment conducted? What was the outcome?"
                              className="min-h-[100px]" 
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                  
                  <FormField
                    control={form.control}
                    name="additionalNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Additional Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Any additional information that might be relevant for the assessment"
                            className="min-h-[100px]" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="flex justify-end">
                  <Button
                    type="submit"
                    disabled={referralMutation.isPending}
                    className="w-full md:w-auto"
                  >
                    {referralMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Send className="mr-2 h-4 w-4" />
                        Submit Referral
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}