import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export default function TestLoginPage() {
  const [username, setUsername] = useState("<EMAIL>");
  const [password, setPassword] = useState("password123");
  const { loginMutation, user, isLoading } = useAuth();
  const { toast } = useToast();

  // Show the current auth state
  const handleTestSession = async () => {
    try {
      const res = await fetch("/api/user");
      const data = await res.json();
      toast({
        title: "Session Test",
        description: `Status: ${res.status}, Authenticated: ${res.ok ? "Yes" : "No"}`,
      });
      console.log("Session test result:", { status: res.status, data });
    } catch (error) {
      toast({
        title: "Session Test Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    loginMutation.mutate(
      { username, password },
      {
        onSuccess: (data) => {
          console.log("Login successful:", data);
          toast({
            title: "Debug Login Success",
            description: `User ID: ${data.id}, Role: ${data.role}`,
          });
        },
        onError: (error) => {
          console.error("Login error:", error);
        },
      }
    );
  };

  return (
    <div className="container mx-auto py-10 max-w-md">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Test</CardTitle>
          <CardDescription>Test login functionality to diagnose issues</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : user ? (
            <div>
              <div className="mb-4 p-4 bg-green-50 rounded-md border border-green-200">
                <p className="text-green-600 font-medium">Currently authenticated</p>
                <p className="text-sm mt-1">User ID: {user.id}</p>
                <p className="text-sm">Role: {user.role}</p>
                <p className="text-sm">Email: {user.email}</p>
              </div>
              <Button variant="outline" onClick={handleTestSession} className="w-full">
                Test Session
              </Button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username/Email</Label>
                <Input
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="password123"
                />
              </div>
              <div className="pt-2">
                <Button 
                  type="submit" 
                  className="w-full"
                  disabled={loginMutation.isPending}
                >
                  {loginMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Logging in...
                    </>
                  ) : (
                    "Login"
                  )}
                </Button>
              </div>
              <Button type="button" variant="outline" onClick={handleTestSession} className="w-full">
                Test Current Session
              </Button>
            </form>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-xs text-muted-foreground">
            Session debugging tool
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}