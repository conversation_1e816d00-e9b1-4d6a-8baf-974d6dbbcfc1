import { useEffect, useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import CSRFToken from '@/components/csrf-token';

export default function EmailTestPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const { toast } = useToast();

  const handleTestEmail = async (testType: string) => {
    if (!email) {
      toast({
        title: "Email Required",
        description: "Please enter an email address",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiRequest('POST', '/api/admin/test-email', {
        email
      });
      
      const data = await response.json();
      setResults(data);
      
      toast({
        title: "Test Email Sent",
        description: "Check the results panel and your inbox",
      });
    } catch (error) {
      console.error('Error testing email:', error);
      toast({
        title: "Test Failed",
        description: error instanceof Error ? error.message : "Failed to send test email",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <CSRFToken onTokenReceived={() => {}} />
      <h1 className="text-3xl font-bold mb-6">Email Testing Center</h1>
      <p className="text-gray-500 mb-8">
        Use this page to test different email templates and diagnose delivery issues.
      </p>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Email Test Configuration</CardTitle>
          <CardDescription>Enter the email address to test with</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid w-full items-center gap-4">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="email">Email Address</Label>
              <Input 
                id="email" 
                placeholder="Enter email address" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                type="email"
              />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Tabs defaultValue="password-reset" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="password-reset">Password Reset</TabsTrigger>
          <TabsTrigger value="template">Template Email</TabsTrigger>
          <TabsTrigger value="direct">Direct HTML</TabsTrigger>
        </TabsList>
        
        <TabsContent value="password-reset" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Password Reset Email Test</CardTitle>
              <CardDescription>
                Tests the password reset email with template system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                This will send a test password reset email using the standard reset email template
                system. The link in the email will not be valid - this is just for testing the template.
              </p>
              <Button 
                onClick={() => handleTestEmail('password-reset')} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? "Sending..." : "Send Password Reset Test Email"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="template" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Email Test</CardTitle>
              <CardDescription>
                Tests a standard email with the template system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                This will send a test email using the regular email template system, but 
                without the password reset specific content. Helps identify if there's an
                issue with the template system or something specific to reset emails.
              </p>
              <Button 
                onClick={() => handleTestEmail('template')} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? "Sending..." : "Send Template Test Email"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="direct" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Direct HTML Email Test</CardTitle>
              <CardDescription>
                Tests direct HTML email without templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                This will send a test email using direct HTML content without the template
                system. Helps identify if there's an issue with SendGrid integration or 
                with the template system.
              </p>
              <Button 
                onClick={() => handleTestEmail('direct')} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? "Sending..." : "Send Direct HTML Test Email"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {results && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>
              Response from the email test
            </CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-md overflow-auto max-h-96">
              {JSON.stringify(results, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}