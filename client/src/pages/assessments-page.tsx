import React from 'react';
import AppLayout from '../components/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Search, FileCheck, Clock, Calendar } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Link } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { User } from '@shared/schema';
import type { AssessmentData } from '../types';

interface AssessmentsPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function AssessmentsPage({ user, logout }: AssessmentsPageProps = {}) {
  const [searchQuery, setSearchQuery] = React.useState('');

  const { data: assessments = [], isLoading } = useQuery<AssessmentData[]>({
    queryKey: ['/api/assessments'],
    enabled: !!user,
  });

  // Simple filtering for search functionality
  const filteredAssessments = searchQuery
    ? assessments.filter(assessment =>
        assessment.assessee?.fullName?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : assessments;

  // Status badge colors
  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { color: string; icon: React.ReactNode }> = {
      VerificationPending: { color: 'bg-blue-100 text-blue-800', icon: <FileCheck className="h-3.5 w-3.5" /> },
      pre_assessment: { color: 'bg-purple-100 text-purple-800', icon: <FileCheck className="h-3.5 w-3.5" /> },
      scheduled: { color: 'bg-yellow-100 text-yellow-800', icon: <Calendar className="h-3.5 w-3.5" /> },
      assessment_complete: { color: 'bg-green-100 text-green-800', icon: <FileCheck className="h-3.5 w-3.5" /> },
      report_writing: { color: 'bg-orange-100 text-orange-800', icon: <Clock className="h-3.5 w-3.5" /> },
      qa_review: { color: 'bg-pink-100 text-pink-800', icon: <FileCheck className="h-3.5 w-3.5" /> },
      completed: { color: 'bg-green-100 text-green-800', icon: <FileCheck className="h-3.5 w-3.5" /> },
    };

    const { color, icon } = statusMap[status] || { color: 'bg-gray-100 text-gray-800', icon: null };

    return (
      <span className={`inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium ${color}`}>
        {icon}
        {status.replace('_', ' ')}
      </span>
    );
  };

  // Format date to readable format
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not scheduled';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Assessments</h1>
          {['admin', 'assessor'].includes(user?.role || '') && (
            <Link href="/assessments/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Assessment
              </Button>
            </Link>
          )}
        </div>

        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search assessments..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="grid gap-4">
          {isLoading ? (
            <p>Loading assessments...</p>
          ) : Array.isArray(filteredAssessments) && filteredAssessments.length > 0 ? (
            filteredAssessments.map((assessment: AssessmentData) => (
              <Link key={assessment.id} href={`/assessments/${assessment.id}`}>
                <div className="cursor-pointer">
                  <Card className="hover:bg-gray-50 transition-colors">
                    <CardContent className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <p className="text-sm font-medium">{assessment.assessee?.fullName || 'Unknown Assessee'}</p>
                          <p className="text-xs text-muted-foreground">
                            DOB: {assessment.assessee?.dateOfBirth 
                              ? formatDate(assessment.assessee.dateOfBirth) 
                              : 'Unknown'}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Created</p>
                          <p className="text-xs text-muted-foreground">
                            {formatDate(assessment.createdAt)}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Status</p>
                          <div className="mt-1">
                            {getStatusBadge(assessment.status)}
                          </div>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Scheduled Date</p>
                          <p className="text-xs text-muted-foreground">
                            {formatDate(assessment.scheduledDate)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </Link>
            ))
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="mb-4">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mx-auto text-muted-foreground">
                    <path d="M16 4H8C6.34315 4 5 5.34315 5 7V19C5 20.6569 6.34315 22 8 22H16C17.6569 22 19 20.6569 19 19V7C19 5.34315 17.6569 4 16 4Z" stroke="currentColor" strokeWidth="2" />
                    <path d="M9 4V2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                    <path d="M15 4V2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                    <path d="M8 10H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                    <path d="M8 14H13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium mb-1">No Assessments Yet</h3>
                <p className="text-muted-foreground mb-4">There are no assessments in the system yet.</p>
                {['admin', 'assessor'].includes(user?.role || '') && (
                  <Link href="/assessments/new">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Assessment
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </AppLayout>
  );
}