import React, { useState, useEffect } from "react";
import { User } from "@shared/schema";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";
import { useLocation } from "wouter";
import AppLayout from "@/components/layouts/app-layout";
import { VerificationQueue } from "@/components/admin/fixed-verification-queue";

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Alert,
  AlertTitle,
  AlertDescription
} from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";

// Icons
import {
  Users,
  User as UserIcon,
  Shield,
  Mail,
  MoreHorizontal,
  RefreshCw,
  Search,
  Plus,
  ChevronDown,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Edit,
  Trash,
  Share,
  History,
  Key,
  UserPlus,
  Settings,
  LogOut,
  Check,
  X,
} from "lucide-react";

// Zod validation
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

// Types
type ActivityLog = {
  id: number;
  adminId: number;
  adminName: string;
  targetUserId: number;
  targetUserName: string;
  action: string;
  details: string;
  createdAt: string;
};

type InviteData = {
  email: string;
  role: string;
  fullName?: string;
  organization?: string;
  message?: string;
};

// Form schemas
const inviteUserSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  role: z.string().min(1, "Please select a role"),
  fullName: z.string().optional(),
  organization: z.string().optional(),
  message: z.string().optional(),
});

const editUserSchema = z.object({
  id: z.number(),
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Please enter a valid email address"),
  role: z.string().min(1, "Please select a role"),
  status: z.string().min(1, "Please select a status"),
});

export default function AdminPage({ user, logout }: { user: User; logout: () => Promise<void> }) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string | undefined>();
  const [statusFilter, setStatusFilter] = useState<string | undefined>();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isInviteOpen, setIsInviteOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [testEmail, setTestEmail] = useState("");
  // Use URL parameter to persist the selected tab
  const [_, setLocation] = useLocation();
  const params = new URLSearchParams(window.location.search);
  const tabParam = params.get('tab') || 'verification';
  const [activeTab, setActiveTab] = useState(tabParam);
  
  // Update URL when tab changes
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setLocation(`/admin?tab=${tab}`, { replace: true });
  };

  // Fetch all users
  const { data: users = [], isLoading, refetch } = useQuery({
    queryKey: ["/api/admin/users"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/admin/users");
      if (!response.ok) {
        throw new Error("Failed to fetch users");
      }
      return response.json();
    },
  });

  // Fetch activity logs
  const { data: activityLogs = [] } = useQuery({
    queryKey: ["/api/admin/activity-logs"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/admin/activity-logs");
      if (!response.ok) {
        throw new Error("Failed to fetch activity logs");
      }
      return response.json();
    },
  });

  // Mutations
  const updateUserMutation = useMutation({
    mutationFn: async (userData: any) => {
      const response = await apiRequest("PATCH", `/api/admin/users/${userData.id}`, userData);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update user");
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/activity-logs"] });
      setIsEditOpen(false);
      toast({
        title: "User updated",
        description: "The user has been updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update user",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const inviteUserMutation = useMutation({
    mutationFn: async (inviteData: InviteData) => {
      const response = await apiRequest("POST", "/api/admin/invite", inviteData);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to send invitation");
      }
      return response.json();
    },
    onSuccess: () => {
      setIsInviteOpen(false);
      toast({
        title: "Invitation sent",
        description: "The user invitation has been sent successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to send invitation",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Test email mutation
  const testEmailMutation = useMutation({
    mutationFn: async (email: string) => {
      if (!email || email.trim() === '') {
        throw new Error("Email address is required");
      }
      
      const response = await apiRequest("POST", "/api/admin/test-email", { email });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to send test email");
      }
      return response.json();
    },
    onSuccess: (data) => {
      setTestEmail(''); // Clear the input after successful send
      toast({
        title: "Test email sent",
        description: data.message || "The test email has been sent successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to send test email",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Forms
  const inviteForm = useForm<z.infer<typeof inviteUserSchema>>({
    resolver: zodResolver(inviteUserSchema),
    defaultValues: {
      email: "",
      role: "",
      fullName: "",
      organization: "",
      message: "",
    },
  });

  const editForm = useForm<z.infer<typeof editUserSchema>>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      id: 0,
      username: "",
      email: "",
      role: "",
      status: "",
    },
  });

  // Handlers
  const handleInviteSubmit = (data: z.infer<typeof inviteUserSchema>) => {
    inviteUserMutation.mutate(data);
  };

  const handleEditSubmit = (data: z.infer<typeof editUserSchema>) => {
    updateUserMutation.mutate(data);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    editForm.reset({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      status: user.status || "active",
    });
    setIsEditOpen(true);
  };

  // Get all assessors for dropdown selection
  const assessors = React.useMemo(() => {
    return users.filter((user: User) => user.role === 'assessor' && user.status === 'active');
  }, [users]);
  
  // Get pending approval users
  const pendingApprovalUsers = React.useMemo(() => {
    return users.filter((user: User) => 
      user.status === 'admin_approval_pending' || 
      (user.status === 'pending' && user.role !== 'admin')
    );
  }, [users]);

  // Handle quick user approval
  const handleQuickApprove = (user: User) => {
    updateUserMutation.mutate({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      status: 'active',
      sendApprovalEmail: true // Add this flag to trigger approval email
    }, {
      onSuccess: () => {
        // Force refresh of the users list after approval
        setTimeout(() => {
          refetch();
        }, 500);
        
        // Set the active tab to "users" to stay on the User Management tab
        handleTabChange("users");
        
        toast({
          title: "User Approved",
          description: `${user.fullName || user.username} has been approved and can now log in.`,
        });
      }
    });
  };

  // Handle quick user rejection
  const handleQuickReject = (user: User) => {
    updateUserMutation.mutate({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      status: 'inactive',
      sendRejectionEmail: true // Add this flag to trigger rejection email
    }, {
      onSuccess: () => {
        // Force refresh of the users list after rejection
        setTimeout(() => {
          refetch();
        }, 500);
        
        // Set the active tab to "users" to stay on the User Management tab
        handleTabChange("users");
        
        toast({
          title: "User Rejected",
          description: `${user.fullName || user.username} has been rejected.`,
        });
      }
    });
  };

  // Filter users with improved search capability
  const filteredUsers = users.filter((user: User) => {
    let matches = true;
    
    // Apply enhanced search - now searches across more fields and handles partial matches better
    if (searchTerm) {
      const term = searchTerm.toLowerCase().trim();
      
      // Split search terms for multi-word search support
      const searchTerms = term.split(/\s+/).filter(t => t.length > 0);
      
      if (searchTerms.length > 0) {
        // Check if all terms match at least one field
        matches = searchTerms.every(searchTerm => (
          (user.username?.toLowerCase().includes(searchTerm) || false) ||
          (user.email?.toLowerCase().includes(searchTerm) || false) ||
          (user.fullName?.toLowerCase().includes(searchTerm) || false) ||
          (user.organization?.toLowerCase().includes(searchTerm) || false) ||
          (user.role?.toLowerCase().includes(searchTerm) || false) ||
          (user.phone?.toLowerCase().includes(searchTerm) || false)
        ));
      }
    }
    
    // Apply role filter
    if (roleFilter) {
      matches = matches && user.role === roleFilter;
    }
    
    // Apply status filter
    if (statusFilter) {
      matches = matches && user.status === statusFilter;
    }
    
    return matches;
  });

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
      case "pending":
        return <Badge className="bg-yellow-500 hover:bg-yellow-600">Pending</Badge>;
      case "inactive":
        return <Badge variant="destructive">Inactive</Badge>;
      case "email_verification_pending":
        return <Badge variant="outline">Email Verification</Badge>;
      case "admin_approval_pending":
        return <Badge variant="outline">Admin Approval</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Function to navigate to the public university referral page in a new tab
  const openUniversityReferralPage = () => {
    window.open("/public-university-refer", "_blank");
  };
  
  // Function to navigate to the public individual referral page in a new tab
  const openIndividualReferralPage = () => {
    window.open("/public-individual-refer", "_blank");
  };

  // Function to navigate to the public assessee form in a new tab
  const openPublicAssesseeForm = () => {
    window.open("/public/assessee-form", "_blank");
  };

  // Function to navigate to the public parent form in a new tab
  const openPublicParentForm = () => {
    window.open("/public/parent-form", "_blank");
  };

   const openPublicSchoolForm = () => {
    window.open("/public/school-form", "_blank");
  };
  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-primary">Admin Dashboard</h1>
            <p className="text-muted-foreground">Manage users, roles, and system settings</p>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              className="flex items-center gap-2"
              onClick={openUniversityReferralPage}
            >
              <Share className="h-4 w-4" />
              University Referral
            </Button>
            <Button 
              variant="outline" 
              className="flex items-center gap-2"
              onClick={openIndividualReferralPage}
            >
              <Share className="h-4 w-4" />
              Individual Referral
            </Button>
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={openPublicAssesseeForm}
            >
              <Share className="h-4 w-4" />
              Assessee Form (Test)
            </Button>
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={openPublicParentForm}
            >
              <Share className="h-4 w-4" />
              Parent Form (Test)
            </Button>
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={openPublicSchoolForm}
            >
              <Share className="h-4 w-4" />
              School Form (Test)
            </Button>
          </div>
        </div>

        <Tabs defaultValue="verification" value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="grid w-full grid-cols-5 mb-6">
            <TabsTrigger value="verification">
              <CheckCircle className="h-4 w-4 mr-2" />
              Verification
            </TabsTrigger>
            <TabsTrigger value="users">
              <Users className="h-4 w-4 mr-2" />
              User Management
            </TabsTrigger>
            <TabsTrigger value="roles">
              <Shield className="h-4 w-4 mr-2" />
              Roles & Permissions
            </TabsTrigger>
            <TabsTrigger value="audit">
              <History className="h-4 w-4 mr-2" />
              Audit Logs
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="h-4 w-4 mr-2" />
              System Settings
            </TabsTrigger>
          </TabsList>

          {/* Verification Queue Tab */}
          <TabsContent value="verification">
            <VerificationQueue />
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>User Management</CardTitle>
                    <CardDescription>
                      View and manage all users within the system
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Dialog open={isInviteOpen} onOpenChange={setIsInviteOpen}>
                      <DialogTrigger asChild>
                        <Button>
                          <UserPlus className="h-4 w-4 mr-2" />
                          Invite User
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Invite New User</DialogTitle>
                          <DialogDescription>
                            Send an invitation to create a new account with specific role permissions.
                          </DialogDescription>
                        </DialogHeader>
                        <Form {...inviteForm}>
                          <form onSubmit={inviteForm.handleSubmit(handleInviteSubmit)} className="space-y-4">
                            <FormField
                              control={inviteForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Email Address</FormLabel>
                                  <FormControl>
                                    <Input placeholder="<EMAIL>" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={inviteForm.control}
                              name="role"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Role</FormLabel>
                                  <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                  >
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select a role" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="admin">Administrator</SelectItem>
                                      <SelectItem value="assessor">Assessor</SelectItem>
                                      <SelectItem value="university">University</SelectItem>
                                      <SelectItem value="school">School</SelectItem>
                                      <SelectItem value="parent">Parent</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={inviteForm.control}
                              name="fullName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Full Name</FormLabel>
                                  <FormControl>
                                    <Input placeholder="John Doe" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={inviteForm.control}
                              name="organization"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Organization (Optional)</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Organization name" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={inviteForm.control}
                              name="message"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Personal Message (Optional)</FormLabel>
                                  <FormControl>
                                    <Textarea 
                                      placeholder="Add a message to the invitation email" 
                                      className="min-h-[100px]" 
                                      {...field} 
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <DialogFooter>
                              <Button 
                                type="submit" 
                                disabled={inviteUserMutation.isPending}
                              >
                                {inviteUserMutation.isPending && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
                                Send Invitation
                              </Button>
                            </DialogFooter>
                          </form>
                        </Form>
                      </DialogContent>
                    </Dialog>
                    <Button variant="outline" onClick={() => refetch()}>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Pending Approvals Section */}
                {pendingApprovalUsers.length > 0 && (
                  <Alert className="mb-8">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Pending Approvals</AlertTitle>
                    <AlertDescription>
                      You have {pendingApprovalUsers.length} user(s) waiting for approval
                    </AlertDescription>
                    
                    <div className="mt-4 rounded-md border overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Role</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {pendingApprovalUsers.map((user: User) => (
                            <TableRow key={user.id} className="bg-muted/40">
                              <TableCell className="font-medium">{user.fullName}</TableCell>
                              <TableCell>{user.email}</TableCell>
                              <TableCell>
                                <Badge variant="outline" className="capitalize">
                                  {user.role}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button 
                                    variant="outline" 
                                    size="sm" 
                                    className="bg-green-500 hover:bg-green-600 text-white hover:text-white"
                                    onClick={() => handleQuickApprove(user)}
                                    disabled={updateUserMutation.isPending}
                                  >
                                    <Check className="mr-1 h-3 w-3" />
                                    Approve
                                  </Button>
                                  <Button 
                                    variant="outline" 
                                    size="sm" 
                                    className="bg-destructive hover:bg-destructive/90 text-white hover:text-white"
                                    onClick={() => handleQuickReject(user)}
                                    disabled={updateUserMutation.isPending}
                                  >
                                    <X className="mr-1 h-3 w-3" />
                                    Reject
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </Alert>
                )}

                <div className="flex flex-col space-y-4">
                  <div className="flex justify-between gap-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search by name, email, role, organization..."
                        className="pl-8"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      {searchTerm && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute right-1 top-1 h-7 w-7 p-0"
                          onClick={() => setSearchTerm("")}
                        >
                          <XCircle className="h-4 w-4" />
                          <span className="sr-only">Clear</span>
                        </Button>
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline">
                          <Filter className="h-4 w-4 mr-2" />
                          Filter
                          <ChevronDown className="h-4 w-4 ml-2" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[200px]">
                        <DropdownMenuLabel>Filter Users</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                          <p className="text-sm font-medium mb-2">Role</p>
                          <Select
                            value={roleFilter}
                            onValueChange={(value) => setRoleFilter(value === "all" ? undefined : value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="All Roles" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Roles</SelectItem>
                              <SelectItem value="admin">Administrator</SelectItem>
                              <SelectItem value="assessor">Assessor</SelectItem>
                              <SelectItem value="university">University</SelectItem>
                              <SelectItem value="school">School</SelectItem>
                              <SelectItem value="parent">Parent</SelectItem>
                              <SelectItem value="assessee">Assessee</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="p-2">
                          <p className="text-sm font-medium mb-2">Status</p>
                          <Select
                            value={statusFilter}
                            onValueChange={(value) => setStatusFilter(value === "all" ? undefined : value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="All Statuses" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Statuses</SelectItem>
                              <SelectItem value="active">Active</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                              <SelectItem value="inactive">Inactive</SelectItem>
                              <SelectItem value="email_verification_pending">Email Verification</SelectItem>
                              <SelectItem value="admin_approval_pending">Admin Approval</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() => {
                              setRoleFilter(undefined);
                              setStatusFilter(undefined);
                            }}
                          >
                            Clear Filters
                          </Button>
                        </div>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>User</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead className="w-[100px]">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {isLoading ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              <RefreshCw className="h-6 w-6 animate-spin mx-auto text-primary" />
                              <p className="mt-2 text-sm text-muted-foreground">Loading users...</p>
                            </TableCell>
                          </TableRow>
                        ) : filteredUsers.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              <p className="text-muted-foreground">No users found</p>
                            </TableCell>
                          </TableRow>
                        ) : (
                          filteredUsers.map((user: User) => (
                            <TableRow key={user.id}>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <div className="bg-muted h-8 w-8 rounded-full flex items-center justify-center">
                                    <UserIcon className="h-4 w-4 text-primary" />
                                  </div>
                                  <div>
                                    <p className="font-medium">{user.fullName || user.username}</p>
                                    <p className="text-xs text-muted-foreground">{user.email}</p>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline" className="capitalize">
                                  {user.role}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(user.status || "active")}
                              </TableCell>
                              <TableCell>
                                {user.createdAt ? formatDistanceToNow(new Date(user.createdAt), { addSuffix: true }) : "Unknown"}
                              </TableCell>
                              <TableCell>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem onClick={() => handleEditUser(user)}>
                                      <Edit className="h-4 w-4 mr-2" />
                                      Edit
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                      <Key className="h-4 w-4 mr-2" />
                                      Reset Password
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem className="text-destructive">
                                      <Trash className="h-4 w-4 mr-2" />
                                      Delete
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>

                {/* Edit User Dialog */}
                <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Edit User</DialogTitle>
                      <DialogDescription>
                        Update user details and permissions
                      </DialogDescription>
                    </DialogHeader>
                    {selectedUser && (
                      <Form {...editForm}>
                        <form onSubmit={editForm.handleSubmit(handleEditSubmit)} className="space-y-4">
                          <FormField
                            control={editForm.control}
                            name="username"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Username</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={editForm.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Email</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={editForm.control}
                            name="role"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Role</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select a role" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="admin">Administrator</SelectItem>
                                    <SelectItem value="assessor">Assessor</SelectItem>
                                    <SelectItem value="university">University</SelectItem>
                                    <SelectItem value="school">School</SelectItem>
                                    <SelectItem value="parent">Parent</SelectItem>
                                    <SelectItem value="assessee">Assessee</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={editForm.control}
                            name="status"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Status</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select a status" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="pending">Pending</SelectItem>
                                    <SelectItem value="inactive">Inactive</SelectItem>
                                    <SelectItem value="email_verification_pending">Email Verification Pending</SelectItem>
                                    <SelectItem value="admin_approval_pending">Admin Approval Pending</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <DialogFooter>
                            <Button 
                              type="submit" 
                              disabled={updateUserMutation.isPending}
                            >
                              {updateUserMutation.isPending && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
                              Save Changes
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    )}
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Roles Tab */}
          <TabsContent value="roles">
            <Card>
              <CardHeader>
                <CardTitle>Roles & Permissions</CardTitle>
                <CardDescription>
                  Configure system roles and their associated permissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="rounded-md border p-4">
                    <h3 className="text-lg font-medium">Administrator</h3>
                    <p className="text-sm text-muted-foreground mb-4">Users with complete system access and control</p>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Manage Users</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Configure Settings</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Manage Roles</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Manage All Assessments</Label>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-md border p-4">
                    <h3 className="text-lg font-medium">Assessor</h3>
                    <p className="text-sm text-muted-foreground mb-4">Clinical professionals who conduct assessments</p>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>View Assessments</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Conduct Assessments</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Generate Reports</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Manage Calendar</Label>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-md border p-4">
                    <h3 className="text-lg font-medium">School</h3>
                    <p className="text-sm text-muted-foreground mb-4">Educational institutions referring students for assessment</p>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Refer Students</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Track Referrals</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>View Reports</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-primary">✓</Badge>
                        <Label>Complete Forms</Label>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Audit Logs Tab */}
          <TabsContent value="audit">
            <Card>
              <CardHeader>
                <CardTitle>Admin Activity Logs</CardTitle>
                <CardDescription>
                  Track administrative actions within the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Admin</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>Target User</TableHead>
                        <TableHead>Details</TableHead>
                        <TableHead>Time</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {activityLogs.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4">
                            <p className="text-muted-foreground">No activity logs found</p>
                          </TableCell>
                        </TableRow>
                      ) : (
                        activityLogs.map((log: ActivityLog) => (
                          <TableRow key={log.id}>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className="bg-primary/10 h-8 w-8 rounded-full flex items-center justify-center">
                                  <UserIcon className="h-4 w-4 text-primary" />
                                </div>
                                <span>{log.adminName}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{log.action}</Badge>
                            </TableCell>
                            <TableCell>{log.targetUserName}</TableCell>
                            <TableCell>{log.details}</TableCell>
                            <TableCell>
                              {formatDistanceToNow(new Date(log.createdAt), { addSuffix: true })}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          {/* System Settings Tab */}
          <TabsContent value="settings">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Email Configuration</CardTitle>
                  <CardDescription>Test and manage email sending functionality</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">SendGrid API Status</p>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-green-500">Connected</Badge>
                      <span className="text-sm text-muted-foreground">Using SendGrid for email delivery</span>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Send Test Email</p>
                    <p className="text-sm text-muted-foreground mb-4">
                      Send a test email to verify that your email configuration is working correctly
                    </p>
                    
                    <div className="flex gap-2">
                      <Input 
                        id="test-email" 
                        placeholder="<EMAIL>" 
                        className="flex-1"
                        type="email"
                        value={testEmail}
                        onChange={(e) => setTestEmail(e.target.value)}
                      />
                      <Button 
                        onClick={() => testEmailMutation.mutate(testEmail)}
                        disabled={testEmailMutation.isPending || !testEmail}
                      >
                        {testEmailMutation.isPending ? (
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <Mail className="mr-2 h-4 w-4" />
                        )}
                        Send Test
                      </Button>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between border-t pt-6">
                  <div className="text-xs text-muted-foreground">
                    Using SendGrid v3 API for email delivery
                  </div>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Application Settings</CardTitle>
                  <CardDescription>Configure global application settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="app-name">Application Name</Label>
                        <p className="text-sm text-muted-foreground">The name displayed throughout the application</p>
                      </div>
                      <Input id="app-name" value="NeuroElevate" className="w-[200px]" readOnly />
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Debug Mode</Label>
                        <p className="text-sm text-muted-foreground">Enable verbose logging for troubleshooting</p>
                      </div>
                      <Switch id="debug-mode" />
                    </div>
                  </div>

                  <Separator />
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Maintenance Mode</Label>
                        <p className="text-sm text-muted-foreground">Temporarily disable access for non-admin users</p>
                      </div>
                      <Switch id="maintenance-mode" />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="border-t pt-6">
                  <Button variant="outline" className="w-full">Save Settings</Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
