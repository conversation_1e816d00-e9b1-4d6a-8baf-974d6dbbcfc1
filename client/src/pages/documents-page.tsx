import React from 'react';
import AppLayout from '@/components/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Download, FileUp, FolderPlus } from 'lucide-react';
import { User } from '@shared/schema';
import { useQuery } from '@tanstack/react-query';

interface DocumentsPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function DocumentsPage({ user, logout }: DocumentsPageProps = {}) {

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Documents</h1>
          <Button>
            <FileUp className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>My Documents</CardTitle>
            <CardDescription>
              Access all documents related to your assessments.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center p-12">
              <FolderPlus className="h-16 w-16 text-muted-foreground mb-4 opacity-40" />
              <h3 className="text-lg font-medium mb-1">No Documents</h3>
              <p className="text-muted-foreground text-center mb-6 max-w-sm">
                You don't have any documents uploaded yet. Documents will appear here once they're uploaded during the assessment process.
              </p>
              <Button>
                <FileUp className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}