import React, { useEffect } from 'react';
import AppLayout from '@/components/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>ert<PERSON>ircle, ArrowLeft, Save } from 'lucide-react';
import { Link, useRoute } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { User, Form as FormType, FormQuestion, FormResponse } from '@shared/schema';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { ParentUnder16Form } from '@/components/forms/parent-under-16-form';
import { AssesseeOver16Form } from '@/components/forms/assessee-over-16-form';
import { SchoolForm } from '@/components/forms/school-form';
import { FormDataDisplay } from '@/components/forms/form-data-display';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

interface FormPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

export default function FormPage({ user, logout }: FormPageProps = {}) {
  const { toast } = useToast();
  const [match, params] = useRoute('/forms/:id');

  // Fix form ID validation to handle various formats and log diagnostic info
  const parseFormId = (formIdParam: string | undefined): number => {
    console.log("Form ID parameter:", formIdParam);
    
    // First try direct numeric parsing
    if (formIdParam && /^\d+$/.test(formIdParam)) {
      return parseInt(formIdParam);
    }
    
    // Then try extracting numeric ID if it's embedded in a string
    if (formIdParam && typeof formIdParam === 'string') {
      const numericMatch = formIdParam.match(/(\d+)/);
      if (numericMatch && numericMatch[1]) {
        return parseInt(numericMatch[1]);
      }
    }
    
    // Default to 0 which indicates invalid ID
    return 0;
  };
  
  const id = params?.id ? parseFormId(params.id) : 0;
  
  // Selectively clear localStorage on new form load but not after saving draft
  useEffect(() => {
    if (id > 0) {
      try {
        // Check if there's a save in progress flag or backup data
        const saveInProgress = localStorage.getItem(`form-${id}-save-in-progress`) === 'true';
        
        // Check for backup data from previous save
        const hasSchoolBackup = localStorage.getItem(`schoolForm-${id}-data-backup`) !== null;
        const hasParentBackup = localStorage.getItem(`parentForm-${id}-data-backup`) !== null;
        const hasAssesseeBackup = localStorage.getItem(`assesseeForm-${id}-data-backup`) !== null;
        
        if (saveInProgress || hasSchoolBackup || hasParentBackup || hasAssesseeBackup) {
          console.log(`Detected saved data for form ${id}, not clearing localStorage`);
          
          // Restore from backup if needed
          if (hasSchoolBackup) {
            const backupData = localStorage.getItem(`schoolForm-${id}-data-backup`);
            const backupSection = localStorage.getItem(`schoolForm-${id}-section-backup`);
            
            localStorage.setItem(`schoolForm-${id}-data`, backupData!);
            localStorage.setItem(`schoolForm-${id}-section`, backupSection!);
            console.log(`Restored school form data from backup for form ${id}`);
          }
          
          if (hasParentBackup) {
            const backupData = localStorage.getItem(`parentForm-${id}-data-backup`);
            const backupSection = localStorage.getItem(`parentForm-${id}-section-backup`);
            
            localStorage.setItem(`parentForm-${id}-data`, backupData!);
            localStorage.setItem(`parentForm-${id}-section`, backupSection!);
            console.log(`Restored parent form data from backup for form ${id}`);
          }
          
          if (hasAssesseeBackup) {
            const backupData = localStorage.getItem(`assesseeForm-${id}-data-backup`);
            const backupSection = localStorage.getItem(`assesseeForm-${id}-section-backup`);
            
            localStorage.setItem(`assesseeForm-${id}-data`, backupData!);
            localStorage.setItem(`assesseeForm-${id}-section`, backupSection!);
            console.log(`Restored assessee form data from backup for form ${id}`);
          }
          
          // Clear the save in progress flag
          localStorage.removeItem(`form-${id}-save-in-progress`);
        } else {
          // Only clear localStorage if there's no saved data from a draft
          ['schoolForm', 'parentForm', 'assesseeForm'].forEach(formType => {
            localStorage.removeItem(`${formType}-${id}-data`);
            localStorage.removeItem(`${formType}-${id}-section`);
          });
          console.log(`Cleared localStorage for all form types with ID ${id}`);
        }
      } catch (error) {
        console.error("Error managing localStorage:", error);
      }
    }
  }, [id]);
  
  // Fetch form data
  // Check if we have a valid ID before making the query
  const isValidId = id > 0;

  const { 
    data: form, 
    isLoading: formLoading, 
    isError: formError,
    error: formErrorData
  } = useQuery<FormType>({ 
    queryKey: ['/api/forms', id.toString()],
    enabled: isValidId,
    retry: 1,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true
  });

  // Fetch form questions if the form is loaded
  const {
    data: questions,
    isLoading: questionsLoading,
    isError: questionsError
  } = useQuery<FormQuestion[]>({
    queryKey: ['/api/form-questions', form?.formType || ''],
    enabled: !!form?.formType,
    retry: 1,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true
  });

  // Fetch existing form responses if the form is loaded
  const {
    data: responses,
    isLoading: responsesLoading,
    isError: responsesError
  } = useQuery<FormResponse[]>({
    queryKey: [`/api/form-responses/form`, id.toString()],
    enabled: isValidId && !!form,
    retry: 1,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true
  });

  // Determine if we should use the newer normalized form data approach
  // We'll only use the legacy format if no questions are defined in the database
  const isUsingLegacyFormat = !questions || questions.length === 0;
  console.log(`Form data storage approach: ${isUsingLegacyFormat ? 'legacy JSON' : 'normalized form responses'}`);
  if (!isUsingLegacyFormat) {
    console.log(`Using normalized form responses approach with ${questions.length} questions defined`);
  }
  
  console.log("Form loading status:", { 
    formLoading, 
    questionsLoading, 
    responsesLoading,
    formError,
    questionsError,
    responsesError,
    formErrorData
  });
  
  // Mutation for saving form responses (new approach)
  const saveResponsesMutation = useMutation({
    mutationFn: async ({ responses, markComplete }: { responses: any[], markComplete: boolean }) => {
      const response = await apiRequest('POST', '/api/form-responses', {
        formId: id,
        responses,
        markComplete
      });
      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/forms', id.toString()] });
      queryClient.invalidateQueries({ queryKey: ['/api/form-responses', id.toString()] });
      console.log("Successfully saved form responses");
      toast({
        title: "Form saved",
        description: "Your responses have been saved successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error saving form",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  // Legacy mutation for saving form data (old approach)
  const updateFormMutation = useMutation({
    mutationFn: async (formData: any) => {
      const response = await apiRequest('PATCH', `/api/forms/${id}`, formData);
      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/forms', id.toString()] });
      toast({
        title: "Form saved",
        description: "The form has been saved successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error saving form",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  // Map UI form field names to database question keys
  const getFieldNameMapping = (formType: string): Record<string, string> => {
    // Define mappings for each form type - from UI field name to DB question key
    if (formType === 'school') {
      return {
        // School form field mappings
        'childFullName': 'pupil_name', 
        'school': 'school_name',
        'yearGroup': 'pupil_year_group',
        'personCompletingName': 'school_email', // Using this for demo purposes
        'personCompletingRole': 'pupil_class'
      };
    } else if (formType === 'parent_under_16') {
      return {
        // Parent form field mappings
        'childName': 'child_name',
        'childDateOfBirth': 'child_dob',
        'childSchool': 'child_school',
        'childYearGroup': 'child_year_group'
      };
    } else if (formType === 'assessee_over_16') {
      return {
        // Assessee form field mappings
        'fullName': 'full_name',
        'preferredName': 'preferred_name',
        'dateOfBirth': 'date_of_birth',
        'address': 'address',
        'contactPhone': 'phone',
        'contactPhoneWork': 'phone_work',
        'contactEmail': 'email',
        'currentInstitution': 'current_institution',
        'courseOfStudy': 'course_of_study',
        'highestQualification': 'highest_qualification',
        'gender': 'gender',
        'preferredPronoun': 'preferred_pronoun',
        'hearAbout': 'hear_about',
        'hearAboutOther': 'hear_about_other'
      };
    }
    return {};
  };

  // Convert flat form data to array of question responses
  const convertFormDataToResponses = (formData: any, questions: FormQuestion[]) => {
    // Get field name mapping for this form type
    const fieldMapping = getFieldNameMapping(form?.formType || '');
    console.log("Using field mapping:", fieldMapping);
    
    // Transform the flat form data into response objects for each question
    return questions.map(question => {
      // First try to find a direct match with the question key
      let value = formData[question.questionKey];
      
      // If no direct match, check if there's a mapped field name
      if (value === undefined) {
        // Find the UI field name that maps to this question key
        const uiFieldName = Object.entries(fieldMapping).find(
          ([_, dbKey]) => dbKey === question.questionKey
        )?.[0];
        
        if (uiFieldName) {
          value = formData[uiFieldName];
          console.log(`Mapped UI field '${uiFieldName}' to DB field '${question.questionKey}' with value:`, value);
        }
      }
      
      // Create a response object based on the question type
      const response: any = {
        formId: id,
        questionId: question.id
      };
      
      // Add the appropriate field based on the question type
      if (question.questionType === 'text' || question.questionType === 'textarea') {
        response.responseText = value;
      } else if (question.questionType === 'number') {
        response.responseNumber = value ? parseFloat(value) : null;
      } else if (question.questionType === 'boolean' || question.questionType === 'checkbox') {
        response.responseBoolean = value === 'true' || value === true;
      } else if (question.questionType === 'select' || question.questionType === 'radio') {
        // Make sure radio values (like gender and pronoun) are properly stored
        response.responseOptions = value;
        // For gender and pronoun fields, store a duplicate in responseText to ensure they're picked up
        if (question.questionKey === 'gender' || question.questionKey === 'preferred_pronoun') {
          response.responseText = value;
        }
      } else if (question.questionType === 'date') {
        response.responseText = value; // Store dates as text in ISO format
      }
      
      return response;
    }).filter(response => {
      // Only include responses that have a value
      return (
        response.responseText !== undefined || 
        response.responseNumber !== undefined || 
        response.responseBoolean !== undefined || 
        response.responseOptions !== undefined
      );
    });
  };
  
  // Save as draft
  const handleSaveDraft = (formData: any) => {
    console.log("Form page handleSaveDraft called with formData:", formData);
    
    // Create a permanent backup of the form data before saving to DB
    // This ensures we don't lose data even if the form state is cleared
    if (form?.formType === 'school') {
      localStorage.setItem(`schoolForm-${id}-data-backup`, JSON.stringify(formData));
      localStorage.setItem(`schoolForm-${id}-section-backup`, localStorage.getItem(`schoolForm-${id}-section`) || '1');
    } else if (form?.formType === 'parent_under_16') {
      localStorage.setItem(`parentForm-${id}-data-backup`, JSON.stringify(formData));
      localStorage.setItem(`parentForm-${id}-section-backup`, localStorage.getItem(`parentForm-${id}-section`) || '1');
    } else if (form?.formType === 'assessee_over_16') {
      localStorage.setItem(`assesseeForm-${id}-data-backup`, JSON.stringify(formData));
      localStorage.setItem(`assesseeForm-${id}-section-backup`, localStorage.getItem(`assesseeForm-${id}-section`) || '1');
    }
    
    if (isUsingLegacyFormat) {
      // If we're still using the legacy format (no questions defined in the database)
      console.log("Using legacy format to save draft");
      
      // Ensure gender and preferred pronoun values are included in the saved data
      const savedGender = localStorage.getItem(`assesseeForm-${id}-gender`);
      const savedPronoun = localStorage.getItem(`assesseeForm-${id}-pronoun`);
      
      // If gender or pronoun values are saved in localStorage but not in formData, add them
      if (savedGender && !formData.gender) {
        console.log("Adding saved gender to form data:", savedGender);
        formData.gender = savedGender;
      }
      
      if (savedPronoun && !formData.preferredPronoun) {
        console.log("Adding saved pronoun to form data:", savedPronoun);
        formData.preferredPronoun = savedPronoun;
      }
      
      console.log("Final form data for saving:", formData);
      
      updateFormMutation.mutate({ 
        status: 'in_progress',
        data: JSON.stringify(formData)
      });
    } else {
      // Using the new formResponses approach
      console.log("Using formResponses approach to save draft");
      console.log("Form data keys:", Object.keys(formData));
      console.log("Question keys:", questions!.map(q => q.questionKey));
      
      // Debug the form data
      Object.keys(formData).forEach(key => {
        console.log(`Form field '${key}' has value:`, formData[key]);
      });
      
      const formResponses = convertFormDataToResponses(formData, questions!);
      console.log(`Converted ${Object.keys(formData).length} form fields to ${formResponses.length} responses`);
      
      if (formResponses.length > 0) {
        console.log("Saving responses with API call");
        
        // Set a flag to indicate a save is in progress
        localStorage.setItem(`form-${id}-save-in-progress`, 'true');
        
        saveResponsesMutation.mutate({
          responses: formResponses,
          markComplete: false
        });
      } else {
        console.log("No form responses to save");
        toast({
          title: "No changes",
          description: "No form responses to save.",
          variant: "default"
        });
      }
    }
  };
  
  // Submit form
  const handleSubmit = (formData: any) => {
    // Log the form data to help with debugging
    console.log("Submitting form with data:", formData);
    console.log("Form data keys:", Object.keys(formData));
    console.log("Form data values:", formData);
    
    // Ensure we capture ALL form fields properly - preserve every field that was submitted
    const processedFormData = {
      // Start with all original form data to preserve every field
      ...formData,
      
      // Add normalized field mappings for backward compatibility
      firstName: formData.firstName || formData.fullName || formData.childName,
      lastName: formData.lastName,
      email: formData.email || formData.contactEmail,
      phone: formData.phone || formData.contactPhone,
      address: formData.address || formData.contactAddress,
      
      // Ensure submission timestamp is always included
      submittedAt: new Date().toISOString(),
      
      // Preserve form-specific fields that might have different naming conventions
      ...(form?.formType === 'parent_under_16' && {
        // Parent form specific fields
        childName: formData.childName,
        childDateOfBirth: formData.childDateOfBirth,
        childAge: formData.childAge
      }),
      
      ...(form?.formType === 'assessee_over_16' && {
        // Assessee form specific fields
        fullName: formData.fullName,
        dateOfBirth: formData.dateOfBirth,
        age: formData.age,
        contactAddress: formData.contactAddress,
        contactPhone: formData.contactPhone,
        contactEmail: formData.contactEmail,
        countryOfBirth: formData.countryOfBirth,
        gender: formData.gender,
        preferredPronoun: formData.preferredPronoun
      }),
      
      ...(form?.formType === 'school' && {
        // School form specific fields
        childName: formData.childName,
        dateOfBirth: formData.dateOfBirth,
        schoolYear: formData.schoolYear,
        school: formData.school,
        personCompletingName: formData.personCompletingName
      })
    };
    
    // Log the complete form data for submission debugging
    console.log("Submitting with processed data:", processedFormData);
    console.log("Processed data keys:", Object.keys(processedFormData));
    console.log("Submitting form with stringified data:", JSON.stringify(processedFormData));
    
    // Update the form with the completed status and data
    updateFormMutation.mutate({ 
      status: 'completed',
      data: JSON.stringify(processedFormData)
    }, {
      onSuccess: (data) => {
        console.log("Form completed successfully, server response:", data);
        toast({
          title: "Form completed",
          description: "Your form has been submitted successfully.",
        });
      },
      onError: (error) => {
        console.error("Error completing form:", error);
        toast({
          title: "Error",
          description: "There was a problem submitting your form. Please try again.",
          variant: "destructive"
        });
      }
    });
    
    // Save a backup of the form data in localStorage in case the server update fails
    localStorage.setItem(`form-${id}-completed-data`, JSON.stringify(processedFormData));
    
    if (!isUsingLegacyFormat) {
      // Using the new formResponses approach in addition to JSON storage
      console.log("Also using form responses approach with processed data:", processedFormData);
      
      // We're also storing the individual form responses in the normalized table
      // but only after the main form update is successful
      try {
        const formResponses = convertFormDataToResponses(processedFormData, questions!);
        
        if (formResponses.length > 0) {
          saveResponsesMutation.mutate({
            responses: formResponses,
            markComplete: true
          });
        } else {
          console.log("No individual question responses to save");
        }
      } catch (error) {
        console.error("Error converting form data to responses:", error);
      }
      
      // We've already saved the individual responses above, no need to do it twice
      // This was causing duplicate submission attempts
    }
  };
  
  // Convert form responses to a flat object for the form component
  const formatResponsesForForm = (responses: FormResponse[] | undefined, questions: FormQuestion[] | undefined) => {
    if (!responses || !questions) return {};
    
    const formData: Record<string, any> = {};
    
    // Get field name mapping for this form type (reverse mapping needed)
    const fieldMapping = getFieldNameMapping(form?.formType || '');
    const reverseMapping: Record<string, string> = {};
    
    // Create reverse mapping (DB field → UI field)
    Object.entries(fieldMapping).forEach(([uiField, dbField]) => {
      reverseMapping[dbField] = uiField;
    });
    
    console.log("Reverse field mapping for loading responses:", reverseMapping);
    console.log("Form responses found:", responses.length);
    console.log("Form questions found:", questions.length);
    
    responses.forEach(response => {
      // Find the matching question
      const question = questions.find(q => q.id === response.questionId);
      if (!question) {
        console.log(`No matching question found for response ID ${response.id}, question ID ${response.questionId}`);
        return;
      }
      
      // Get the database field key
      const dbKey = question.questionKey;
      
      // Get the value based on the question type
      let value = null;
      if (response.responseText !== null && response.responseText !== undefined) {
        value = response.responseText;
      } else if (response.responseNumber !== null && response.responseNumber !== undefined) {
        value = response.responseNumber;
      } else if (response.responseBoolean !== null && response.responseBoolean !== undefined) {
        value = response.responseBoolean;
      } else if (response.responseOptions !== null && response.responseOptions !== undefined) {
        value = response.responseOptions;
      }
      
      console.log(`Response for question ${dbKey}: ${value}`);
      
      // Store value using the DB key
      formData[dbKey] = value;
      
      // If there's a UI field name that maps to this DB key, use that too
      const uiFieldName = reverseMapping[dbKey];
      if (uiFieldName) {
        formData[uiFieldName] = value;
        console.log(`Mapped DB field '${dbKey}' to UI field '${uiFieldName}' with value:`, value);
      }
    });
    
    // Save session data to help preserve state
    if (form?.formType === 'school') {
      localStorage.setItem(`schoolForm-${id}-formData`, JSON.stringify(formData));
    } else if (form?.formType === 'parent_under_16') {
      localStorage.setItem(`parentForm-${id}-formData`, JSON.stringify(formData));
    } else if (form?.formType === 'assessee_over_16') {
      localStorage.setItem(`assesseeForm-${id}-formData`, JSON.stringify(formData));
    }
    
    console.log("Formatted form data:", formData);
    return formData;
  };
  
  // Get form data either from the legacy approach or the new responses
  const getFormInitialData = () => {
    console.log("Getting initial data for form status:", form?.status);
    console.log("Form completion status:", form?.status === 'completed');
    console.log("Using legacy format:", isUsingLegacyFormat);
    console.log("Available responses:", responses?.length || 0);
    
    // For completed forms, prioritize the normalized responses over everything else
    if (form?.status === 'completed' && !isUsingLegacyFormat && responses && responses.length > 0) {
      console.log("Form is completed - loading from saved responses");
      const formattedData = formatResponsesForForm(responses, questions);
      console.log("Formatted response data for completed form:", formattedData);
      return formattedData;
    }
    
    // For completed forms that don't have normalized responses, check for backup data
    if (form?.status === 'completed') {
      console.log("Completed form without normalized responses - checking backup data");
      const backupData = localStorage.getItem(`form-${id}-completed-data`);
      if (backupData) {
        try {
          const parsedBackup = JSON.parse(backupData);
          console.log("Found backup data for completed form:", parsedBackup);
          return parsedBackup;
        } catch (e) {
          console.error("Error parsing backup data:", e);
        }
      }
    }
    
    // Always check localStorage first for any existing draft data
    const getLocalStorageData = () => {
      const localStorageKey = form?.formType === 'school' 
        ? `schoolForm-${id}-data-backup` 
        : form?.formType === 'parent_under_16'
          ? `parentForm-${id}-data-backup`
          : `assesseeForm-${id}-data-backup`;
      
      const localData = localStorage.getItem(localStorageKey);
      console.log("Found local data for form:", localData);
      
      if (localData) {
        try {
          return JSON.parse(localData);
        } catch (e) {
          console.error("Error parsing local storage data:", e);
        }
      }
      return null;
    };
    
    // First try to get data from the form itself (legacy JSON format)
    if (form?.data) {
      try {
        console.log("Using form data from database:", form.data);
        return JSON.parse(form.data);
      } catch (e) {
        console.error("Error parsing form data:", e);
      }
    }
    
    // Then try local storage
    const localData = getLocalStorageData();
    if (localData) {
      return localData;
    }
    
    // If using the new format, use the normalized responses
    if (!isUsingLegacyFormat) {
      console.log("Loading from normalized responses");
      const formattedData = formatResponsesForForm(responses, questions);
      console.log("Formatted response data:", formattedData);
      return formattedData;
    }
    
    // Default empty object if no data found
    console.log("No data found - returning empty object");
    return {};
  };

  // Render the appropriate form component based on the form type
  const renderFormComponent = (formInitialData: any) => {
    if (!form) return null;
    
    // Don't clear localStorage when saving a form as draft
    // This prevents the form from resetting
    const clearLocalStorageOnSave = false;
    
    switch (form.formType) {
      case 'parent_under_16':
        return (
          <ParentUnder16Form 
            formId={id} 
            initialData={formInitialData} 
            onSubmit={handleSubmit} 
            onSaveDraft={handleSaveDraft} 
          />
        );
      case 'assessee_over_16':
        return (
          <AssesseeOver16Form 
            formId={id} 
            initialData={formInitialData} 
            onSubmit={handleSubmit} 
            onSaveDraft={handleSaveDraft} 
          />
        );
      case 'school':
        return (
          <SchoolForm 
            formId={id} 
            initialData={formInitialData} 
            onSubmit={handleSubmit} 
            onSaveDraft={handleSaveDraft} 
          />
        );
      default:
        return (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Unknown form type: {form.formType}
            </AlertDescription>
          </Alert>
        );
    }
  };
  
  // Handle loading state
  const isLoading = formLoading || questionsLoading || responsesLoading;
  if (isLoading) {
    return (
      <AppLayout user={user} logout={logout}>
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Link href="/assessments">
              <Button variant="outline" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <Skeleton className="h-8 w-40" />
          </div>
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-40 mb-2" />
              <Skeleton className="h-4 w-full" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }
  
  // Handle error state
  const hasError = formError || questionsError || responsesError;
  if (hasError || !form) {
    return (
      <AppLayout user={user} logout={logout}>
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Link href="/assessments">
              <Button variant="outline" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">Form Error</h1>
          </div>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {formError ? (
                formErrorData?.message || 'The form could not be found.'
              ) : questionsError ? (
                'Error loading form questions.'
              ) : responsesError ? (
                'Error loading form responses.'
              ) : (
                'There was a problem loading the form.'
              )}
            </AlertDescription>
          </Alert>
        </div>
      </AppLayout>
    );
  }
  
  // Determine form title based on form type
  const getFormTitle = () => {
    switch (form.formType) {
      case 'parent_under_16':
        return 'Parent Pre-Assessment Questionnaire (Under 16 Years)';
      case 'assessee_over_16':
        return 'Assessee Pre-Assessment Questionnaire (Over 16 Years)';
      case 'school':
        return 'School Pre-Assessment Form';
      default:
        return 'Assessment Form';
    }
  };

  const formInitialData = getFormInitialData();

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Link href="/assessments">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">{getFormTitle()}</h1>
        </div>

        <FormDataDisplay
          status={form.status}
          completedAt={form.completedAt as any}
          completedById={form.completedById as any}
        />

        {renderFormComponent(formInitialData)}
      </div>
    </AppLayout>
  );
}