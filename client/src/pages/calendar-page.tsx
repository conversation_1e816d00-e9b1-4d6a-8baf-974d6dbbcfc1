import React, { useEffect, useState } from 'react';
import AppLayout from '@/components/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { User } from '@shared/schema';
import { Calendar } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/query-client';
import { Skeleton } from '@/components/ui/skeleton';
import { format, parseISO } from 'date-fns';
import { CalendarIcon, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';

interface CalendarPageProps {
  user?: User | null;
  logout?: () => Promise<void>;
}

interface Assessment {
  id: number;
  assesseeId: number;
  scheduledDate: string | null;
  status: string;
  assessee?: {
    fullName: string;
  };
}

export default function CalendarPage({ user, logout }: CalendarPageProps = {}) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [assessmentsByDate, setAssessmentsByDate] = useState<Record<string, Assessment[]>>({});
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Fetch all assessments that have scheduled dates
  const { data: assessments, isLoading } = useQuery({
    queryKey: ['/api/assessments'],
    enabled: !!user,
    select: (data: any) => {
      return data.filter((assessment: Assessment) => assessment.scheduledDate);
    }
  });

  // Organize assessments by date
  useEffect(() => {
    if (assessments) {
      const byDate: Record<string, Assessment[]> = {};
      
      assessments.forEach((assessment: Assessment) => {
        if (assessment.scheduledDate) {
          // Format date as YYYY-MM-DD for grouping
          const dateKey = assessment.scheduledDate.split('T')[0];
          
          if (!byDate[dateKey]) {
            byDate[dateKey] = [];
          }
          
          byDate[dateKey].push(assessment);
        }
      });
      
      setAssessmentsByDate(byDate);
    }
  }, [assessments]);

  // Get dates that have assessments for styling
  const datesWithAssessments = Object.keys(assessmentsByDate).map(dateKey => new Date(dateKey));

  // Get assessments for the selected date
  const selectedDateKey = selectedDate ? format(selectedDate, 'yyyy-MM-dd') : '';
  const selectedDateAssessments = assessmentsByDate[selectedDateKey] || [];

  // Handle click to view assessment details
  const handleViewAssessment = (assessmentId: number) => {
    setLocation(`/assessments/${assessmentId}`);
  };

  return (
    <AppLayout user={user} logout={logout}>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Assessment Calendar</h1>
        
        <div className="grid lg:grid-cols-[400px_1fr] gap-6">
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Calendar</CardTitle>
              <CardDescription>
                View scheduled assessments by date
              </CardDescription>
            </CardHeader>
            <CardContent className="p-3">
              {isLoading ? (
                <Skeleton className="h-[350px] w-full" />
              ) : (
                <div className="w-full overflow-hidden">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    className="w-full border rounded-md"
                    modifiers={{
                      hasAppointments: datesWithAssessments
                    }}
                    modifiersStyles={{
                      hasAppointments: {
                        backgroundColor: '#dbeafe',
                        color: '#1e40af',
                        fontWeight: 'bold'
                      }
                    }}
                  />
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedDate ? (
                  <div className="flex items-center">
                    <CalendarIcon className="mr-2 h-5 w-5" />
                    Assessments on {format(selectedDate, 'MMMM d, yyyy')}
                  </div>
                ) : (
                  "Select a date to view assessments"
                )}
              </CardTitle>
              <CardDescription>
                {selectedDateAssessments.length > 0 
                  ? `${selectedDateAssessments.length} assessment${selectedDateAssessments.length > 1 ? 's' : ''} scheduled`
                  : "No assessments scheduled for this date"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedDateAssessments.length > 0 ? (
                    selectedDateAssessments
                      .sort((a, b) => {
                        if (!a.scheduledDate || !b.scheduledDate) return 0;
                        return parseISO(a.scheduledDate).getTime() - parseISO(b.scheduledDate).getTime();
                      })
                      .map((assessment) => (
                        <div 
                          key={assessment.id}
                          className="p-4 border rounded-lg hover:shadow-md cursor-pointer transition-all duration-200 bg-white hover:bg-gray-50"
                          onClick={() => handleViewAssessment(assessment.id)}
                        >
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex-1">
                              <h3 className="font-semibold text-lg text-gray-900">
                                {assessment.assessee?.fullName || 'Unnamed Assessee'}
                              </h3>
                              <p className="text-sm text-gray-500 mt-1">
                                Assessment ID: #{assessment.id}
                              </p>
                            </div>
                            {assessment.scheduledDate && (
                              <div className="flex items-center bg-blue-50 px-3 py-1.5 rounded-full">
                                <Clock className="h-4 w-4 mr-2 text-blue-600" />
                                <span className="font-medium text-blue-700 text-sm">
                                  {format(parseISO(assessment.scheduledDate), 'h:mm a')}
                                </span>
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <Badge 
                              variant="secondary" 
                              className={`px-3 py-1 text-xs font-medium
                                ${assessment.status === 'completed' ? "bg-green-100 text-green-800 border-green-200" : ""}
                                ${assessment.status === 'scheduled' ? "bg-blue-100 text-blue-800 border-blue-200" : ""}
                                ${assessment.status === 'in_progress' ? "bg-yellow-100 text-yellow-800 border-yellow-200" : ""}
                                ${assessment.status === 'assessment_complete' ? "bg-purple-100 text-purple-800 border-purple-200" : ""}
                                ${assessment.status === 'report_writing' ? "bg-orange-100 text-orange-800 border-orange-200" : ""}
                                ${assessment.status === 'cancelled' ? "bg-red-100 text-red-800 border-red-200" : ""}
                                ${!['completed', 'scheduled', 'in_progress', 'assessment_complete', 'report_writing', 'cancelled'].includes(assessment.status) ? "bg-gray-100 text-gray-800 border-gray-200" : ""}
                              `}
                            >
                              {assessment.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </Badge>
                            
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-gray-400">Click to view</span>
                              <div className="w-5 h-5 flex items-center justify-center">
                                <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                  ) : (
                    <div className="py-16 text-center">
                      <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <CalendarIcon className="h-8 w-8 text-gray-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No appointments scheduled
                      </h3>
                      <p className="text-gray-500 mb-4">
                        No assessments are scheduled for {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'this date'}
                      </p>
                      <div className="text-xs text-gray-400 bg-gray-50 rounded-lg p-3 max-w-sm mx-auto">
                        <p>💡 Tip: Select different dates to view other scheduled appointments</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}