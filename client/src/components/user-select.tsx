import { useState, useEffect } from "react";
import { Check, ChevronsUpDown, X, Search, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { apiRequest } from "@/lib/queryClient";
import { useQuery } from "@tanstack/react-query";

interface UserSelectProps {
  selectedUsers: number[];
  onSelect: (userId: number) => void;
  onRemove: (userId: number) => void;
  disabled?: boolean;
  roleFilter?: string[];
}

export function UserSelect({ selectedUsers, onSelect, onRemove, disabled, roleFilter }: UserSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch the list of users
  const { data: users, isLoading } = useQuery({
    queryKey: ['/api/users/select'],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/users/select");
      const data = await res.json();
      
      // If role filter is provided, filter the users
      if (roleFilter && roleFilter.length > 0) {
        return data.filter((user: any) => roleFilter.includes(user.role));
      }
      
      return data;
    },
  });

  // Filter users based on search query
  const filteredUsers = users?.filter((user: any) => {
    const searchLower = searchQuery.toLowerCase();
    const fullNameMatch = user.fullName?.toLowerCase().includes(searchLower);
    const emailMatch = user.email?.toLowerCase().includes(searchLower);
    const roleMatch = user.role?.toLowerCase().includes(searchLower);
    
    return fullNameMatch || emailMatch || roleMatch;
  });

  // Find selected user details by ID
  const getSelectedUserDetails = (userId: number) => {
    return users?.find((user: any) => user.id === userId);
  };

  return (
    <div className="flex flex-col space-y-2">
      {/* Selected Users */}
      <div className="flex flex-wrap gap-2 mb-2">
        {selectedUsers.map((userId) => {
          const user = getSelectedUserDetails(userId);
          if (!user) return null;
          
          return (
            <Badge key={userId} variant="secondary" className="flex items-center gap-1 py-1 px-2">
              <span className="max-w-[150px] truncate">{user.fullName || user.email}</span>
              <button 
                type="button" 
                onClick={() => onRemove(userId)}
                className="ml-1 rounded-full opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                disabled={disabled}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove</span>
              </button>
            </Badge>
          );
        })}
      </div>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled}
          >
            <div className="flex items-center gap-2">
              <Search className="w-4 h-4 opacity-50" />
              <span>{selectedUsers.length > 0 ? `${selectedUsers.length} users selected` : "Select users"}</span>
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput 
              placeholder="Search users..." 
              className="h-9"
              value={searchQuery}
              onValueChange={setSearchQuery}
            />
            {isLoading ? (
              <div className="flex items-center justify-center p-6">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <>
                <CommandEmpty>No users found.</CommandEmpty>
                <CommandGroup>
                  <ScrollArea className="h-60">
                    {filteredUsers?.map((user: any) => (
                      <CommandItem
                        key={user.id}
                        value={user.id.toString()}
                        onSelect={() => {
                          if (selectedUsers.includes(user.id)) {
                            onRemove(user.id);
                          } else {
                            onSelect(user.id);
                          }
                        }}
                      >
                        <div className="flex flex-col mr-2">
                          <span className="font-medium truncate">{user.fullName || "Unknown"}</span>
                          <span className="text-xs text-muted-foreground truncate">{user.email}</span>
                        </div>
                        <span className="ml-auto text-xs px-2 py-0.5 rounded-full bg-muted capitalize">
                          {user.role}
                        </span>
                        <Check
                          className={cn(
                            "ml-2 h-4 w-4",
                            selectedUsers.includes(user.id) ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </CommandItem>
                    ))}
                  </ScrollArea>
                </CommandGroup>
              </>
            )}
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}