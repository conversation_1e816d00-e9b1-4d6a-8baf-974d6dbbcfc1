import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { 
  LayoutDashboard, FileText, Calendar, CreditCard, 
  CheckCircle, Clock, AlertTriangle, ActivityIcon 
} from "lucide-react";

interface StatsCardProps {
  title: string;
  value: number;
  icon: "pending" | "scheduled" | "completed" | "awaiting-payment" | "total" | "pre-assessment" | "report-writing" | "qa-review";
  change?: {
    value: number;
    type: "increase" | "decrease";
  };
}

export function StatsCard({ title, value, icon, change }: StatsCardProps) {
  const getIcon = () => {
    switch (icon) {
      case "pending":
        return (
          <div className="bg-primary/10 p-3 rounded-full">
            <FileText className="h-8 w-8 text-primary" />
          </div>
        );
      case "scheduled":
        return (
          <div className="bg-blue-100 p-3 rounded-full">
            <Calendar className="h-8 w-8 text-blue-600" />
          </div>
        );
      case "completed":
        return (
          <div className="bg-green-100 p-3 rounded-full">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        );
      case "awaiting-payment":
        return (
          <div className="bg-amber-100 p-3 rounded-full">
            <CreditCard className="h-8 w-8 text-amber-600" />
          </div>
        );
      case "total":
        return (
          <div className="bg-gray-100 p-3 rounded-full">
            <LayoutDashboard className="h-8 w-8 text-gray-600" />
          </div>
        );
      case "pre-assessment":
        return (
          <div className="bg-purple-100 p-3 rounded-full">
            <FileText className="h-8 w-8 text-purple-600" />
          </div>
        );
      case "report-writing":
        return (
          <div className="bg-indigo-100 p-3 rounded-full">
            <Clock className="h-8 w-8 text-indigo-600" />
          </div>
        );
      case "qa-review":
        return (
          <div className="bg-cyan-100 p-3 rounded-full">
            <AlertTriangle className="h-8 w-8 text-cyan-600" />
          </div>
        );
      default:
        return (
          <div className="bg-gray-100 p-3 rounded-full">
            <ActivityIcon className="h-8 w-8 text-gray-600" />
          </div>
        );
    }
  };

  return (
    <Card className="border border-gray-200">
      <CardContent className="p-6">
        <div className="flex items-center">
          {getIcon()}
          <div className="ml-4">
            <h3 className="text-gray-500 text-sm font-medium">{title}</h3>
            <div className="flex items-center">
              <p className="text-2xl font-bold text-gray-800">{value}</p>
              {change && (
                <span className={`ml-2 text-xs font-medium ${change.type === 'increase' ? 'text-green-500' : 'text-red-500'}`}>
                  {change.type === 'increase' ? '+' : '-'}
                  {change.value}%
                </span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
