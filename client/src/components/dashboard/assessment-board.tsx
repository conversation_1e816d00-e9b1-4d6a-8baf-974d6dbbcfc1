import React from "react";
import { AssessmentCard } from "./assessment-card";
import { Assessment } from "@shared/schema";

interface AssessmentsByStatus {
  VerificationPending: Assessment[];
  preAssessment: Assessment[];
  scheduled: Assessment[];
  reportWriting: Assessment[];
}

interface AssessmentBoardProps {
  assessmentsByStatus: AssessmentsByStatus;
  onAssessmentClick: (id: number) => void;
}

export function AssessmentBoard({ assessmentsByStatus, onAssessmentClick }: AssessmentBoardProps) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 overflow-x-auto">
      {/* VerificationPending Column */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-700 mb-2 flex items-center">
          <span className="w-3 h-3 bg-gray-400 rounded-full mr-2"></span>
          Verification Pending
          <span className="ml-auto bg-gray-200 text-gray-700 text-xs font-medium rounded-full px-2 py-0.5">
            {assessmentsByStatus.VerificationPending.length}
          </span>
        </h3>

        <div className="space-y-3">
          {assessmentsByStatus.VerificationPending.map((assessment) => (
            <AssessmentCard
              key={assessment.id}
              assessment={assessment}
              onClick={() => onAssessmentClick(assessment.id)}
            />
          ))}

          {assessmentsByStatus.VerificationPending.length === 0 && (
            <div className="bg-white p-4 rounded-lg shadow border border-gray-200 text-center text-gray-500 text-sm">
              No assessments in this phase
            </div>
          )}
        </div>
      </div>
      
      {/* Pre-Assessment Column */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-700 mb-2 flex items-center">
          <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
          Pre-Assessment
          <span className="ml-auto bg-gray-200 text-gray-700 text-xs font-medium rounded-full px-2 py-0.5">
            {assessmentsByStatus.preAssessment.length}
          </span>
        </h3>
        
        <div className="space-y-3">
          {assessmentsByStatus.preAssessment.map((assessment) => (
            <AssessmentCard 
              key={assessment.id}
              assessment={assessment}
              onClick={() => onAssessmentClick(assessment.id)}
            />
          ))}
          
          {assessmentsByStatus.preAssessment.length === 0 && (
            <div className="bg-white p-4 rounded-lg shadow border border-gray-200 text-center text-gray-500 text-sm">
              No assessments in this phase
            </div>
          )}
        </div>
      </div>
      
      {/* Assessment Column */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-700 mb-2 flex items-center">
          <span className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
          Assessment
          <span className="ml-auto bg-gray-200 text-gray-700 text-xs font-medium rounded-full px-2 py-0.5">
            {assessmentsByStatus.scheduled.length}
          </span>
        </h3>
        
        <div className="space-y-3">
          {assessmentsByStatus.scheduled.map((assessment) => (
            <AssessmentCard 
              key={assessment.id}
              assessment={assessment}
              onClick={() => onAssessmentClick(assessment.id)}
            />
          ))}
          
          {assessmentsByStatus.scheduled.length === 0 && (
            <div className="bg-white p-4 rounded-lg shadow border border-gray-200 text-center text-gray-500 text-sm">
              No assessments in this phase
            </div>
          )}
        </div>
      </div>
      
      {/* Report Column */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-700 mb-2 flex items-center">
          <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
          Report Preparation
          <span className="ml-auto bg-gray-200 text-gray-700 text-xs font-medium rounded-full px-2 py-0.5">
            {assessmentsByStatus.reportWriting.length}
          </span>
        </h3>
        
        <div className="space-y-3">
          {assessmentsByStatus.reportWriting.map((assessment) => (
            <AssessmentCard 
              key={assessment.id}
              assessment={assessment}
              onClick={() => onAssessmentClick(assessment.id)}
            />
          ))}
          
          {assessmentsByStatus.reportWriting.length === 0 && (
            <div className="bg-white p-4 rounded-lg shadow border border-gray-200 text-center text-gray-500 text-sm">
              No assessments in this phase
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
