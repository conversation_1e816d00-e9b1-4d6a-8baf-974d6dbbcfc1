import React from 'react';
import { useDashboard } from './widget-context';
import { getWidgetComponent } from './widget-registry';
import WidgetEditToolbar from './widget-edit-toolbar';
import { User } from "@shared/schema";

interface DashboardGridProps {
  user?: User | null;
}

export default function DashboardGrid({ user }: DashboardGridProps) {
  const { widgets, isEditMode } = useDashboard();
  
  // Filter only visible widgets unless in edit mode
  const visibleWidgets = isEditMode
    ? widgets
    : widgets.filter(widget => widget.visible);

  return (
    <div className="space-y-4">
      <WidgetEditToolbar />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {visibleWidgets.map(widget => (
          <div key={widget.id} className="contents">
            {getWidgetComponent(widget)}
          </div>
        ))}
      </div>
    </div>
  );
}