import React from "react";
import { format } from "date-fns";
import { Assessment } from "@shared/schema";
import { MessageSquare } from "lucide-react";

interface AssessmentCardProps {
  assessment: Assessment;
  onClick: () => void;
}

export function AssessmentCard({ assessment, onClick }: AssessmentCardProps) {
  // Calculate the relative age for the assessee
  const calculateAge = (dateOfBirth: string | Date) => {
    if (!dateOfBirth) return null;
    
    const dob = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }
    
    return age;
  };
  
  // Get the form status for the assessment
  const getFormCompletion = (assessment: Assessment) => {
    if (!assessment.forms) return { completed: 0, total: 0 };
    
    const completedForms = assessment.forms.filter(form => form.status === 'completed').length;
    return { completed: completedForms, total: assessment.forms.length };
  };
  
  const assesseeAge = assessment.assessee ? calculateAge(assessment.assessee.dateOfBirth) : null;
  const formCompletion = getFormCompletion(assessment);
  
  // Format created date
  const createdDate = new Date(assessment.createdAt);
  const formattedDate = format(createdDate, "MMM d, yyyy");
  
  // Format scheduled date if available
  const scheduledDate = assessment.scheduledDate ? format(new Date(assessment.scheduledDate), "MMM d, yyyy - h:mm a") : null;
  
  return (
    <div 
      className="bg-white p-4 rounded-lg shadow border border-gray-200 hover:shadow-md cursor-pointer"
      onClick={onClick}
    >
      <div className="flex justify-between items-start mb-2">
        <span className={`${assesseeAge && assesseeAge < 16 ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'} text-xs font-medium px-2 py-0.5 rounded`}>
          {assesseeAge && assesseeAge < 16 ? 'Under 16' : 'Over 16'}
        </span>
        <span className="text-xs text-gray-500">{formattedDate}</span>
      </div>
      
      <h4 className="font-medium text-gray-800 mb-1">
        {assessment.assessee ? assessment.assessee.fullName : 'Unknown Assessee'}
      </h4>
      
      <p className="text-sm text-gray-600 mb-2">
        {assessment.status === 'VerificationPending' && 'Awaiting form completion'}
        {assessment.status === 'pre_assessment' && 'Forms completed, awaiting scheduling'}
        {assessment.status === 'scheduled' && (
          scheduledDate ? `Scheduled for ${scheduledDate}` : 'Assessment scheduled'
        )}
        {assessment.status === 'assessment_complete' && 'Assessment completed'}
        {assessment.status === 'report_writing' && 'Report in progress'}
        {assessment.status === 'qa_review' && 'Report in QA review'}
        {assessment.status === 'completed' && 'Assessment completed'}
      </p>
      
      {assessment.status === 'VerificationPending' && formCompletion.total > 0 && (
        <div className="flex flex-wrap gap-1 mb-2">
          <span className={`${formCompletion.completed > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'} text-xs px-2 py-0.5 rounded`}>
            Forms: {formCompletion.completed}/{formCompletion.total}
          </span>
        </div>
      )}
      
      <div className="flex justify-between items-center">
        <span className="text-xs text-gray-500">ID: #{assessment.id}</span>
        
        {assessment.paymentStatus === 'unpaid' && (
          <span className="bg-red-100 text-red-800 text-xs px-2 py-0.5 rounded-full">Unpaid</span>
        )}
        
        {assessment.paymentStatus === 'deposit_paid' && (
          <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full">Deposit Paid</span>
        )}
        
        {assessment.paymentStatus === 'fully_paid' && (
          <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Fully Paid</span>
        )}
      </div>
    </div>
  );
}
