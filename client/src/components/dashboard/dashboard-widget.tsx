import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Settings, X, ChevronUp, ChevronDown, Trash2, LayoutGrid, Info, Eye, EyeOff } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { WidgetConfig, useDashboard } from './widget-context';

interface DashboardWidgetProps {
  widget: WidgetConfig;
  children: React.ReactNode;
  description?: string;
}

export default function DashboardWidget({ widget, children, description }: DashboardWidgetProps) {
  const { isEditMode, removeWidget, moveWidgetUp, moveWidgetDown, updateWidget } = useDashboard();

  // Get size class based on widget width
  const getSizeClass = () => {
    switch (widget.width) {
      case 'small':
        return 'col-span-1';
      case 'medium':
        return 'col-span-1 lg:col-span-2';
      case 'large':
        return 'col-span-1 lg:col-span-3';
      default:
        return 'col-span-1';
    }
  };

  // Toggle widget visibility
  const toggleVisibility = () => {
    updateWidget(widget.id, { visible: !widget.visible });
  };

  // Cycle through sizes
  const cycleSize = () => {
    const sizes: ('small' | 'medium' | 'large')[] = ['small', 'medium', 'large'];
    const currentIndex = sizes.indexOf(widget.width);
    const nextSize = sizes[(currentIndex + 1) % sizes.length];
    updateWidget(widget.id, { width: nextSize });
  };

  return (
    <div className={`${getSizeClass()} ${!widget.visible ? 'opacity-50' : ''}`}>
      <Card className="h-full relative transition-all duration-300">
        {isEditMode && (
          <div className="absolute -top-3 -right-2 flex gap-1 z-10">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon"
                    variant="outline"
                    className="h-6 w-6 rounded-full bg-background shadow-md"
                    onClick={() => moveWidgetUp(widget.id)}
                  >
                    <ChevronUp className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Move Up</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon"
                    variant="outline"
                    className="h-6 w-6 rounded-full bg-background shadow-md"
                    onClick={() => moveWidgetDown(widget.id)}
                  >
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Move Down</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon"
                    variant="outline"
                    className="h-6 w-6 rounded-full bg-background shadow-md"
                    onClick={toggleVisibility}
                  >
                    {widget.visible ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{widget.visible ? "Hide Widget" : "Show Widget"}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon"
                    variant="outline"
                    className="h-6 w-6 rounded-full bg-background shadow-md"
                    onClick={cycleSize}
                  >
                    <LayoutGrid className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Change Size</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon"
                    variant="outline"
                    className="h-6 w-6 rounded-full bg-background shadow-md text-destructive"
                    onClick={() => removeWidget(widget.id)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Remove Widget</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}

        <CardHeader className={isEditMode ? "pt-4" : ""}>
          <div className="flex justify-between items-center">
            <CardTitle className="text-sm font-medium">{widget.title}</CardTitle>
            {description && !isEditMode && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6 text-muted-foreground">
                      <Info className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{description}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>

        <CardContent>
          {widget.visible ? children : (
            <div className="flex flex-col items-center justify-center h-24 text-muted-foreground">
              <EyeOff className="h-6 w-6 mb-2" />
              <p className="text-xs">Widget Hidden</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}