import React, { createContext, useContext, useState, useEffect } from 'react';
import { User } from '@shared/schema';

// Define available widget types
export type WidgetType = 
  | 'total-assessments' 
  | 'pending-forms' 
  | 'upcoming-appointments' 
  | 'recent-activity' 
  | 'assessment-status' 
  | 'assessment-chart'
  | 'payment-status';

// Define widget configuration
export interface WidgetConfig {
  id: string;
  type: WidgetType;
  title: string;
  width: 'small' | 'medium' | 'large';
  position: number; // Order in the layout
  visible: boolean;
}

interface DashboardContextType {
  // Widget management
  widgets: WidgetConfig[];
  addWidget: (type: WidgetType) => void;
  removeWidget: (id: string) => void;
  updateWidget: (id: string, config: Partial<WidgetConfig>) => void;
  moveWidgetUp: (id: string) => void;
  moveWidgetDown: (id: string) => void;
  resetToDefault: () => void;
  isEditMode: boolean;
  setEditMode: (mode: boolean) => void;
}

// Default widgets configuration
export const DEFAULT_WIDGETS: WidgetConfig[] = [
  {
    id: 'total-assessments',
    type: 'total-assessments',
    title: 'Total Assessments',
    width: 'small',
    position: 0,
    visible: true
  },
  {
    id: 'pending-forms',
    type: 'pending-forms',
    title: 'Pending Forms',
    width: 'small',
    position: 1,
    visible: true
  },
  {
    id: 'upcoming-appointments',
    type: 'upcoming-appointments',
    title: 'Upcoming Appointments',
    width: 'small',
    position: 2,
    visible: true
  },
  {
    id: 'assessment-chart',
    type: 'assessment-chart',
    title: 'Assessment Activity',
    width: 'medium',
    position: 3,
    visible: true
  },
  {
    id: 'recent-activity',
    type: 'recent-activity',
    title: 'Recent Activity',
    width: 'small',
    position: 4,
    visible: true
  }
];

// Role-specific default widgets
const ADMIN_WIDGETS: WidgetConfig[] = [
  ...DEFAULT_WIDGETS,
  {
    id: 'assessment-status',
    type: 'assessment-status',
    title: 'Assessment Status',
    width: 'medium',
    position: 5,
    visible: true
  }
];

const ASSESSOR_WIDGETS: WidgetConfig[] = [
  ...DEFAULT_WIDGETS,
  {
    id: 'assessment-status',
    type: 'assessment-status',
    title: 'Assessment Status',
    width: 'medium',
    position: 5,
    visible: true
  }
];

const PARENT_WIDGETS: WidgetConfig[] = [
  ...DEFAULT_WIDGETS,
  {
    id: 'payment-status',
    type: 'payment-status',
    title: 'Payment Status',
    width: 'small',
    position: 5,
    visible: true
  }
];

const UNIVERSITY_WIDGETS: WidgetConfig[] = [
  {
    id: 'total-assessments',
    type: 'total-assessments',
    title: 'Total Referrals',
    width: 'small',
    position: 0,
    visible: true
  },
  {
    id: 'assessment-status',
    type: 'assessment-status',
    title: 'Referral Status',
    width: 'medium',
    position: 1,
    visible: true
  },
  {
    id: 'recent-activity',
    type: 'recent-activity',
    title: 'Recent Activity',
    width: 'small',
    position: 2,
    visible: true
  }
];

const SCHOOL_WIDGETS: WidgetConfig[] = [
  {
    id: 'total-assessments',
    type: 'total-assessments',
    title: 'Students in Assessment',
    width: 'small',
    position: 0,
    visible: true
  },
  {
    id: 'pending-forms',
    type: 'pending-forms',
    title: 'School Forms to Complete',
    width: 'small',
    position: 1,
    visible: true
  },
  {
    id: 'assessment-status',
    type: 'assessment-status',
    title: 'Assessment Status',
    width: 'medium',
    position: 2,
    visible: true
  }
];

const ASSESSEE_WIDGETS: WidgetConfig[] = [
  {
    id: 'assessment-status',
    type: 'assessment-status',
    title: 'My Assessment Status',
    width: 'medium',
    position: 0,
    visible: true
  },
  {
    id: 'pending-forms',
    type: 'pending-forms',
    title: 'Forms to Complete',
    width: 'small',
    position: 1,
    visible: true
  },
  {
    id: 'payment-status',
    type: 'payment-status',
    title: 'Payment Status',
    width: 'small',
    position: 2,
    visible: true
  }
];

// Create context
const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

export function DashboardProvider({ 
  children, 
  user 
}: { 
  children: React.ReactNode;
  user?: User | null;
}) {
  const [widgets, setWidgets] = useState<WidgetConfig[]>([]);
  const [isEditMode, setEditMode] = useState(false);

  // Load default widgets based on user role
  useEffect(() => {
    if (user) {
      // Get saved widgets from localStorage if they exist
      const savedWidgets = localStorage.getItem(`a2lexa_dashboard_widgets_${user.id}`);
      
      if (savedWidgets) {
        try {
          setWidgets(JSON.parse(savedWidgets));
          return;
        } catch (error) {
          console.error('Error parsing saved widgets:', error);
          // Continue to default widgets
        }
      }
      
      // Otherwise set default widgets based on role
      switch (user.role) {
        case 'admin':
          setWidgets(ADMIN_WIDGETS);
          break;
        case 'assessor':
          setWidgets(ASSESSOR_WIDGETS);
          break;
        case 'parent':
          setWidgets(PARENT_WIDGETS);
          break;
        case 'university':
          setWidgets(UNIVERSITY_WIDGETS);
          break;
        case 'school':
          setWidgets(SCHOOL_WIDGETS);
          break;
        case 'assessee':
          setWidgets(ASSESSEE_WIDGETS);
          break;
        default:
          setWidgets(DEFAULT_WIDGETS);
      }
    }
  }, [user]);

  // Save widgets to localStorage whenever they change
  useEffect(() => {
    if (user && widgets.length > 0) {
      localStorage.setItem(`a2lexa_dashboard_widgets_${user.id}`, JSON.stringify(widgets));
    }
  }, [widgets, user]);

  // Add a new widget
  const addWidget = (type: WidgetType) => {
    const newWidget: WidgetConfig = {
      id: `${type}-${Date.now()}`,
      type,
      title: type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
      width: 'small',
      position: widgets.length,
      visible: true
    };
    
    setWidgets([...widgets, newWidget]);
  };

  // Remove a widget
  const removeWidget = (id: string) => {
    const newWidgets = widgets.filter(widget => widget.id !== id);
    
    // Reorder positions
    const updatedWidgets = newWidgets.map((widget, index) => ({
      ...widget,
      position: index
    }));
    
    setWidgets(updatedWidgets);
  };

  // Update a widget's configuration
  const updateWidget = (id: string, config: Partial<WidgetConfig>) => {
    setWidgets(widgets.map(widget => 
      widget.id === id ? { ...widget, ...config } : widget
    ));
  };

  // Move a widget up in the order
  const moveWidgetUp = (id: string) => {
    const index = widgets.findIndex(widget => widget.id === id);
    if (index <= 0) return; // Already at the top
    
    const newWidgets = [...widgets];
    // Swap with the widget above
    [newWidgets[index - 1].position, newWidgets[index].position] = 
      [newWidgets[index].position, newWidgets[index - 1].position];
    
    // Sort by position
    setWidgets([...newWidgets.sort((a, b) => a.position - b.position)]);
  };

  // Move a widget down in the order
  const moveWidgetDown = (id: string) => {
    const index = widgets.findIndex(widget => widget.id === id);
    if (index >= widgets.length - 1) return; // Already at the bottom
    
    const newWidgets = [...widgets];
    // Swap with the widget below
    [newWidgets[index + 1].position, newWidgets[index].position] = 
      [newWidgets[index].position, newWidgets[index + 1].position];
    
    // Sort by position
    setWidgets([...newWidgets.sort((a, b) => a.position - b.position)]);
  };

  // Reset to default widgets
  const resetToDefault = () => {
    if (user) {
      switch (user.role) {
        case 'admin':
          setWidgets(ADMIN_WIDGETS);
          break;
        case 'assessor':
          setWidgets(ASSESSOR_WIDGETS);
          break;
        case 'parent':
          setWidgets(PARENT_WIDGETS);
          break;
        case 'university':
          setWidgets(UNIVERSITY_WIDGETS);
          break;
        case 'school':
          setWidgets(SCHOOL_WIDGETS);
          break;
        case 'assessee':
          setWidgets(ASSESSEE_WIDGETS);
          break;
        default:
          setWidgets(DEFAULT_WIDGETS);
      }
    } else {
      setWidgets(DEFAULT_WIDGETS);
    }
  };

  return (
    <DashboardContext.Provider value={{
      widgets,
      addWidget,
      removeWidget,
      updateWidget,
      moveWidgetUp,
      moveWidgetDown,
      resetToDefault,
      isEditMode,
      setEditMode
    }}>
      {children}
    </DashboardContext.Provider>
  );
}

export function useDashboard() {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
}