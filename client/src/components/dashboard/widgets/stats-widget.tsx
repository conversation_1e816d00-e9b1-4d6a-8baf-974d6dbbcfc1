import React from 'react';
import DashboardWidget from '../dashboard-widget';
import { WidgetConfig } from '../widget-context';
import { useQuery } from '@tanstack/react-query';
import { FileCheck, BookA, CalendarDays, FileSearch, PiggyBank } from 'lucide-react';

interface StatsWidgetProps {
  widget: WidgetConfig;
}

export default function StatsWidget({ widget }: StatsWidgetProps) {
  // Stats query – dashboard endpoint returns { stats, activities, assessmentsByStatus }
  const { data } = useQuery<{
    stats: {
      total: number;
      VerificationPending: number;
      preAssessment: number;
      scheduled: number;
      reportWriting: number;
      qaReview: number;
      completed: number;
      awaitingPayment: number;
    };
  }>({
    queryKey: ["/api/dashboard"],
    refetchOnWindowFocus: true,
    staleTime: 30000
  });

  const stats = data?.stats;

  // Widget-specific data
  const getWidgetData = () => {
    switch (widget.type) {
      case 'total-assessments':
        return {
          icon: <FileCheck className="mr-2 h-4 w-4" />,
          value: stats?.total || 0,
          description: stats ? `${stats.VerificationPending + stats.preAssessment + stats.scheduled + stats.reportWriting + stats.qaReview} in progress` : 'Total number of assessments'
        };
      case 'pending-forms':
        return {
          icon: <BookA className="mr-2 h-4 w-4" />,
          value: 0, // Need additional API data for pending forms
          description: 'Forms awaiting completion'
        };
      case 'upcoming-appointments':
        return {
          icon: <CalendarDays className="mr-2 h-4 w-4" />,
          value: stats?.scheduled || 0,
          description: 'In the next 30 days'
        };
      case 'payment-status':
        return {
          icon: <PiggyBank className="mr-2 h-4 w-4" />,
          value: stats?.awaitingPayment || 0,
          description: 'Awaiting payment'
        };
      default:
        return {
          icon: <FileSearch className="mr-2 h-4 w-4" />,
          value: 0,
          description: 'No data available'
        };
    }
  };

  const { icon, value, description } = getWidgetData();

  return (
    <DashboardWidget widget={widget}>
      <div className="flex flex-col space-y-2">
        <div className="flex items-center">
          {icon}
          <h3 className="text-sm font-medium">{widget.title}</h3>
        </div>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </div>
    </DashboardWidget>
  );
}