import React from 'react';
import DashboardWidget from '../dashboard-widget';
import { WidgetConfig } from '../widget-context';
import { useQuery } from '@tanstack/react-query';
import type { AssessmentData } from '@/types';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface ChartWidgetProps {
  widget: WidgetConfig;
}

export default function ChartWidget({ widget }: ChartWidgetProps) {
  // Assessments query for chart data
  const { data: assessments = [] } = useQuery<AssessmentData[]>({
    queryKey: ["/api/assessments"],
    refetchOnWindowFocus: true,
    staleTime: 30000
  });

  // Get the appropriate chart data based on widget type
  const getChartData = () => {
    if (widget.type === 'assessment-chart') {
      // Basic data - could be improved with monthly breakdowns
      return assessments.length > 0 
        ? [{ name: 'Current', assessments: assessments.length }]
        : [{ name: 'No Data', assessments: 0 }];
    }
    
    return [];
  };

  const chartData = getChartData();

  return (
    <DashboardWidget 
      widget={widget} 
      description="Monthly assessment statistics"
    >
      <div className="h-[250px] mt-2">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="assessments" fill="hsl(var(--primary))" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </DashboardWidget>
  );
}