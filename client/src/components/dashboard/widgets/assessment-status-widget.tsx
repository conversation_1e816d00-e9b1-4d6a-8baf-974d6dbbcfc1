import React from 'react';
import DashboardWidget from '../dashboard-widget';
import { WidgetConfig } from '../widget-context';
import { useQuery } from '@tanstack/react-query';
import { Badge } from '@/components/ui/badge';
import { 
  ClipboardList, 
  FileSearch, 
  Calendar, 
  FileText, 
  FileCheck2, 
  CheckCircle2, 
  Coffee 
} from 'lucide-react';

interface AssessmentStatusWidgetProps {
  widget: WidgetConfig;
}

export default function AssessmentStatusWidget({ widget }: AssessmentStatusWidgetProps) {
  // Stats query – dashboard endpoint returns { stats, activities, assessmentsByStatus }
  const { data } = useQuery<{
    stats: {
      total: number;
      VerificationPending: number;
      preAssessment: number;
      scheduled: number;
      reportWriting: number;
      qaReview: number;
      completed: number;
      awaitingPayment: number;
    };
  }>({
    queryKey: ["/api/dashboard"],
    refetchOnWindowFocus: true,
    staleTime: 30000
  });

  const stats = data?.stats;

  // Helper function to get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'VerificationPending':
        return <FileSearch className="h-4 w-4" />;
      case 'pre_assessment':
        return <ClipboardList className="h-4 w-4" />;
      case 'scheduled':
        return <Calendar className="h-4 w-4" />;
      case 'report_writing':
        return <FileText className="h-4 w-4" />;
      case 'qa_review':
        return <FileCheck2 className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle2 className="h-4 w-4" />;
      default:
        return <Coffee className="h-4 w-4" />;
    }
  };

  // Helper function for status colors
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'VerificationPending':
        return 'bg-blue-100 text-blue-800';
      case 'pre_assessment':
        return 'bg-purple-100 text-purple-800';
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'report_writing':
        return 'bg-orange-100 text-orange-800';
      case 'qa_review':
        return 'bg-indigo-100 text-indigo-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Status counts
  const statusCounts = stats ? [
    { status: 'VerificationPending', count: stats.VerificationPending || 0, label: 'Verification Pending' },
    { status: 'pre_assessment', count: stats.preAssessment || 0, label: 'Pre-Assessment' },
    { status: 'scheduled', count: stats.scheduled || 0, label: 'Scheduled' },
    { status: 'report_writing', count: stats.reportWriting || 0, label: 'Report Writing' },
    { status: 'qa_review', count: stats.qaReview || 0, label: 'QA Review' },
    { status: 'completed', count: stats.completed || 0, label: 'Completed' }
  ] : [];

  return (
    <DashboardWidget 
      widget={widget} 
      description="Current status of all assessments"
    >
      <div className="space-y-3">
        {statusCounts.length > 0 ? (
          statusCounts.map((item) => (
            <div key={item.status} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getStatusIcon(item.status)}
                <span className="text-sm">{item.label}</span>
              </div>
              <Badge variant="outline" className={`${getStatusColor(item.status)} font-bold`}>
                {item.count}
              </Badge>
            </div>
          ))
        ) : (
          <div className="text-center text-muted-foreground py-4">
            <ClipboardList className="h-6 w-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No assessment data available</p>
          </div>
        )}
      </div>
    </DashboardWidget>
  );
}