import React from 'react';
import DashboardWidget from '../dashboard-widget';
import { WidgetConfig } from '../widget-context';
import { useQuery } from '@tanstack/react-query';
import { Activity } from 'lucide-react';
import type { Activity as ActivityType } from '@shared/schema';

interface RecentActivityWidgetProps {
  widget: WidgetConfig;
}

export default function RecentActivityWidget({ widget }: RecentActivityWidgetProps) {
  // Recent activities query
  const { data: recentActivities = [] } = useQuery<ActivityType[]>({
    queryKey: ["/api/activities/recent"],
    refetchOnWindowFocus: true,
    staleTime: 30000
  });

  // Format activity names for better readability
  const formatActivityName = (action: string) => {
    // Handle specific action types with more user-friendly names
    switch(action) {
      case 'approve_referral':
        return 'Referral Approved';
      case 'university_referral_created':
        return 'University Referral Created';
      case 'individual_referral_created':
        return 'Individual Referral Created';
      case 'school_referral_created':
        return 'School Referral Created';
      case 'create_assessment':
        return 'Assessment Created';
      case 'update_assessment':
        return 'Assessment Updated';
      case 'update_status':
        return 'Status Updated';
      case 'payment_received':
        return 'Payment Received';
      case 'assessment_scheduled':
        return 'Assessment Scheduled';
      case 'report_complete':
        return 'Report Completed';
      case 'payment_reminder':
        return 'Payment Reminder Sent';
      case 'form_completed':
        return 'Form Completed';
      case 'document_uploaded':
        return 'Document Uploaded';
      case 'reject_referral':
        return 'Referral Rejected';
      default:
        // Convert snake_case to Space Separated Title Case for other actions
        return action
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
    }
  };

  return (
    <DashboardWidget 
      widget={widget} 
      description="Latest activities in the platform"
    >
      <div className="space-y-4">
        {recentActivities.length > 0 ? (
          recentActivities.slice(0, 5).map((activity: ActivityType) => (
            <div key={activity.id} className="flex items-start space-x-3">
              <div className="w-2 h-2 mt-1 bg-primary rounded-full" />
              <div>
                <p className="text-sm font-medium">
                  {formatActivityName(activity.action)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {new Date(activity.createdAt).toLocaleString()}
                </p>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-muted-foreground py-4">
            <Activity className="h-6 w-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No recent activity</p>
          </div>
        )}
      </div>
    </DashboardWidget>
  );
}