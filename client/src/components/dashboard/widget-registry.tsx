import React from 'react';
import { WidgetConfig, WidgetType } from './widget-context';
import StatsWidget from './widgets/stats-widget';
import ChartWidget from './widgets/chart-widget';
import RecentActivityWidget from './widgets/recent-activity-widget';
import AssessmentStatusWidget from './widgets/assessment-status-widget';

// Component registry for widget types
export function getWidgetComponent(widget: WidgetConfig): React.ReactNode {
  switch (widget.type) {
    case 'total-assessments':
    case 'pending-forms':
    case 'upcoming-appointments':
    case 'payment-status':
      return <StatsWidget widget={widget} />;
    
    case 'assessment-chart':
      return <ChartWidget widget={widget} />;
    
    case 'recent-activity':
      return <RecentActivityWidget widget={widget} />;
    
    case 'assessment-status':
      return <AssessmentStatusWidget widget={widget} />;
    
    default:
      console.warn(`Unknown widget type: ${widget.type}`);
      return (
        <div className="p-4 border rounded-md">
          <p>Unknown widget type: {widget.type}</p>
        </div>
      );
  }
}