import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { format, isToday, isYesterday } from "date-fns";
import { CheckCircle, CreditCard, Calendar, Clock, FileText, AlertCircle } from "lucide-react";

interface Activity {
  id: number;
  createdAt: Date;
  action: string;
  details: string;
  user: {
    fullName: string;
  };
  assessment?: {
    id: number;
    assessee?: {
      fullName: string;
    };
  };
}

interface ActivityFeedProps {
  activities: Activity[];
  onViewAll?: () => void;
}

export function ActivityFeed({ activities, onViewAll }: ActivityFeedProps) {
  const formatDate = (date: Date) => {
    if (isToday(date)) {
      return `Today at ${format(date, "h:mm a")}`;
    } else if (isYesterday(date)) {
      return `Yesterday at ${format(date, "h:mm a")}`;
    } else {
      return format(date, "MMM d, yyyy");
    }
  };

  const getActivityIcon = (action: string) => {
    switch (action) {
      case "create_assessment":
      case "update_assessment":
        return (
          <div className="bg-blue-100 p-2 rounded-full">
            <FileText className="h-5 w-5 text-blue-800" />
          </div>
        );
      case "update_status":
        return (
          <div className="bg-indigo-100 p-2 rounded-full">
            <Clock className="h-5 w-5 text-indigo-800" />
          </div>
        );
      case "payment_received":
        return (
          <div className="bg-green-100 p-2 rounded-full">
            <CreditCard className="h-5 w-5 text-green-800" />
          </div>
        );
      case "assessment_scheduled":
        return (
          <div className="bg-indigo-100 p-2 rounded-full">
            <Calendar className="h-5 w-5 text-indigo-800" />
          </div>
        );
      case "report_complete":
        return (
          <div className="bg-purple-100 p-2 rounded-full">
            <CheckCircle className="h-5 w-5 text-purple-800" />
          </div>
        );
      case "payment_reminder":
        return (
          <div className="bg-yellow-100 p-2 rounded-full">
            <AlertCircle className="h-5 w-5 text-yellow-800" />
          </div>
        );
      default:
        return (
          <div className="bg-gray-100 p-2 rounded-full">
            <FileText className="h-5 w-5 text-gray-800" />
          </div>
        );
    }
  };

  function formatActionType(action: string): string {
    // Handle specific action types with more user-friendly names
    switch(action) {
      case 'approve_referral':
        return 'Referral Approved';
      case 'university_referral_created':
        return 'University Referral Created';
      case 'individual_referral_created':
        return 'Individual Referral Created';
      case 'school_referral_created':
        return 'School Referral Created';
      case 'create_assessment':
        return 'Assessment Created';
      case 'update_assessment':
        return 'Assessment Updated';
      case 'update_status':
        return 'Status Updated';
      case 'payment_received':
        return 'Payment Received';
      case 'assessment_scheduled':
        return 'Assessment Scheduled';
      case 'report_complete':
        return 'Report Completed';
      case 'payment_reminder':
        return 'Payment Reminder Sent';
      case 'form_completed':
        return 'Form Completed';
      case 'document_uploaded':
        return 'Document Uploaded';
      case 'reject_referral':
        return 'Referral Rejected';
      default:
        // Convert snake_case to Space Separated Title Case for other actions
        return action
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
    }
  }

  return (
    <Card className="bg-white rounded-lg shadow border border-gray-200">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl font-bold text-gray-800">Recent Activity</CardTitle>
        {onViewAll && (
          <button 
            onClick={onViewAll}
            className="text-primary text-sm font-medium"
          >
            View All
          </button>
        )}
      </CardHeader>

      <CardContent className="pt-2">
        <div className="space-y-4">
          {activities.length > 0 ? (
            activities.map((activity) => (
              <div key={activity.id} className="flex items-start">
                {getActivityIcon(activity.action)}
                <div className="ml-3">
                  <p className="text-sm text-gray-800">
                    <span className="font-medium">{activity.user?.fullName || 'Unknown User'}</span>
                    {activity.assessment?.assessee && (
                      <>
                        {" "}
                        {formatActionType(activity.action)}
                        {" "}
                        <span className="font-medium">{activity.assessment.assessee?.fullName || 'Unknown Assessee'}</span>
                      </>
                    )}
                    {activity.details && ` - ${activity.details}`}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">{formatDate(new Date(activity.createdAt))}</p>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-6 text-gray-500">
              No recent activity to display
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}