import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  LayoutPanelTop, 
  Save, 
  RotateCcw, 
  Plus, 
  X,
  BarChart3, 
  Activity, 
  FileCheck, 
  CheckSquare, 
  Calendar, 
  PiggyBank
} from "lucide-react";
import { useDashboard, WidgetType } from './widget-context';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function WidgetEditToolbar() {
  const { 
    isEditMode, 
    setEditMode, 
    resetToDefault, 
    addWidget 
  } = useDashboard();

  const startEditing = () => setEditMode(true);
  const stopEditing = () => setEditMode(false);

  // Available widgets to add
  const availableWidgets: {type: WidgetType, icon: React.ReactNode, label: string}[] = [
    { type: 'total-assessments', icon: <FileCheck className="h-4 w-4 mr-2" />, label: 'Total Assessments' },
    { type: 'pending-forms', icon: <CheckSquare className="h-4 w-4 mr-2" />, label: 'Pending Forms' },
    { type: 'upcoming-appointments', icon: <Calendar className="h-4 w-4 mr-2" />, label: 'Upcoming Appointments' },
    { type: 'assessment-chart', icon: <BarChart3 className="h-4 w-4 mr-2" />, label: 'Assessment Chart' },
    { type: 'recent-activity', icon: <Activity className="h-4 w-4 mr-2" />, label: 'Recent Activity' },
    { type: 'assessment-status', icon: <LayoutPanelTop className="h-4 w-4 mr-2" />, label: 'Assessment Status' },
    { type: 'payment-status', icon: <PiggyBank className="h-4 w-4 mr-2" />, label: 'Payment Status' },
  ];

  return (
    <div className="flex justify-between items-center mb-4">
      {isEditMode ? (
        <div className="bg-muted p-2 rounded-lg shadow-sm flex items-center space-x-2 w-full">
          <span className="text-sm font-medium ml-2 mr-2">Edit Dashboard</span>
          <Separator orientation="vertical" className="h-6" />
          
          <Popover>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <PopoverTrigger asChild>
                    <Button variant="secondary" size="sm" className="gap-1">
                      <Plus className="h-4 w-4" />
                      Add Widget
                    </Button>
                  </PopoverTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Add a new widget to your dashboard</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <PopoverContent className="w-64 p-2" align="start">
              <div className="space-y-1">
                <h4 className="font-medium text-sm mb-2">Add Widget</h4>
                {availableWidgets.map((widget) => (
                  <Button
                    key={widget.type}
                    variant="ghost"
                    className="w-full justify-start text-sm h-8"
                    onClick={() => addWidget(widget.type)}
                  >
                    {widget.icon}
                    {widget.label}
                  </Button>
                ))}
              </div>
            </PopoverContent>
          </Popover>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={resetToDefault}>
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Reset
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Reset to default dashboard layout</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="flex-1"></div>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="default" size="sm" onClick={stopEditing}>
                  <Save className="h-4 w-4 mr-1" />
                  Save Layout
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Save dashboard layout</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" onClick={stopEditing}>
                  <X className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Cancel editing</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ) : (
        <Button
          variant="outline"
          size="sm"
          onClick={startEditing}
          className="ml-auto"
        >
          <LayoutPanelTop className="h-4 w-4 mr-2" />
          Customize Dashboard
        </Button>
      )}
    </div>
  );
}