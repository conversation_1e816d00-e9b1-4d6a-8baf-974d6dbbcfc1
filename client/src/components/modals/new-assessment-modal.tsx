import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useMutation } from "@tanstack/react-query";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { format } from "date-fns";
import { User } from "@shared/schema";

// Define schema for the new assessment form
const newAssessmentSchema = z.object({
  // Basic info step
  fullName: z.string().min(2, "Name must be at least 2 characters"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  assessmentType: z.enum(["under_16", "over_16"]),
  email: z.string().email("Please enter a valid email"),
  phone: z.string().min(5, "Please enter a valid phone number"),
  referralType: z.enum(["individual", "school", "university"]),
  notes: z.string().optional(),
  
  // Additional information step
  schoolName: z.string().optional(),
  schoolContactName: z.string().optional(),
  schoolEmail: z.string().optional(),
  schoolPhone: z.string().optional(),
  address: z.string().optional(),
  referringUserId: z.number().optional(),
  
  // Payment step
  depositAmount: z.number().min(1, "Deposit amount is required"),
  finalAmount: z.number().min(1, "Final amount is required"),
  acceptTerms: z.literal(true, {
    errorMap: () => ({ message: "You must accept the terms and conditions" }),
  }),
  
  // Confirmation step
  sendEmailNotification: z.boolean().default(true),
  sendFormLinks: z.boolean().default(true),
});

type NewAssessmentFormValues = z.infer<typeof newAssessmentSchema>;

interface NewAssessmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  assessors?: User[];
}

export function NewAssessmentModal({ isOpen, onClose, assessors = [] }: NewAssessmentModalProps) {
  const [step, setStep] = useState(1);
  const { toast } = useToast();
  
  const form = useForm<NewAssessmentFormValues>({
    resolver: zodResolver(newAssessmentSchema),
    defaultValues: {
      assessmentType: "under_16",
      referralType: "individual",
      depositAmount: 125,
      finalAmount: 275,
      acceptTerms: false,
      sendEmailNotification: true,
      sendFormLinks: true,
    },
  });
  
  const createAssessmentMutation = useMutation({
    mutationFn: async (data: NewAssessmentFormValues) => {
      // First create the assessee
      const assesseeRes = await apiRequest("POST", "/api/assessees", {
        fullName: data.fullName,
        dateOfBirth: new Date(data.dateOfBirth),
        email: data.email,
        phone: data.phone,
        address: data.address,
        // If school info provided, link to school
        ...(data.schoolName && data.schoolEmail ? { 
          schoolId: null, // This would be filled in by the backend
        } : {})
      });
      
      const assessee = await assesseeRes.json();
      
      // Then create the assessment
      const assessmentRes = await apiRequest("POST", "/api/assessments", {
        assesseeId: assessee.id,
        referralType: data.referralType,
        referringUserId: data.referringUserId,
        status: "VerificationPending",
        paymentStatus: "unpaid",
        depositAmount: data.depositAmount,
        finalAmount: data.finalAmount,
        notes: data.notes
      });
      
      return await assessmentRes.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/assessments"] });
      queryClient.invalidateQueries({ queryKey: ["/api/dashboard"] });
      toast({
        title: "Assessment created",
        description: "The new assessment has been created successfully.",
      });
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create assessment",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  const onSubmit = (values: NewAssessmentFormValues) => {
    createAssessmentMutation.mutate(values);
  };
  
  const nextStep = () => {
    setStep(current => Math.min(current + 1, 4));
  };
  
  const prevStep = () => {
    setStep(current => Math.max(current - 1, 1));
  };
  
  const handleClose = () => {
    setStep(1);
    form.reset();
    onClose();
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-gray-800">New Assessment</DialogTitle>
        </DialogHeader>
        
        <div className="mb-6">
          <div className="flex items-center">
            <div className="flex items-center text-white relative">
              <div className={`rounded-full transition duration-500 ease-in-out h-12 w-12 py-3 border-2 ${
                step >= 1 ? "bg-primary border-primary" : "bg-gray-200 border-gray-300"
              }`}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div className="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium text-primary">
                Basic Info
              </div>
            </div>
            <div className={`flex-auto border-t-2 transition duration-500 ease-in-out ${
              step >= 2 ? "border-primary" : "border-gray-300"
            }`}></div>
            
            <div className="flex items-center text-white relative">
              <div className={`rounded-full transition duration-500 ease-in-out h-12 w-12 py-3 border-2 ${
                step >= 2 ? "bg-primary border-primary" : "bg-gray-200 border-gray-300"
              }`}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <div className="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium text-gray-500">
                Additional Info
              </div>
            </div>
            <div className={`flex-auto border-t-2 transition duration-500 ease-in-out ${
              step >= 3 ? "border-primary" : "border-gray-300"
            }`}></div>
            
            <div className="flex items-center text-white relative">
              <div className={`rounded-full transition duration-500 ease-in-out h-12 w-12 py-3 border-2 ${
                step >= 3 ? "bg-primary border-primary" : "bg-gray-200 border-gray-300"
              }`}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium text-gray-500">
                Payment
              </div>
            </div>
            <div className={`flex-auto border-t-2 transition duration-500 ease-in-out ${
              step >= 4 ? "border-primary" : "border-gray-300"
            }`}></div>
            
            <div className="flex items-center text-white relative">
              <div className={`rounded-full transition duration-500 ease-in-out h-12 w-12 py-3 border-2 ${
                step >= 4 ? "bg-primary border-primary" : "bg-gray-200 border-gray-300"
              }`}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="absolute top-0 -ml-10 text-center mt-16 w-32 text-xs font-medium text-gray-500">
                Confirm
              </div>
            </div>
          </div>
        </div>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {step === 1 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Assessee Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Full name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="dateOfBirth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date of Birth</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="assessmentType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Assessment Type</FormLabel>
                      <div className="mt-1 space-y-2">
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-1"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="under_16" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Under 16 Assessment (Parent/Guardian completes forms)
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="over_16" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Over 16 Assessment (Assessee completes forms)
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Email</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Email address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Phone</FormLabel>
                        <FormControl>
                          <Input type="tel" placeholder="Phone number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="referralType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Referral Source</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select referral source" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="individual">Individual Referral</SelectItem>
                          <SelectItem value="school">School Referral</SelectItem>
                          <SelectItem value="university">University Referral</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Any additional information about the assessment"
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
            
            {step === 2 && (
              <div className="space-y-4">
                {form.watch("referralType") === "school" && (
                  <>
                    <FormField
                      control={form.control}
                      name="schoolName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>School Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Name of school" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="schoolContactName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Contact Person</FormLabel>
                            <FormControl>
                              <Input placeholder="Name of contact at school" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="schoolEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Email</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="School email address" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormField
                      control={form.control}
                      name="schoolPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>School Phone</FormLabel>
                          <FormControl>
                            <Input type="tel" placeholder="School phone number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
                
                {form.watch("referralType") === "university" && (
                  <FormField
                    control={form.control}
                    name="referringUserId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Referring University</FormLabel>
                        <Select 
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          value={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select university" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {/* This would be populated with university users */}
                            <SelectItem value="1">University of Example</SelectItem>
                            <SelectItem value="2">Sample University</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Full address including postcode"
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
            
            {step === 3 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="depositAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Deposit Amount (£)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            placeholder="e.g. 125"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          Initial deposit required to schedule the assessment
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="finalAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Final Payment Amount (£)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            placeholder="e.g. 275" 
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          Balance due before report is provided
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="acceptTerms"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          I confirm that payment terms have been explained to the client
                        </FormLabel>
                        <FormDescription>
                          The client understands that a deposit of £{form.watch("depositAmount")} is required to book the assessment, and the remaining £{form.watch("finalAmount")} must be paid before the report is issued.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            )}
            
            {step === 4 && (
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium text-gray-900 mb-2">Assessment Summary</h3>
                  
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-gray-500">Name:</div>
                    <div className="font-medium">{form.watch("fullName")}</div>
                    
                    <div className="text-gray-500">Date of Birth:</div>
                    <div className="font-medium">
                      {form.watch("dateOfBirth") ? format(new Date(form.watch("dateOfBirth")), "dd/MM/yyyy") : ""}
                    </div>
                    
                    <div className="text-gray-500">Type:</div>
                    <div className="font-medium">
                      {form.watch("assessmentType") === "under_16" ? "Under 16 Assessment" : "Over 16 Assessment"}
                    </div>
                    
                    <div className="text-gray-500">Email:</div>
                    <div className="font-medium">{form.watch("email")}</div>
                    
                    <div className="text-gray-500">Phone:</div>
                    <div className="font-medium">{form.watch("phone")}</div>
                    
                    <div className="text-gray-500">Referral:</div>
                    <div className="font-medium capitalize">{form.watch("referralType")} Referral</div>
                    
                    <div className="text-gray-500">Total Cost:</div>
                    <div className="font-medium">£{(form.watch("depositAmount") || 0) + (form.watch("finalAmount") || 0)}</div>
                  </div>
                </div>
                
                <FormField
                  control={form.control}
                  name="sendEmailNotification"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Send email notification to client
                        </FormLabel>
                        <FormDescription>
                          An email will be sent to {form.watch("email")} with assessment details
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="sendFormLinks"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Include form links in notification
                        </FormLabel>
                        <FormDescription>
                          Links to required assessment forms will be included in the email
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            )}
            
            <DialogFooter className="pt-4">
              {step > 1 && (
                <Button
                  type="button"
                  variant="outline" 
                  onClick={prevStep}
                >
                  Previous
                </Button>
              )}
              
              {step < 4 ? (
                <Button type="button" onClick={nextStep}>
                  Next Step
                </Button>
              ) : (
                <Button 
                  type="submit" 
                  disabled={createAssessmentMutation.isPending}
                >
                  {createAssessmentMutation.isPending ? "Creating..." : "Create Assessment"}
                </Button>
              )}
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
