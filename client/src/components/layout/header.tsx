import { useAuth } from "@/hooks/use-auth";
import { HelpCircle, Menu } from "lucide-react";
import { NotificationIndicator } from "@/components/notification-indicator";

interface HeaderProps {
  title: string;
  onMenuToggle: () => void;
}

export function Header({ title, onMenuToggle }: HeaderProps) {
  const { user } = useAuth();
  
  if (!user) return null;
  
  return (
    <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-4">
          <button 
            className="md:hidden text-gray-500 focus:outline-none"
            onClick={onMenuToggle}
          >
            <Menu className="h-6 w-6" />
          </button>
          <h1 className="text-xl font-semibold text-gray-800">{title}</h1>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <div className="relative">
            <NotificationIndicator />
          </div>
          
          {/* Help */}
          <button className="p-1 text-gray-500 rounded-full hover:bg-gray-100 focus:outline-none">
            <HelpCircle className="h-6 w-6" />
          </button>
        </div>
      </div>
    </div>
  );
}
