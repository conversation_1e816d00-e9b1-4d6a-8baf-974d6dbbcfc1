import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
 
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
 
interface DatePickerProps {
  date: Date | undefined
  setDate: (date: Date) => void
  isBirthDate?: boolean
}

export function DatePicker({ date, setDate, isBirthDate = false }: DatePickerProps) {
  const [month, setMonth] = React.useState<number>(date ? date.getMonth() : new Date().getMonth())
  const [year, setYear] = React.useState<number>(date ? date.getFullYear() : new Date().getFullYear())
  const [open, setOpen] = React.useState(false)
  
  // Generate a range of years for selection (100 years in the past for birth dates)
  const currentYear = new Date().getFullYear()
  const startYear = isBirthDate ? currentYear - 100 : currentYear - 10
  const endYear = isBirthDate ? currentYear : currentYear + 10
  const years = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i).reverse()
  
  // Generate months
  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ]
  
  // Update the calendar when month or year changes
  React.useEffect(() => {
    if (date) {
      setMonth(date.getMonth())
      setYear(date.getFullYear())
    }
  }, [date])
  
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground"
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "PPP") : <span>Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        {isBirthDate && (
          <div className="flex justify-between p-3 border-b">
            <Select 
              value={month.toString()} 
              onValueChange={(value) => setMonth(parseInt(value))}
            >
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Month" />
              </SelectTrigger>
              <SelectContent>
                {months.map((month, index) => (
                  <SelectItem key={index} value={index.toString()}>
                    {month}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select 
              value={year.toString()} 
              onValueChange={(value) => setYear(parseInt(value))}
            >
              <SelectTrigger className="w-[90px]">
                <SelectValue placeholder="Year" />
              </SelectTrigger>
              <SelectContent className="max-h-[200px]">
                {years.map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        <Calendar
          mode="single"
          selected={date}
          onSelect={(date) => {
            if (date) {
              setDate(date)
              setOpen(false)
            }
          }}
          month={new Date(year, month)}
          onMonthChange={(date) => {
            setMonth(date.getMonth())
            setYear(date.getFullYear())
          }}
          initialFocus
          captionLayout={isBirthDate ? "buttons" : "dropdown"}
          fromYear={startYear}
          toYear={endYear}
        />
      </PopoverContent>
    </Popover>
  )
}