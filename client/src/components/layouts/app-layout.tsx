/**
 * Main Application Layout Component
 * 
 * This component provides the overall layout structure for the application, including:
 * - Responsive sidebar with navigation links
 * - Mobile-friendly drawer navigation
 * - User profile display
 * - Logout functionality
 * 
 * All authenticated pages should be wrapped in this layout component to maintain
 * consistent UI and navigation across the application.
 */

import React, { useState } from 'react';
import { Link, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { User } from '@shared/schema';
import { 
  LayoutDashboard, 
  FileSpreadsheet, 
  Calendar, 
  FileText, 
  Settings, 
  LogOut, 
  BarChart, 
  Menu, 
  Users, 
  CreditCard,
  MessageSquare,
  Bell,
  AlertTriangle
} from 'lucide-react';
import { useNotifications } from '@/hooks/use-notifications';
import { useIsMobile } from '@/hooks/use-mobile';

/**
 * Navigation Link Component
 * 
 * This component renders a navigation link in the sidebar with:
 * - Visual indication of active state
 * - Icon and label
 * - Click handler for mobile navigation closing
 */
type NavLinkProps = {
  href: string;        // Route path for the link
  label: string;       // Display text for the link
  icon: React.ReactNode; // Icon component to display beside the label
  onClick?: () => void; // Optional click handler (used for closing mobile sidebar)
  useNotificationBadge?: boolean; // Whether to show notification badge (for Communication Center)
};

const NavLink = ({ href, label, icon, onClick }: NavLinkProps) => {
  const [location] = useLocation();
  const isActive = location === href;
  
  return (
    <Link href={href}>
      <div 
        className={`flex items-center py-2 px-3 rounded-md text-sm group cursor-pointer ${
          isActive 
            ? 'bg-primary/10 text-primary' 
            : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
        }`}
        onClick={onClick}
      >
        <span className="mr-2">{icon}</span>
        {label}
      </div>
    </Link>
  );
};

// Navigation link with notification badge for Communication Center
const NavLinkWithBadge = ({ href, label, icon, onClick }: NavLinkProps) => {
  const [location] = useLocation();
  const isActive = location === href;
  const { 
    unreadCount, 
    isLoadingUnreadCount, 
    refetchUnreadCount,
    setupNotificationPolling 
  } = useNotifications();
  
  // Set up polling for notification count to keep the badge updated
  React.useEffect(() => {
    // Initial fetch
    refetchUnreadCount();
    
    // Set up polling every 30 seconds
    const cleanup = setupNotificationPolling(30000);
    
    return () => {
      cleanup();
    };
  }, [refetchUnreadCount, setupNotificationPolling]);
  
  return (
    <Link href={href}>
      <div 
        className={`flex items-center py-2 px-3 rounded-md text-sm group cursor-pointer ${
          isActive 
            ? 'bg-primary/10 text-primary' 
            : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
        }`}
        onClick={onClick}
      >
        <span className="mr-2">
          {icon}
        </span>
        <span className="flex items-center">
          {label}
          {!isLoadingUnreadCount && unreadCount !== undefined && (
            <span className={`ml-2 text-[10px] rounded-full min-w-[16px] h-[16px] flex items-center justify-center px-1 ${
              unreadCount > 0 
                ? "bg-destructive text-destructive-foreground" 
                : "bg-muted text-muted-foreground"
            }`}>
              {unreadCount > 99 ? "99+" : unreadCount}
            </span>
          )}
        </span>
      </div>
    </Link>
  );
};

/**
 * Props interface for the AppLayout component
 */
type AppLayoutProps = {
  children: React.ReactNode;     // Page content to render inside the layout
  user?: User | null;            // Current authenticated user or null if not logged in
  logout?: () => Promise<void>;  // Logout function to call when user clicks the logout button
};

export default function AppLayout({ children, user, logout }: AppLayoutProps) {
  const isMobile = useIsMobile();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  const handleLogout = async () => {
    if (logout) {
      try {
        await logout();
      } catch (error) {
        console.error('Logout error:', error);
      }
    } else {
      // Fallback if no logout function provided
      console.warn('Logout function not provided to AppLayout');
    }
  };
  
  const closeSidebar = () => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  };
  
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Define the navigation item type
  type NavigationItem = {
    href: string;
    label: string;
    icon: React.ReactNode;
    useNotificationBadge?: boolean;
  };

  // Create the navigation array
  const navigation: NavigationItem[] = [
    { href: '/', label: 'Dashboard', icon: <LayoutDashboard className="h-4 w-4" /> },
    { href: '/assessments', label: 'Assessments', icon: <FileSpreadsheet className="h-4 w-4" /> },
    { href: '/calendar', label: 'Calendar', icon: <Calendar className="h-4 w-4" /> },
    { href: '/reports', label: 'Reports', icon: <BarChart className="h-4 w-4" /> },
    { href: '/documents', label: 'Documents', icon: <FileText className="h-4 w-4" /> },
    { href: '/payments', label: 'Payments', icon: <CreditCard className="h-4 w-4" /> },
  ];
  
  // Only show admin items to admins
  if (user?.role === 'admin') {
    navigation.push({ href: '/admin', label: 'Admin Dashboard', icon: <LayoutDashboard className="h-4 w-4" /> });
    navigation.push({ href: '/admin/all-referrals', label: 'All Referrals', icon: <FileText className="h-4 w-4" /> });
    navigation.push({ href: '/users', label: 'User Management', icon: <Users className="h-4 w-4" /> });
  } 
  // Show Users link to assessors only (admins get it above)
  else if (user?.role === 'assessor') {
    navigation.push({ href: '/users', label: 'Users', icon: <Users className="h-4 w-4" /> });
  }
  
  // Add Issues link for admin and assessor only
  if (user?.role === 'admin' || user?.role === 'assessor') {
    navigation.push({ href: '/issues', label: 'Issues', icon: <AlertTriangle className="h-4 w-4" /> });
  }
  
  // Add Conversations link before Settings
  navigation.push({ href: '/communication-center', label: 'Conversations', icon: <MessageSquare className="h-4 w-4" />, useNotificationBadge: true });
  
  // Add Settings link (always last)
  navigation.push({ href: '/settings', label: 'Settings', icon: <Settings className="h-4 w-4" /> });

  const sidebarContent = (
    <div className="h-full flex flex-col">
      <div className="p-4">
        <Link href="/">
          <div className="flex items-center gap-2 cursor-pointer">
            <div className="bg-primary h-8 w-8 rounded-md flex items-center justify-center text-primary-foreground font-bold">
              NE
            </div>
            <span className="font-bold text-lg">NeuroElevate</span>
          </div>
        </Link>
      </div>
      
      <Separator />
      
      <div className="flex-1 p-4 space-y-1">
        {navigation.map((item) => 
          item.useNotificationBadge ? (
            <NavLinkWithBadge
              key={item.href}
              href={item.href}
              label={item.label}
              icon={item.icon}
              onClick={closeSidebar}
            />
          ) : (
            <NavLink
              key={item.href}
              href={item.href}
              label={item.label}
              icon={item.icon}
              onClick={closeSidebar}
            />
          )
        )}
      </div>
      
      <Separator />
      
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Avatar>
              <AvatarFallback>{user?.fullName ? getInitials(user.fullName) : 'U'}</AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">{user?.fullName || 'Unknown User'}</p>
              <p className="text-xs text-muted-foreground capitalize">{user?.role || 'unknown'}</p>
            </div>
          </div>
          <Button variant="ghost" size="icon" onClick={handleLogout}>
            <LogOut className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex min-h-screen">
      {isMobile ? (
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="fixed top-4 left-4 z-50">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-64">
            {sidebarContent}
          </SheetContent>
        </Sheet>
      ) : (
        <div className="w-64 border-r h-screen sticky top-0">
          {sidebarContent}
        </div>
      )}
      
      <div className="flex-1">
        <main className={`p-4 md:p-8 ${isMobile ? 'pt-16' : ''}`}>
          {children}
        </main>
      </div>
    </div>
  );
}