import { useEffect } from "react";
import { Bell } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useNotifications } from "@/hooks/use-notifications";
import { Link } from "wouter";

export function NotificationIndicator() {
  const { 
    unreadCount, 
    isLoadingUnreadCount, 
    refetchUnreadCount, 
    setupNotificationPolling 
  } = useNotifications();

  // Set up polling for new notifications
  useEffect(() => {
    // Initial fetch
    refetchUnreadCount();
    
    // Set up polling
    const cleanup = setupNotificationPolling(30000); // Check every 30 seconds
    
    return () => {
      cleanup();
    };
  }, [refetchUnreadCount, setupNotificationPolling]);

  return (
    <Link href="/communication-center">
      <a className="relative inline-flex items-center justify-center p-2 rounded-full hover:bg-primary/10 transition-colors">
        <Bell className="h-5 w-5" />
        {!isLoadingUnreadCount && unreadCount && unreadCount > 0 && (
          <Badge variant="destructive" className="absolute -top-1 -right-1 px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center text-[10px]">
            {unreadCount > 99 ? "99+" : unreadCount}
          </Badge>
        )}
      </a>
    </Link>
  );
}