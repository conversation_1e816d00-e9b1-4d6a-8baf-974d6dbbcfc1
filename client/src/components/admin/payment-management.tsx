import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { PoundSterling, Calendar } from "lucide-react";

interface Assessment {
  id: number;
  paymentStatus: 'unpaid' | 'deposit_paid' | 'fully_paid';
  depositAmount?: number;
  finalAmount?: number;
  depositPaidAt?: string;
  finalPaidAt?: string;
}

interface PaymentManagementProps {
  assessment: Assessment;
  onUpdate?: () => void;
}

export function PaymentManagement({ assessment, onUpdate }: PaymentManagementProps) {
  const [paymentStatus, setPaymentStatus] = useState(assessment.paymentStatus);
  const [depositAmount, setDepositAmount] = useState(
    assessment.depositAmount ? (assessment.depositAmount / 100).toFixed(2) : ""
  );
  const [finalAmount, setFinalAmount] = useState(
    assessment.finalAmount ? (assessment.finalAmount / 100).toFixed(2) : ""
  );
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const updatePaymentMutation = useMutation({
    mutationFn: async (data: any) => {
      console.log('Payment update mutation started with data:', data);
      
      const response = await fetch(`/api/admin/update-payment/${assessment.id}`, {
        method: 'PATCH',
        body: JSON.stringify(data),
        headers: { 
          'Content-Type': 'application/json'
        },
        credentials: 'include' // Include session cookies
      });
      
      console.log('Payment update response status:', response.status);
      console.log('Payment update response ok:', response.ok);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to update payment' }));
        console.log('Payment update error data:', errorData);
        throw new Error(errorData.message || `HTTP ${response.status}: Failed to update payment`);
      }
      
      const result = await response.json();
      console.log('Payment update success result:', result);
      return result;
    },
    onSuccess: () => {
      toast({
        title: "Payment Updated",
        description: "Payment details have been successfully updated.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/assessments'] });
      queryClient.invalidateQueries({ queryKey: ['/api/assessments', assessment.id] });
      onUpdate?.();
    },
    onError: (error: any) => {
      toast({
        title: "Update Failed", 
        description: error.message || "Failed to update payment details",
        variant: "destructive",
      });
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form inputs
    if (!paymentStatus) {
      toast({
        title: "Validation Error",
        description: "Please select a payment status",
        variant: "destructive",
      });
      return;
    }

    if (depositAmount && isNaN(parseFloat(depositAmount))) {
      toast({
        title: "Validation Error", 
        description: "Please enter a valid deposit amount",
        variant: "destructive",
      });
      return;
    }

    if (finalAmount && isNaN(parseFloat(finalAmount))) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid final amount", 
        variant: "destructive",
      });
      return;
    }
    
    const updateData: any = {
      paymentStatus
    };

    if (depositAmount && depositAmount.trim() !== "") {
      updateData.depositAmount = Math.round(parseFloat(depositAmount) * 100); // Convert to pence
    }
    
    if (finalAmount && finalAmount.trim() !== "") {
      updateData.finalAmount = Math.round(parseFloat(finalAmount) * 100); // Convert to pence
    }

    updatePaymentMutation.mutate(updateData);
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return "Not set";
    return `£${(amount / 100).toFixed(2)}`;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Not paid";
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'unpaid':
        return <Badge variant="destructive">Unpaid</Badge>;
      case 'deposit_paid':
        return <Badge variant="secondary">Deposit Paid</Badge>;
      case 'fully_paid':
        return <Badge variant="default" className="bg-green-600 hover:bg-green-700">Fully Paid</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PoundSterling className="h-5 w-5" />
          Payment Management
        </CardTitle>
        <CardDescription>
          Update payment status and amounts for this assessment
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Payment Status */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
          <div>
            <Label className="text-sm font-medium text-gray-600">Current Status</Label>
            <div className="mt-1">{getStatusBadge(assessment.paymentStatus)}</div>
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-600">Deposit Amount</Label>
            <div className="mt-1 font-medium">{formatCurrency(assessment.depositAmount)}</div>
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-600">Final Amount</Label>
            <div className="mt-1 font-medium">{formatCurrency(assessment.finalAmount)}</div>
          </div>
          <div>
            <Label className="text-sm font-medium text-gray-600">Payment Dates</Label>
            <div className="mt-1 text-sm">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                Deposit: {formatDate(assessment.depositPaidAt)}
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                Final: {formatDate(assessment.finalPaidAt)}
              </div>
            </div>
          </div>
        </div>

        {/* Update Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="paymentStatus">Payment Status</Label>
              <Select value={paymentStatus} onValueChange={(value) => setPaymentStatus(value as 'unpaid' | 'deposit_paid' | 'fully_paid')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unpaid">Unpaid</SelectItem>
                  <SelectItem value="deposit_paid">Deposit Paid</SelectItem>
                  <SelectItem value="fully_paid">Fully Paid</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="depositAmount">Deposit Amount (£)</Label>
              <Input
                id="depositAmount"
                type="number"
                step="0.01"
                min="0"
                value={depositAmount}
                onChange={(e) => setDepositAmount(e.target.value)}
                placeholder="0.00"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="finalAmount">Final Amount (£)</Label>
            <Input
              id="finalAmount"
              type="number"
              step="0.01"
              min="0"
              value={finalAmount}
              onChange={(e) => setFinalAmount(e.target.value)}
              placeholder="0.00"
            />
          </div>

          <Button
            type="submit"
            disabled={updatePaymentMutation.isPending}
            className="w-full"
          >
            {updatePaymentMutation.isPending ? "Updating..." : "Update Payment"}
          </Button>
        </form>

        {/* Payment Status Guide */}
        <div className="text-sm text-gray-600 border-t pt-4">
          <h4 className="font-medium mb-2">Payment Status Guide:</h4>
          <ul className="space-y-1">
            <li><strong>Unpaid:</strong> No payment received</li>
            <li><strong>Deposit Paid:</strong> Initial deposit received, final payment pending</li>
            <li><strong>Fully Paid:</strong> All payments completed</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}