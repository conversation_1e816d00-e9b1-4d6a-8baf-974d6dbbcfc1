import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardFooter 
} from "@/components/ui/card";
import { 
  <PERSON><PERSON>, 
  <PERSON>bsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Form, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormDescription, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Spinner } from "@/components/ui/spinner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  ChevronLeft,
  Save,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Building2,
  School,
  FileText,
  Clock,
  Tag,
  Edit,
  Loader2
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { PaymentManagement } from "./payment-management";

// Define types for our data model
interface Assessment {
  id: number;
  assesseeId: number;
  referringUserId: number | null;
  referralType: 'university' | 'school' | 'individual';
  status: string;
  createdAt: string;
  notes: string | null;
  paymentStatus: 'unpaid' | 'deposit_paid' | 'fully_paid';
  depositAmount?: number;
  finalAmount?: number;
  depositPaidAt?: string;
  finalPaidAt?: string;
  assessee: {
    id: number;
    fullName: string;
    dateOfBirth: string;
    email: string | null;
    phone: string | null;
    address: string | null;
    courseProgram: string | null;
    yearOfStudy: string | null;
    parentId: number | null;
    parent?: {
      id: number;
      fullName: string;
      email: string;
      phone: string | null;
    } | null;
  };
  referringParty?: {
    id: number;
    fullName: string;
    organization: string | null;
    department: string | null;
    position: string | null;
    email: string;
    phone: string | null;
    role: string;
  } | null;
  referringUser?: {
    id: number;
    fullName: string;
    organization: string | null;
    department: string | null;
    position: string | null;
    email: string;
    phone: string | null;
    role: string;
  } | null;
  referringSchool?: {
    id: number;
    name: string;
    address: string | null;
    contactName: string | null;
    contactEmail: string | null;
    contactPhone: string | null;
  } | null;
  referral?: {
    id: number;
    schoolName: string | null;
    schoolEmail: string | null;
    schoolPhone: string | null;
    department: string | null;
    staffName: string | null;
    staffPosition: string | null;
    parentName: string | null;
    parentEmail: string | null;
    parentPhone: string | null;
    relationship: string | null;
    assesseeFullName: string | null;
    assesseeEmail: string | null;
    assesseePhone: string | null;
    dateOfBirth: string | null;
    assesseeYear: string | null;
    assesseeCourse: string | null;
    reasonForReferral: string | null;
    previousAssessment: string | null;
    previousAssessmentDetails: string | null;
    additionalNotes: string | null;
    assessmentConcerns: string | null;
  } | null;
}

// Create a schema for form validation
const referralFormSchema = z.object({
  // Assessee information
  assesseeFullName: z.string().min(1, "Assessee name is required"),
  assesseeDateOfBirth: z.string().optional(),
  assesseeEmail: z.string().email().optional().nullable(),
  assesseePhone: z.string().optional().nullable(),
  assesseeAddress: z.string().optional().nullable(),
  
  // Parent information (for under 16)
  parentFullName: z.string().optional().nullable(),
  parentEmail: z.string().email().optional().nullable(),
  parentPhone: z.string().optional().nullable(),
  parentRelationship: z.string().optional().nullable(),
  
  // Referring party information
  referringPartyName: z.string().optional().nullable(),
  referringPartyOrganization: z.string().optional().nullable(),
  referringPartyPosition: z.string().optional().nullable(),
  referringPartyEmail: z.string().email().optional().nullable(),
  referringPartyPhone: z.string().optional().nullable(),
  
  // School specific
  schoolName: z.string().optional().nullable(),
  schoolAddress: z.string().optional().nullable(),
  schoolContactName: z.string().optional().nullable(),
  schoolContactEmail: z.string().email().optional().nullable(),
  schoolContactPhone: z.string().optional().nullable(),
  
  // University specific
  departmentName: z.string().optional().nullable(),
  studentId: z.string().optional().nullable(),
  courseDetails: z.string().optional().nullable(),
  yearOfStudy: z.string().optional().nullable(),
  disabilityOfficer: z.string().optional().nullable(),

  // Common fields
  concernDetails: z.string().optional().nullable(),
  assessmentConcerns: z.array(z.string()).optional().nullable(),
  previousAssessment: z.enum(['yes', 'no']).optional().nullable(),
  previousAssessmentDetails: z.string().optional().nullable(),
  reasonForReferral: z.string().optional().nullable(),
  additionalInformation: z.string().optional().nullable(),
  internalNotes: z.string().optional().nullable(),
  
  // Payment fields
  paymentStatus: z.enum(['unpaid', 'deposit_paid', 'fully_paid']).optional(),
  depositAmount: z.string().optional().nullable(),
  finalAmount: z.string().optional().nullable(),
});

type ReferralFormValues = z.infer<typeof referralFormSchema>;

interface ReferralDetailsViewProps {
  assessmentId: number;
  onBack: () => void;
  onApprove?: (assessmentId: number) => void;
  onReject?: (assessmentId: number) => void;
  extraActions?: React.ReactNode;
}

export function ReferralDetailsView({
  assessmentId,
  onBack,
  onApprove,
  onReject,
  extraActions
}: ReferralDetailsViewProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Fetch assessment details
  const { 
    data: assessment, 
    isLoading, 
    error,
    refetch
  } = useQuery<Assessment>({
    queryKey: ['/api/admin/assessments', assessmentId],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/admin/assessments/${assessmentId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch assessment details');
      }
      return response.json();
    }
  });
  
  // Setup form with react-hook-form
  const form = useForm<ReferralFormValues>({
    resolver: zodResolver(referralFormSchema),
    defaultValues: {
      assesseeFullName: '',
      assesseeDateOfBirth: '',
      assesseeEmail: '',
      assesseePhone: '',
      assesseeAddress: '',
      parentFullName: '',
      parentEmail: '',
      parentPhone: '',
      parentRelationship: '',
      referringPartyName: '',
      referringPartyOrganization: '',
      referringPartyPosition: '',
      referringPartyEmail: '',
      referringPartyPhone: '',
      schoolName: '',
      schoolAddress: '',
      schoolContactName: '',
      schoolContactEmail: '',
      schoolContactPhone: '',
      departmentName: '',
      studentId: '',
      courseDetails: '',
      yearOfStudy: '',
      disabilityOfficer: '',
      concernDetails: '',
      assessmentConcerns: [],
      previousAssessment: 'no',
      previousAssessmentDetails: '',
      reasonForReferral: '',
      additionalInformation: '',
      internalNotes: '',
      
      // Payment fields
      paymentStatus: 'unpaid',
      depositAmount: '',
      finalAmount: '',
    }
  });
  
  // Update form when assessment data is loaded
  useEffect(() => {
    if (assessment && assessment.assessee) {
      console.log('Referral details view - Assessment data:', assessment);
      console.log('Referral details view - Referral data:', assessment.referral);
      
      // Extract additional data from notes field if it exists
      let additionalData = {};
      if (assessment.notes) {
        try {
          // Try to parse as JSON first
          additionalData = JSON.parse(assessment.notes);
        } catch (e) {
          // If not valid JSON, attempt to extract key info from the text notes
          const notesText = assessment.notes as string;
          const firstLine = notesText.split('\n')[0];
          const prevMatch = notesText.match(/Previous assessment:\s*(Yes|No)(?:\s*-\s*(.*))?/i);
          const yearMatch = notesText.match(/School Year\/Grade:\s*([^\n]+)/i);
          const parentMatch = notesText.match(/Parent\/Guardian:\s*.*\(([^)]+)\)/i);
          const additionalMatch = notesText.match(/Additional notes:\s*([^\n]+)/i);
          additionalData = {
            reasonForReferral: firstLine.trim(),
            previousAssessment: prevMatch ? prevMatch[1].toLowerCase() : 'no',
            previousAssessmentDetails: prevMatch && prevMatch[2] ? prevMatch[2].trim() : '',
            yearOfStudy: yearMatch ? yearMatch[1].trim() : '',
            parentRelationship: parentMatch ? parentMatch[1].trim() : '',
            additionalInformation: additionalMatch ? additionalMatch[1].trim() : '',
          } as any;
        }
      }

      // Set values in the form
      const referral = assessment.referral;
      const assessee = assessment.assessee;
      
      console.log('Referral details view - Form values being set:', {
        reasonForReferral: referral?.reasonForReferral,
        assessmentConcerns: referral?.assessmentConcerns,
        previousAssessment: referral?.previousAssessment,
        previousAssessmentDetails: referral?.previousAssessmentDetails,
        additionalInformation: referral?.additionalNotes
      });

      form.reset({
        assesseeFullName: referral?.assesseeFullName || assessee?.fullName || '',
        assesseeDateOfBirth: (referral?.dateOfBirth ?
          format(new Date(referral.dateOfBirth), 'yyyy-MM-dd') :
          (assessee?.dateOfBirth ? format(new Date(assessee.dateOfBirth), 'yyyy-MM-dd') : '')),
        assesseeEmail: referral?.assesseeEmail || assessee?.email || '',
        assesseePhone: referral?.assesseePhone || assessee?.phone || '',
        assesseeAddress: assessee?.address || '',

        // Parent info (if available)
        parentFullName: referral?.parentName || assessee?.parent?.fullName || '',
        parentEmail: referral?.parentEmail || assessee?.parent?.email || '',
        parentPhone: referral?.parentPhone || assessee?.parent?.phone || '',
        parentRelationship: referral?.relationship || (additionalData as any).parentRelationship || '',

        // Referring party info - with proper null checks
        referringPartyName: referral?.staffName || assessment.referringUser?.fullName || assessment.referringParty?.fullName || '',
        referringPartyOrganization: assessment.referringUser?.organization || referral?.schoolName || assessment.referringParty?.organization || '',
        referringPartyPosition: referral?.staffPosition || assessment.referringUser?.position || assessment.referringParty?.position || '',
        referringPartyEmail: referral?.schoolEmail || assessment.referringUser?.email || assessment.referringParty?.email || '',
        referringPartyPhone: referral?.schoolPhone || assessment.referringUser?.phone || assessment.referringParty?.phone || '',

        // School specific (if available)
        schoolName: referral?.schoolName || assessment.referringSchool?.name || '',
        schoolAddress: assessment.referringSchool?.address || '',
        schoolContactName: assessment.referringSchool?.contactName || '',
        schoolContactEmail: assessment.referringSchool?.contactEmail || '',
        schoolContactPhone: assessment.referringSchool?.contactPhone || '',

        // Specific fields based on assessee data and referral data
        departmentName: referral?.department || assessment.referringUser?.department || assessment.referringParty?.department ||
                       (additionalData as any).departmentName || '',
        studentId: (additionalData as any).studentId || '',
        // Use the dedicated assessee fields first, then fall back to referral data
        courseDetails: referral?.assesseeCourse || assessee?.courseProgram ||
                      (additionalData as any).course ||
                      (additionalData as any).courseDetails || '',
        yearOfStudy: referral?.assesseeYear || assessee?.yearOfStudy ||
                    (additionalData as any).yearOfStudy || '',
        assessmentConcerns:
          referral?.assessmentConcerns
            ? (typeof referral.assessmentConcerns === 'string' ? 
               (referral.assessmentConcerns.startsWith('[') ? JSON.parse(referral.assessmentConcerns) : []) : 
               referral.assessmentConcerns)
            : (additionalData as any).assessmentConcerns || [],
        disabilityOfficer: (additionalData as any).disabilityOfficer || '',

        // Common fields - prioritize referral data over notes data
        concernDetails: (additionalData as any).concernDetails || '',
        previousAssessment: referral?.previousAssessment || (additionalData as any).previousAssessment || 'no',
        previousAssessmentDetails:
          referral?.previousAssessmentDetails || (additionalData as any).previousAssessmentDetails || '',
        reasonForReferral: referral?.reasonForReferral || (additionalData as any).reasonForReferral || '',
        additionalInformation: referral?.additionalNotes || (additionalData as any).additionalInfo ||
                              (additionalData as any).additionalInformation || '',
        internalNotes: (additionalData as any).internalNotes || '',
        
        // Payment fields
        paymentStatus: assessment.paymentStatus || 'unpaid',
        depositAmount: assessment.depositAmount ? (assessment.depositAmount / 100).toFixed(2) : '',
        finalAmount: assessment.finalAmount ? (assessment.finalAmount / 100).toFixed(2) : '',
      });
    }
  }, [assessment, form]);
  
  // Mutation to save form changes
  const updateReferralMutation = useMutation({
    mutationFn: async (formData: ReferralFormValues) => {
      // First update assessee information
      if (assessment) {
        // Convert form data to match our database structure and API expectations
        const notes = JSON.stringify({
          departmentName: formData.departmentName,
          studentId: formData.studentId,
          courseDetails: formData.courseDetails,
          yearOfStudy: formData.yearOfStudy,
          assessmentConcerns: formData.assessmentConcerns,
          disabilityOfficer: formData.disabilityOfficer,
          concernDetails: formData.concernDetails,
          previousAssessment: formData.previousAssessment,
          previousAssessmentDetails: formData.previousAssessmentDetails,
          parentRelationship: formData.parentRelationship,
          reasonForReferral: formData.reasonForReferral,
          additionalInformation: formData.additionalInformation,
          internalNotes: formData.internalNotes,
        });
        
        // Update assessment notes
        const assessmentResponse = await apiRequest('PATCH', `/api/admin/update-assessment-notes/${assessmentId}`, {
          notes
        });
        
        if (!assessmentResponse.ok) {
          throw new Error('Failed to update assessment data');
        }
        
        // Update assessee information
        const assesseeResponse = await apiRequest('PATCH', `/api/admin/update-assessee/${assessment.assesseeId}`, {
          fullName: formData.assesseeFullName,
          // Properly handle the date by converting string to Date object
          dateOfBirth: formData.assesseeDateOfBirth ? new Date(formData.assesseeDateOfBirth) : null,
          email: formData.assesseeEmail || null,
          phone: formData.assesseePhone || null,
          address: formData.assesseeAddress || null,
          // Include university specific fields when updating assessee
          courseProgram: formData.courseDetails || null,
          yearOfStudy: formData.yearOfStudy || null,
        });
        
        if (!assesseeResponse.ok) {
          throw new Error('Failed to update assessee data');
        }
        
        // If this is a university referral and we have a referring user ID, update the user data
        if (assessment.referralType === 'university' && assessment.referringUserId) {
          const referringUserResponse = await apiRequest('PATCH', `/api/admin/update-user/${assessment.referringUserId}`, {
            fullName: formData.referringPartyName || null,
            organization: formData.referringPartyOrganization || null,
            position: formData.referringPartyPosition || null,
            department: formData.departmentName || null, // Store department directly in user record
            phone: formData.referringPartyPhone || null,
          });

          if (!referringUserResponse.ok) {
            // Log the error but don't fail the overall update
            console.error('Failed to update referring user data');
          }
        }

        // Note: Referral data is stored in the assessment notes and assessee records
        // No separate referral table update needed
        
        // Update payment information if payment fields are present
        if (formData.paymentStatus || formData.depositAmount || formData.finalAmount) {
          const paymentData = {
            paymentStatus: formData.paymentStatus,
            // Convert pounds to pence for storage
            depositAmount: formData.depositAmount ? Math.round(parseFloat(formData.depositAmount) * 100) : null,
            finalAmount: formData.finalAmount ? Math.round(parseFloat(formData.finalAmount) * 100) : null,
          };
          
          const paymentResponse = await apiRequest('PATCH', `/api/admin/update-payment/${assessmentId}`, paymentData);
          
          if (!paymentResponse.ok) {
            throw new Error('Failed to update payment data');
          }
        }

        return assessmentResponse.json();
      }
      
      throw new Error('Assessment not found');
    },
    onSuccess: () => {
      toast({
        title: 'Changes saved',
        description: 'Referral information has been updated successfully',
      });
      refetch();
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
    onSettled: () => {
      setIsSubmitting(false);
    }
  });
  
  const onSubmit = (data: ReferralFormValues) => {
    setIsSubmitting(true);
    updateReferralMutation.mutate(data);
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Spinner size="lg" className="mb-4" />
            <p className="text-muted-foreground">Loading referral details...</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  // Render error state
  if (error || !assessment || !assessment.assessee) {
    return (
      <Card className="w-full">
        <CardContent className="py-6">
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error instanceof Error ? error.message : 'Failed to load referral details'}
            </AlertDescription>
          </Alert>
          <Button onClick={onBack} variant="outline">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Queue
          </Button>
        </CardContent>
      </Card>
    );
  }
  
  // Helper function to render the referral badge
  const getReferralTypeBadge = () => {
    switch (assessment.referralType) {
      case 'university':
        return (
          <Badge variant="outline" className="flex items-center space-x-1">
            <Building2 className="h-4 w-4 mr-1" />
            <span>University</span>
          </Badge>
        );
      case 'school':
        return (
          <Badge variant="outline" className="flex items-center space-x-1">
            <School className="h-4 w-4 mr-1" />
            <span>School</span>
          </Badge>
        );
      case 'individual':
        return (
          <Badge variant="outline" className="flex items-center space-x-1">
            <User className="h-4 w-4 mr-1" />
            <span>Individual</span>
          </Badge>
        );
      default:
        return null;
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center mb-4">
          <Button 
            variant="ghost" 
            onClick={onBack} 
            className="mr-2 p-0 h-8 w-8"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div>
            <CardTitle className="text-xl flex items-center">
              Referral Details
              <span className="ml-3">
                {getReferralTypeBadge()}
              </span>
            </CardTitle>
            <CardDescription className="mt-1">
              Submitted {format(new Date(assessment.createdAt), 'PPP')} • Referral ID: {assessment.id}
            </CardDescription>
          </div>
        </div>
        <div className="bg-muted/40 rounded-md p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-muted-foreground mb-1">Assessee</p>
            <p className="font-medium">{assessment.assessee?.fullName || 'N/A'}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground mb-1">Referring Party</p>
            <p className="font-medium">
              {assessment.referringUser ? assessment.referringUser.fullName : 
                assessment.referringSchool ? assessment.referringSchool.name : 'Self-referred'}
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground mb-1">Status</p>
            <Badge variant={assessment.status === 'VerificationPending' ? 'outline' : 'default'}>
              {assessment.status}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-0 py-0">
        <Tabs defaultValue="student">
          <div className="px-6 pt-4 border-b">
            <TabsList className="w-full justify-start">
              {(assessment.referralType === 'university' || assessment.referralType === 'individual') && (
                <TabsTrigger value="student">Student Details</TabsTrigger>
              )}
              {(assessment.referralType === 'individual' || (
                assessment.assessee?.dateOfBirth &&
                new Date(assessment.assessee?.dateOfBirth || '') > new Date(new Date().setFullYear(new Date().getFullYear() - 16))
              )) && (
                <TabsTrigger value="parent">Parent/Guardian Details</TabsTrigger>
              )}
              {assessment.referralType === 'university' && (
                <TabsTrigger value="university">University Details</TabsTrigger>
              )}
              {(assessment.referralType === 'school' || assessment.referralType === 'individual') && (
                <TabsTrigger value="school">School Details</TabsTrigger>
              )}
              <TabsTrigger value="notes">
                {assessment.referralType === 'individual' ? 'Assessment Details' : 'Notes & Additional Info'}
              </TabsTrigger>
              <TabsTrigger value="payment">Payment Management</TabsTrigger>
            </TabsList>
          </div>
          

          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="px-6 py-4">
                {/* Student Details Tab for both university and individual referrals */}
                {(assessment.referralType === 'university' || assessment.referralType === 'individual') && (
                  <TabsContent value="student" className="mt-0 space-y-4 px-6 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="assesseeFullName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Full Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="assesseeDateOfBirth"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Date of Birth</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    
                      <FormField
                        control={form.control}
                        name="assesseeEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="assesseePhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {assessment.referralType === 'university' && (
                      <FormField
                        control={form.control}
                        name="assesseeAddress"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Address</FormLabel>
                            <FormControl>
                              <Textarea rows={2} {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                      {assessment.referralType === 'university' && (
                        <>
                          <FormField
                            control={form.control}
                            name="studentId"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Student ID</FormLabel>
                                <FormControl>
                                  <Input {...field} value={field.value || ''} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="courseDetails"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Course/Program</FormLabel>
                                <FormControl>
                                  <Input {...field} value={field.value || ''} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </>
                      )}

                      {assessment.referralType === 'university' && (
                        <FormField
                          control={form.control}
                          name="yearOfStudy"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Year of Study</FormLabel>
                              <FormControl>
                                <Input {...field} value={field.value || ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {assessment.referralType === 'university' && (
                        <FormField
                          control={form.control}
                          name="disabilityOfficer"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Disability Officer</FormLabel>
                              <FormControl>
                                <Input {...field} value={field.value || ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                  </TabsContent>
                )}
                
                {/* Parent/Guardian Details Tab */}
                {(assessment.referralType === 'individual' || (
                  assessment.assessee?.dateOfBirth &&
                  new Date(assessment.assessee?.dateOfBirth || '') > new Date(new Date().setFullYear(new Date().getFullYear() - 16))
                )) && (
                  <TabsContent value="parent" className="mt-0 space-y-4 px-6 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="parentFullName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Full Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="parentRelationship"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Relationship with Assessee</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    
                      <FormField
                        control={form.control}
                        name="parentEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="parentPhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>
                )}
                
                {assessment.referralType === 'university' && (
                  <TabsContent value="university" className="mt-0 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="referringPartyOrganization"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>University Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="departmentName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Department Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="referringPartyName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff Member Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="referringPartyPosition"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff Position</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="referringPartyEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff Email</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="referringPartyPhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Contact Phone</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                  </TabsContent>
                )}
                
                {(assessment.referralType === 'school' || assessment.referralType === 'individual') && (
                  <TabsContent value="school" className="mt-0 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="schoolName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="departmentName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Department</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {assessment.referralType !== 'university' && (
                        <FormField
                          control={form.control}
                          name="yearOfStudy"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>School Year/Grade</FormLabel>
                              <FormControl>
                                <Input {...field} value={field.value || ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      <FormField
                        control={form.control}
                        name="referringPartyName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff Member Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="referringPartyPosition"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff Position</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="referringPartyEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Email</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="referringPartyPhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Phone</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>
                )}
                
                <TabsContent value="notes" className="mt-0 space-y-4">
                  <FormField
                    control={form.control}
                    name="reasonForReferral"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reason for Referral</FormLabel>
                        <FormControl>
                          <Textarea rows={3} {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="assessmentConcerns"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Suspected Difficulties</FormLabel>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 mt-2">
                          {['Dyslexia', 'Dyspraxia/DCD', 'ADHD'].map((option) => (
                            <label key={option} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                value={option}
                                checked={field.value?.includes(option) || false}
                                onChange={(e) => {
                                  const checked = e.target.checked;
                                  const newValue = checked
                                    ? [...(field.value || []), option]
                                    : (field.value || []).filter((v) => v !== option);
                                  field.onChange(newValue);
                                }}
                                className="h-4 w-4"
                              />
                              <span className="text-sm">{option}</span>
                            </label>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="previousAssessment"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Has the student had a previous SpLD assessment?</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value || 'no'}
                            className="flex flex-row space-x-4"
                          >
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="yes" />
                              </FormControl>
                              <FormLabel className="font-normal">Yes</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="no" />
                              </FormControl>
                              <FormLabel className="font-normal">No</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch('previousAssessment') === 'yes' && (
                    <FormField
                      control={form.control}
                      name="previousAssessmentDetails"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Previous Assessment Details</FormLabel>
                          <FormControl>
                            <Textarea rows={3} {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="additionalInformation"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Additional Information</FormLabel>
                        <FormControl>
                          <Textarea rows={3} {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="internalNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Admin Notes</FormLabel>
                        <FormControl>
                          <Textarea rows={3} {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                {/* Payment Management Tab */}
                <TabsContent value="payment" className="mt-0 space-y-4 px-6 py-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="paymentStatus"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Payment Status</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select payment status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="unpaid">Unpaid</SelectItem>
                              <SelectItem value="deposit_paid">Deposit Paid</SelectItem>
                              <SelectItem value="fully_paid">Fully Paid</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="space-y-1">
                      <FormLabel className="text-sm font-medium text-muted-foreground">Current Status</FormLabel>
                      <div className="flex items-center space-x-2">
                        <Badge variant={
                          assessment.paymentStatus === 'fully_paid' ? 'default' :
                          assessment.paymentStatus === 'deposit_paid' ? 'secondary' : 'outline'
                        }>
                          {assessment.paymentStatus === 'fully_paid' ? 'Fully Paid' :
                           assessment.paymentStatus === 'deposit_paid' ? 'Deposit Paid' : 'Unpaid'}
                        </Badge>
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="depositAmount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Deposit Amount (£)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-3 text-sm text-muted-foreground">£</span>
                              <Input 
                                {...field} 
                                type="number" 
                                step="0.01" 
                                placeholder="0.00"
                                className="pl-8"
                                value={field.value || ''}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="finalAmount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Final Amount (£)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-3 text-sm text-muted-foreground">£</span>
                              <Input 
                                {...field} 
                                type="number" 
                                step="0.01" 
                                placeholder="0.00"
                                className="pl-8"
                                value={field.value || ''}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Payment Summary */}
                  <div className="bg-muted/40 rounded-md p-4 space-y-2">
                    <h4 className="font-medium text-sm">Payment Summary</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Deposit:</span>
                        <span className="ml-2 font-medium">
                          {assessment.depositAmount ? `£${(assessment.depositAmount / 100).toFixed(2)}` : '£0.00'}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Final Amount:</span>
                        <span className="ml-2 font-medium">
                          {assessment.finalAmount ? `£${(assessment.finalAmount / 100).toFixed(2)}` : '£0.00'}
                        </span>
                      </div>
                      {assessment.depositPaidAt && (
                        <div className="col-span-2">
                          <span className="text-muted-foreground">Deposit paid:</span>
                          <span className="ml-2">{format(new Date(assessment.depositPaidAt), 'PPP')}</span>
                        </div>
                      )}
                      {assessment.finalPaidAt && (
                        <div className="col-span-2">
                          <span className="text-muted-foreground">Final payment:</span>
                          <span className="ml-2">{format(new Date(assessment.finalPaidAt), 'PPP')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

              </div>
              
              <CardFooter className="flex justify-between border-t px-6 py-4">
                <div className="flex space-x-2">
                  <Button 
                    type="button"
                    onClick={() => {
                      console.log("Save button clicked");
                      onSubmit(form.getValues());
                    }}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </div>
                
                <div className="flex space-x-2">
                  {onApprove && (
                    <Button 
                      variant="default"
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() => onApprove(assessmentId)}
                      disabled={isSubmitting}
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve Referral
                    </Button>
                  )}
                  
                  {onReject && (
                    <Button 
                      variant="destructive"
                      onClick={() => onReject(assessmentId)}
                      disabled={isSubmitting}
                    >
                      <XCircle className="mr-2 h-4 w-4" />
                      Reject Referral
                    </Button>
                  )}
                  
                  {extraActions}
                </div>
              </CardFooter>
            </form>
          </Form>
        </Tabs>
      </CardContent>
    </Card>
  );
}