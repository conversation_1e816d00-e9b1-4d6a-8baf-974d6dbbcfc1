import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import { CheckCircle, XCircle, AlertCircle, User, School, Building2, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { formatDistanceToNow } from "date-fns";
import { ReferralDetailsView } from "./referral-details-view";

interface Assessment {
  id: number;
  assesseeId: number;
  referringUserId: number | null;
  referralType: 'university' | 'school' | 'individual';
  status: string;
  createdAt: string;
  notes: string | null;
  paymentStatus: string;
  assessee: {
    fullName: string;
    dateOfBirth: string;
    email: string | null;
    phone: string | null;
    address: string | null;
    parentId: number | null;
  };
  referringUser: {
    fullName: string;
    organization: string | null;
    email: string;
    phone: string | null;
  } | null;
}

export function VerificationQueue() {
  const { toast } = useToast();
  const [selectedAssessmentId, setSelectedAssessmentId] = useState<number | null>(null);
  
  // Fetch pending verification assessments
  const { data: pendingAssessments, isLoading, error, refetch } = useQuery<Assessment[]>({
    queryKey: ['/api/admin/pending-verifications'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/pending-verifications');
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to fetch pending verifications');
      }
      return res.json();
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Mutation to approve a referral
  const approveReferralMutation = useMutation({
    mutationFn: async (assessmentId: number) => {
      const response = await apiRequest('PATCH', `/api/admin/approve-referral/${assessmentId}`, {});
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to approve referral');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Referral Approved',
        description: 'The referral has been approved and moved to the Pre-Assessment phase.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/assessments'] });
      setSelectedAssessmentId(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Mutation to reject a referral
  const rejectReferralMutation = useMutation({
    mutationFn: async (assessmentId: number) => {
      const response = await apiRequest('PATCH', `/api/admin/reject-referral/${assessmentId}`, {});
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to reject referral');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Referral Rejected',
        description: 'The referral has been rejected.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/assessments'] });
      setSelectedAssessmentId(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Helper to render the referral type icon
  const renderReferralTypeIcon = (type: string) => {
    switch (type) {
      case 'university':
        return <Building2 className="h-4 w-4 mr-1" />;
      case 'school':
        return <School className="h-4 w-4 mr-1" />;
      case 'individual':
        return <User className="h-4 w-4 mr-1" />;
      default:
        return null;
    }
  };

  // Handle approving a referral
  const handleApproveReferral = (assessmentId: number) => {
    approveReferralMutation.mutate(assessmentId, {
      onSuccess: () => {
        refetch();
        setSelectedAssessmentId(null);
      }
    });
  };
  
  // Handle rejecting a referral
  const handleRejectReferral = (assessmentId: number) => {
    rejectReferralMutation.mutate(assessmentId, {
      onSuccess: () => {
        refetch();
        setSelectedAssessmentId(null);
      }
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-10 w-10 text-destructive mb-4" />
        <h3 className="text-lg font-medium">Error loading verification queue</h3>
        <p className="text-sm text-muted-foreground mt-2">
          {error instanceof Error ? error.message : 'An unknown error occurred'}
        </p>
      </div>
    );
  }

  // If a referral is selected, show the detailed view
  if (selectedAssessmentId) {
    return (
      <ReferralDetailsView 
        assessmentId={selectedAssessmentId}
        onBack={() => setSelectedAssessmentId(null)}
        onApprove={handleApproveReferral}
        onReject={handleRejectReferral}
      />
    );
  }

  // Main queue view
  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Verification Queue</h2>
        <div>
          <Button
            variant="outline"
            className="ml-2"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            Refresh
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-destructive/20 p-4 rounded-md text-destructive flex items-center space-x-2">
          <AlertCircle className="h-5 w-5" />
          <span>Error loading verification queue. Please try again.</span>
        </div>
      )}

      {pendingAssessments && pendingAssessments.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center p-6">
              <CheckCircle className="mx-auto h-12 w-12 text-primary opacity-50" />
              <h3 className="mt-4 text-lg font-medium">No pending verifications</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                All referrals have been processed. Check back later for new submissions.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="overflow-hidden">
          <CardHeader>
            <CardTitle>Pending Referrals</CardTitle>
            <CardDescription>
              Review and approve or reject incoming assessment referrals
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table className="border">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">ID</TableHead>
                  <TableHead>Assessee</TableHead>
                  <TableHead>Referring User</TableHead>
                  <TableHead className="w-[120px]">Type</TableHead>
                  <TableHead className="w-[130px]">Received</TableHead>
                  <TableHead className="w-[120px]">Payment</TableHead>
                  <TableHead className="w-[100px] text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pendingAssessments?.map((assessment) => (
                  <TableRow key={assessment.id}>
                    <TableCell className="font-medium">UNI-{assessment.id.toString().padStart(4, '0')}</TableCell>
                    <TableCell>{assessment.assessee.fullName}</TableCell>
                    <TableCell>
                      {assessment.referringUser?.fullName || '-'}
                      {assessment.referringUser?.organization && (
                        <div className="text-xs text-muted-foreground">
                          {assessment.referringUser.organization}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="flex items-center gap-1 capitalize">
                        {assessment.referralType === 'university' ? <Building2 className="h-3 w-3" /> : 
                        assessment.referralType === 'school' ? <School className="h-3 w-3" /> : 
                        <User className="h-3 w-3" />}
                        {assessment.referralType}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-xs">
                      {formatDistanceToNow(new Date(assessment.createdAt), { addSuffix: true })}
                    </TableCell>
                    <TableCell>
                      <Badge variant={assessment.paymentStatus === 'paid' ? 'outline' : 'secondary'} 
                             className={assessment.paymentStatus === 'paid' ? 'border-green-200 bg-green-100 text-green-800 hover:bg-green-200' : ''}>
                        {assessment.paymentStatus}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedAssessmentId(assessment.id)}
                          className="flex items-center"
                          title="View"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => handleApproveReferral(assessment.id)}
                          className="flex items-center bg-green-600 hover:bg-green-700"
                          title="Approve"
                        >
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleRejectReferral(assessment.id)}
                          className="flex items-center"
                          title="Reject"
                        >
                          <XCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}