import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import { CheckCircle, XCircle, AlertCircle, User, School, Building2, Eye, RotateCcw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { formatDistanceToNow } from "date-fns";
import { ReferralDetailsView } from "./referral-details-view";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ial<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ialog<PERSON><PERSON><PERSON>, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

interface Assessment {
  id: number;
  assesseeId: number;
  referringUserId: number | null;
  referralType: 'university' | 'school' | 'individual';
  status: string;
  createdAt: string;
  notes: string | null;
  paymentStatus: string;
  assessee: {
    fullName: string;
    dateOfBirth: string;
    email: string | null;
    phone: string | null;
    address: string | null;
    parentId: number | null;
  } | null;
  referringUser: {
    fullName: string;
    organization: string | null;
    email: string;
    phone: string | null;
  } | null;
}

export function AllReferralsTable() {
  const { toast } = useToast();
  const [selectedAssessmentId, setSelectedAssessmentId] = useState<number | null>(null);
  const [filter, setFilter] = useState<'all' | 'approved' | 'rejected' | 'pending'>('all');
  
  // Fetch all assessments
  const { data: allAssessments, isLoading, error, refetch } = useQuery<Assessment[]>({
    queryKey: ['/api/admin/all-assessments'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/admin/all-assessments');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to fetch all assessments');
      }
      return response.json();
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Mutation to reset a referral status
  const resetReferralMutation = useMutation({
    mutationFn: async (assessmentId: number) => {
      const response = await apiRequest('PATCH', `/api/admin/reset-referral/${assessmentId}`, {});
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to reset referral status');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Referral Status Reset',
        description: 'The referral has been reset to pending verification status.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/all-assessments'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/assessments'] });
      setSelectedAssessmentId(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Helper to determine if an assessment is approved
  const isApproved = (assessment: Assessment) => {
    return assessment.status === 'pre_assessment' || 
           (assessment.notes && assessment.notes.includes('APPROVED:'));
  };

  // Helper to determine if an assessment is rejected
  const isRejected = (assessment: Assessment) => {
    return assessment.notes && assessment.notes.includes('REJECTED:');
  };

  // Helper to determine if an assessment is pending
  const isPending = (assessment: Assessment) => {
    return (assessment.status === 'VerificationPending' || assessment.status === 'Enquiry') &&
           !isApproved(assessment) &&
           !isRejected(assessment);
  };

  // Filter assessments based on current filter
  const filteredAssessments = allAssessments ? allAssessments.filter(assessment => {
    if (filter === 'all') return true;
    if (filter === 'approved') return isApproved(assessment);
    if (filter === 'rejected') return isRejected(assessment);
    if (filter === 'pending') return isPending(assessment);
    return true;
  }) : [];

  // Helper to render the referral type icon
  const renderReferralTypeIcon = (type: string) => {
    switch (type) {
      case 'university':
        return <Building2 className="h-4 w-4" />;
      case 'school':
        return <School className="h-4 w-4" />;
      case 'individual':
        return <User className="h-4 w-4" />;
      default:
        return null;
    }
  };

  // Helper to render the status badge
  const renderStatusBadge = (assessment: Assessment) => {
    if (isApproved(assessment)) {
      return <Badge variant="outline" className="bg-green-100 text-green-800">Approved</Badge>;
    } else if (isRejected(assessment)) {
      return <Badge variant="destructive" className="bg-red-100 text-red-800">Rejected</Badge>;
    } else {
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    }
  };

  // If a referral is selected, show the detailed view
  if (selectedAssessmentId) {
    return (
      <ReferralDetailsView 
        assessmentId={selectedAssessmentId}
        onBack={() => setSelectedAssessmentId(null)}
        extraActions={
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" className="ml-2">
                <RotateCcw className="mr-2 h-4 w-4" />
                Reset Status
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Reset Referral Status</AlertDialogTitle>
                <AlertDialogDescription>
                  This will reset the referral to pending verification status. Any approval or rejection will be cleared.
                  Are you sure you want to proceed?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={() => resetReferralMutation.mutate(selectedAssessmentId)}
                  disabled={resetReferralMutation.isPending}
                >
                  {resetReferralMutation.isPending ? (
                    <>
                      <Spinner className="mr-2" size="sm" /> 
                      Resetting...
                    </>
                  ) : (
                    "Reset Status"
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        }
      />
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-10 w-10 text-destructive mb-4" />
        <h3 className="text-lg font-medium">Error loading referrals</h3>
        <p className="text-sm text-muted-foreground mt-2">
          {error instanceof Error ? error.message : 'An unknown error occurred'}
        </p>
      </div>
    );
  }

  // Main table view
  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">All Referrals</h2>
        <div className="flex gap-2">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            onClick={() => setFilter('all')}
          >
            All
          </Button>
          <Button
            variant={filter === 'pending' ? 'default' : 'outline'}
            onClick={() => setFilter('pending')}
          >
            Pending
          </Button>
          <Button
            variant={filter === 'approved' ? 'default' : 'outline'}
            onClick={() => setFilter('approved')}
          >
            Approved
          </Button>
          <Button
            variant={filter === 'rejected' ? 'default' : 'outline'}
            onClick={() => setFilter('rejected')}
          >
            Rejected
          </Button>
          <Button
            variant="outline"
            className="ml-2"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            Refresh
          </Button>
        </div>
      </div>

      {filteredAssessments.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <p className="text-muted-foreground">No referrals found with the selected filter.</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Assessee</TableHead>
                  <TableHead>Referring Party</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Received</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssessments.map((assessment) => (
                  <TableRow key={assessment.id}>
                    <TableCell className="font-medium">UNI-{assessment.id.toString().padStart(4, '0')}</TableCell>
                    <TableCell>{assessment.assessee?.fullName || 'Unknown Assessee'}</TableCell>
                    <TableCell>
                      {assessment.referringUser?.fullName || '-'}
                      {assessment.referringUser?.organization && (
                        <div className="text-xs text-muted-foreground">
                          {assessment.referringUser.organization}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="flex items-center gap-1 capitalize">
                        {renderReferralTypeIcon(assessment.referralType)}
                        {assessment.referralType}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-xs">
                      {formatDistanceToNow(new Date(assessment.createdAt), { addSuffix: true })}
                    </TableCell>
                    <TableCell>
                      {renderStatusBadge(assessment)}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => setSelectedAssessmentId(assessment.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="ml-2"
                              disabled={isPending(assessment)}
                            >
                              <RotateCcw className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Reset Referral Status</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will reset the referral to pending verification status. Any approval or rejection will be cleared.
                                Are you sure you want to proceed?
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction 
                                onClick={() => resetReferralMutation.mutate(assessment.id)}
                                disabled={resetReferralMutation.isPending}
                              >
                                {resetReferralMutation.isPending ? (
                                  <>
                                    <Spinner className="mr-2" size="sm" /> 
                                    Resetting...
                                  </>
                                ) : (
                                  "Reset Status"
                                )}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}