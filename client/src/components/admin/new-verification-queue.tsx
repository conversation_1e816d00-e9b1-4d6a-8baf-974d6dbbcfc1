import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { CheckCircle, XCircle, AlertCircle, User, School, Building2, Pencil, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { formatDistanceToNow } from "date-fns";
import { z } from "zod";

interface Assessment {
  id: number;
  assesseeId: number;
  referringUserId: number | null;
  referralType: 'university' | 'school' | 'individual';
  status: string;
  createdAt: string;
  notes: string | null;
  paymentStatus: string;
  assessee: {
    fullName: string;
    dateOfBirth: string;
    email: string | null;
    phone: string | null;
    address: string | null;
    parentId: number | null;
  };
  referringUser: {
    fullName: string;
    organization: string | null;
    email: string;
    phone: string | null;
  } | null;
}

export function VerificationQueue() {
  const { toast } = useToast();
  const [selectedAssessment, setSelectedAssessment] = useState<Assessment | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  
  // Define a type for form data based on referral type
  type FormDataType = {
    // Common fields
    notes?: string;
    additionalInfo?: string;
    
    // University specific fields
    universityName?: string;
    department?: string;
    studentName?: string;
    studentId?: string;
    course?: string;
    yearOfStudy?: string;
    disabilityOfficer?: string;
    reasonForReferral?: string;
    
    // Individual specific fields
    fullName?: string;
    dateOfBirth?: string;
    email?: string;
    phone?: string;
    address?: string;
    concernDetails?: string;
    
    // Additional fields can be added as needed
    [key: string]: any;
  };
  
  const [formData, setFormData] = useState<FormDataType | null>(null);

  // Fetch pending verification assessments
  const { data: pendingAssessments, isLoading, error } = useQuery<Assessment[]>({
    queryKey: ['/api/admin/pending-verifications'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/pending-verifications');
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to fetch pending verifications');
      }
      return res.json();
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Mutation to approve a referral
  const approveReferralMutation = useMutation({
    mutationFn: async (assessmentId: number) => {
      const response = await apiRequest('PATCH', `/api/admin/approve-referral/${assessmentId}`, {});
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to approve referral');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Referral Approved',
        description: 'The referral has been approved and moved to the Pre-Assessment phase.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/assessments'] });
      setSelectedAssessment(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Mutation to reject a referral
  const rejectReferralMutation = useMutation({
    mutationFn: async (assessmentId: number) => {
      const response = await apiRequest('PATCH', `/api/admin/reject-referral/${assessmentId}`, {});
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to reject referral');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Referral Rejected',
        description: 'The referral has been rejected.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/assessments'] });
      setSelectedAssessment(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Mutation to update form data
  const updateFormDataMutation = useMutation({
    mutationFn: async ({ assessmentId, formData }: { assessmentId: number, formData: FormDataType }) => {
      const response = await apiRequest('PATCH', `/api/admin/update-assessment-notes/${assessmentId}`, {
        notes: JSON.stringify(formData)
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to update form data');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Form Updated',
        description: 'The form data has been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
      setIsEditing(false);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Helper to render the referral type icon
  const renderReferralTypeIcon = (type: string) => {
    switch (type) {
      case 'university':
        return <Building2 className="h-4 w-4 mr-1" />;
      case 'school':
        return <School className="h-4 w-4 mr-1" />;
      case 'individual':
        return <User className="h-4 w-4 mr-1" />;
      default:
        return null;
    }
  };

  // Helper function to initialize form data from assessment notes
  const initializeFormData = (assessment: Assessment) => {
    if (!assessment.notes) return;
    
    try {
      // First try to parse as JSON
      if (assessment.notes.trim().startsWith('{')) {
        const parsedData = JSON.parse(assessment.notes);
        setFormData(parsedData);
      } else {
        // If not valid JSON, treat as plain text and create a new object
        const initialData: FormDataType = {
          notes: assessment.notes,
          // Add default fields based on referral type
          ...(assessment.referralType === 'university' 
            ? {
                universityName: assessment.referringUser?.organization || '',
                department: '',
                studentName: assessment.assessee.fullName || '',
                studentId: '',
                course: '',
                yearOfStudy: '',
                disabilityOfficer: assessment.referringUser?.fullName || '',
                reasonForReferral: '',
                additionalInfo: assessment.notes || ''
              }
            : {
                fullName: assessment.assessee.fullName || '',
                dateOfBirth: new Date(assessment.assessee.dateOfBirth).toLocaleDateString() || '',
                email: assessment.assessee.email || '',
                phone: assessment.assessee.phone || '',
                address: assessment.assessee.address || '',
                concernDetails: '',
                additionalInfo: assessment.notes || ''
              }
          )
        };
        setFormData(initialData);
      }
    } catch (error) {
      const e = error as Error;
      console.error("Parse error:", e);
      toast({
        title: "Error",
        description: `Could not process form data: ${e.message}`,
        variant: "destructive",
      });
    }
  };

  // Create form fields dynamically based on referral type
  const renderFormFields = () => {
    if (!formData) return null;

    if (selectedAssessment?.referralType === 'university') {
      return (
        <div className="space-y-6">
          <div className="space-y-4">
            <h4 className="text-base font-medium border-b pb-2">University Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">University Name</label>
                <Input 
                  value={formData.universityName || ''} 
                  onChange={(e) => setFormData({...formData, universityName: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Department</label>
                <Input 
                  value={formData.department || ''} 
                  onChange={(e) => setFormData({...formData, department: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h4 className="text-base font-medium border-b pb-2">Student Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Student Name</label>
                <Input 
                  value={formData.studentName || ''} 
                  onChange={(e) => setFormData({...formData, studentName: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Student ID</label>
                <Input 
                  value={formData.studentId || ''} 
                  onChange={(e) => setFormData({...formData, studentId: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Course</label>
                <Input 
                  value={formData.course || ''} 
                  onChange={(e) => setFormData({...formData, course: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Year of Study</label>
                <Input 
                  value={formData.yearOfStudy || ''} 
                  onChange={(e) => setFormData({...formData, yearOfStudy: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h4 className="text-base font-medium border-b pb-2">Referral Details</h4>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Disability Officer</label>
                <Input 
                  value={formData.disabilityOfficer || ''} 
                  onChange={(e) => setFormData({...formData, disabilityOfficer: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Reason For Referral</label>
                <Textarea 
                  value={formData.reasonForReferral || ''} 
                  onChange={(e) => setFormData({...formData, reasonForReferral: e.target.value})}
                  rows={4}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Additional Information</label>
                <Textarea 
                  value={formData.additionalInfo || ''} 
                  onChange={(e) => setFormData({...formData, additionalInfo: e.target.value})}
                  rows={4}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
            </div>
          </div>
        </div>
      );
    } else if (selectedAssessment?.referralType === 'individual') {
      return (
        <div className="space-y-6">
          <div className="space-y-4">
            <h4 className="text-base font-medium border-b pb-2">Assessee Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Full Name</label>
                <Input 
                  value={formData.fullName || ''} 
                  onChange={(e) => setFormData({...formData, fullName: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Date of Birth</label>
                <Input 
                  value={formData.dateOfBirth || ''} 
                  onChange={(e) => setFormData({...formData, dateOfBirth: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h4 className="text-base font-medium border-b pb-2">Contact Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Email</label>
                <Input 
                  value={formData.email || ''} 
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Phone</label>
                <Input 
                  value={formData.phone || ''} 
                  onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium">Address</label>
              <Textarea 
                value={formData.address || ''} 
                onChange={(e) => setFormData({...formData, address: e.target.value})}
                className="w-full"
                disabled={!isEditing}
              />
            </div>
          </div>
          
          <div className="space-y-4">
            <h4 className="text-base font-medium border-b pb-2">Referral Details</h4>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Concern Details</label>
                <Textarea 
                  value={formData.concernDetails || ''} 
                  onChange={(e) => setFormData({...formData, concernDetails: e.target.value})}
                  rows={4}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Additional Information</label>
                <Textarea 
                  value={formData.additionalInfo || ''} 
                  onChange={(e) => setFormData({...formData, additionalInfo: e.target.value})}
                  rows={4}
                  className="w-full"
                  disabled={!isEditing}
                />
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      // For other referral types or if the type isn't recognized
      return (
        <div className="space-y-4 px-1">
          <div className="space-y-2">
            <label className="block text-sm font-medium">Notes</label>
            <Textarea 
              value={formData.notes || ''}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              rows={10}
              className="w-full"
              disabled={!isEditing}
            />
          </div>
        </div>
      );
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-10 w-10 text-destructive mb-4" />
        <h3 className="text-lg font-medium">Error loading verification queue</h3>
        <p className="text-sm text-muted-foreground mt-2">
          {error instanceof Error ? error.message : 'An unknown error occurred'}
        </p>
      </div>
    );
  }

  if (!pendingAssessments || pendingAssessments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Verification Queue</CardTitle>
          <CardDescription>
            No referrals waiting for verification at this time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
            <CheckCircle className="h-10 w-10 mb-4 text-primary/60" />
            <p>All caught up! Check back later for new referrals.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Verification Queue</CardTitle>
        <CardDescription>
          {pendingAssessments.length} referral{pendingAssessments.length !== 1 ? 's' : ''} waiting for verification
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Assessee</TableHead>
              <TableHead>Referring Party</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Received</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {pendingAssessments.map((assessment) => (
              <TableRow key={assessment.id}>
                <TableCell className="font-medium">
                  {assessment.assessee.fullName}
                </TableCell>
                <TableCell>
                  {assessment.referringUser ? assessment.referringUser.fullName : 'Unknown'}
                  {assessment.referringUser?.organization && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {assessment.referringUser.organization}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <Badge 
                    variant="outline" 
                    className="flex items-center space-x-1"
                  >
                    {renderReferralTypeIcon(assessment.referralType)}
                    <span>
                      {assessment.referralType.charAt(0).toUpperCase() + assessment.referralType.slice(1)}
                    </span>
                  </Badge>
                </TableCell>
                <TableCell>
                  <span className="text-sm">
                    {formatDistanceToNow(new Date(assessment.createdAt), { addSuffix: true })}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => {
                        setSelectedAssessment(assessment);
                        initializeFormData(assessment);
                      }}
                    >
                      View
                    </Button>
                    <Button 
                      size="sm" 
                      variant="default" 
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() => approveReferralMutation.mutate(assessment.id)}
                      disabled={approveReferralMutation.isPending || rejectReferralMutation.isPending}
                    >
                      Approve
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => rejectReferralMutation.mutate(assessment.id)}
                      disabled={approveReferralMutation.isPending || rejectReferralMutation.isPending}
                    >
                      Reject
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
      
      {/* Modal for viewing and editing form data */}
      {selectedAssessment && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Referral Details</CardTitle>
                  <CardDescription>
                    Review the details of this referral
                  </CardDescription>
                </div>
                {selectedAssessment.notes && (
                  <Button 
                    variant={isEditing ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      if (isEditing) {
                        // Save changes
                        if (formData) {
                          updateFormDataMutation.mutate({
                            assessmentId: selectedAssessment.id,
                            formData
                          });
                        }
                      } else {
                        // Enter edit mode
                        setIsEditing(true);
                        toast({
                          title: "Edit Mode Enabled",
                          description: "You can now edit the form fields",
                        });
                      }
                    }}
                    disabled={updateFormDataMutation.isPending}
                  >
                    {updateFormDataMutation.isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        Saving...
                      </>
                    ) : isEditing ? (
                      'Save Changes'
                    ) : (
                      <>
                        <Pencil className="h-4 w-4 mr-1" />
                        Edit Form
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Assessee Information</h3>
                  <div className="mt-2 space-y-1">
                    <p className="font-medium">{selectedAssessment.assessee?.fullName || 'N/A'}</p>
                    <p className="text-sm">DOB: {selectedAssessment.assessee?.dateOfBirth ? new Date(selectedAssessment.assessee.dateOfBirth).toLocaleDateString() : 'N/A'}</p>
                    {selectedAssessment.assessee?.email && (
                      <p className="text-sm">Email: {selectedAssessment.assessee.email}</p>
                    )}
                    {selectedAssessment.assessee?.phone && (
                      <p className="text-sm">Phone: {selectedAssessment.assessee.phone}</p>
                    )}
                    {selectedAssessment.assessee?.address && (
                      <p className="text-sm">Address: {selectedAssessment.assessee.address}</p>
                    )}
                  </div>
                </div>

                {selectedAssessment.referringUser && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Referring Party</h3>
                    <div className="mt-2 space-y-1">
                      <p className="font-medium">{selectedAssessment.referringUser.fullName}</p>
                      <p className="text-sm">Email: {selectedAssessment.referringUser.email}</p>
                      {selectedAssessment.referringUser.phone && (
                        <p className="text-sm">Phone: {selectedAssessment.referringUser.phone}</p>
                      )}
                      {selectedAssessment.referringUser.organization && (
                        <p className="text-sm">Organization: {selectedAssessment.referringUser.organization}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Referral Type</h3>
                <Badge className="flex items-center space-x-1">
                  {renderReferralTypeIcon(selectedAssessment.referralType)}
                  <span>
                    {selectedAssessment.referralType.charAt(0).toUpperCase() + selectedAssessment.referralType.slice(1)} Referral
                  </span>
                </Badge>
              </div>
              
              {/* Form data from notes */}
              {selectedAssessment.notes && formData && (
                <div className="p-4 border rounded-md bg-muted/10">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-base font-medium">
                        {selectedAssessment.referralType === 'university' ? 'University Referral Form' : 
                        selectedAssessment.referralType === 'individual' ? 'Individual Referral Form' : 
                        'Referral Form Data'}
                      </h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {isEditing ? 'Make changes to the form data below' : 'Form data submitted with this referral'}
                      </p>
                    </div>
                    
                    {isEditing && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => {
                          setIsEditing(false);
                          // Reinitialize form data to discard changes
                          initializeFormData(selectedAssessment);
                        }}
                      >
                        Cancel Editing
                      </Button>
                    )}
                  </div>
                  
                  {/* Dynamic form fields based on referral type */}
                  {renderFormFields()}
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Other Details</h3>
                <p className="text-sm">
                  Created: {new Date(selectedAssessment.createdAt).toLocaleString()}
                </p>
                <p className="text-sm">
                  Payment Status: {selectedAssessment.paymentStatus.charAt(0).toUpperCase() + selectedAssessment.paymentStatus.slice(1).replace('_', ' ')}
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button 
                variant="outline" 
                onClick={() => {
                  setSelectedAssessment(null);
                  setFormData(null);
                  setIsEditing(false);
                }}
              >
                Close
              </Button>
              
              <div className="flex space-x-2">
                <Button 
                  variant="default" 
                  className="bg-green-600 hover:bg-green-700"
                  onClick={() => approveReferralMutation.mutate(selectedAssessment.id)}
                  disabled={approveReferralMutation.isPending || rejectReferralMutation.isPending}
                >
                  {approveReferralMutation.isPending ? (
                    <>
                      <Spinner className="mr-2" size="sm" /> 
                      Approving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve Referral
                    </>
                  )}
                </Button>
                <Button 
                  variant="destructive"
                  onClick={() => rejectReferralMutation.mutate(selectedAssessment.id)}
                  disabled={approveReferralMutation.isPending || rejectReferralMutation.isPending}
                >
                  {rejectReferralMutation.isPending ? (
                    <>
                      <Spinner className="mr-2" size="sm" /> 
                      Rejecting...
                    </>
                  ) : (
                    <>
                      <XCircle className="mr-2 h-4 w-4" />
                      Reject Referral
                    </>
                  )}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      )}
    </Card>
  );
}