import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { CheckCircle, XCircle, AlertCircle, User, School, Building2, Pencil, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { formatDistanceToNow } from "date-fns";
import { z } from "zod";

interface Assessment {
  id: number;
  assesseeId: number;
  referringUserId: number | null;
  referralType: 'university' | 'school' | 'individual';
  status: string;
  createdAt: string;
  notes: string | null;
  paymentStatus: string;
  assessee: {
    fullName: string;
    dateOfBirth: string;
    email: string | null;
    phone: string | null;
    address: string | null;
    parentId: number | null;
  };
  referringUser: {
    fullName: string;
    organization: string | null;
    email: string;
    phone: string | null;
  } | null;
}

export function VerificationQueue() {
  const { toast } = useToast();
  const [selectedAssessment, setSelectedAssessment] = useState<Assessment | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  // Define a type for form data based on referral type
  type FormDataType = {
    // Common fields
    notes?: string;
    additionalInfo?: string;
    
    // University specific fields
    universityName?: string;
    department?: string;
    studentName?: string;
    studentId?: string;
    course?: string;
    yearOfStudy?: string;
    disabilityOfficer?: string;
    reasonForReferral?: string;
    
    // Individual specific fields
    fullName?: string;
    dateOfBirth?: string;
    email?: string;
    phone?: string;
    address?: string;
    concernDetails?: string;
    
    // Additional fields can be added as needed
    [key: string]: any;
  };
  
  const [formData, setFormData] = useState<FormDataType | null>(null);

  // Fetch pending verification assessments
  const { data: pendingAssessments, isLoading, error } = useQuery<Assessment[]>({
    queryKey: ['/api/admin/pending-verifications'],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Mutation to approve a referral
  const approveReferralMutation = useMutation({
    mutationFn: async (assessmentId: number) => {
      const response = await apiRequest('PATCH', `/api/admin/approve-referral/${assessmentId}`, {});
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to approve referral');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Referral Approved',
        description: 'The referral has been approved and moved to the Pre-Assessment phase.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/assessments'] });
      setSelectedAssessment(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Mutation to reject a referral
  const rejectReferralMutation = useMutation({
    mutationFn: async (assessmentId: number) => {
      const response = await apiRequest('PATCH', `/api/admin/reject-referral/${assessmentId}`, {});
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to reject referral');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Referral Rejected',
        description: 'The referral has been rejected.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/assessments'] });
      setSelectedAssessment(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  
  // Mutation to update form data
  const updateFormDataMutation = useMutation({
    mutationFn: async ({ assessmentId, formData }: { assessmentId: number, formData: FormDataType }) => {
      const response = await apiRequest('PATCH', `/api/admin/update-assessment-notes/${assessmentId}`, {
        notes: JSON.stringify(formData)
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to update form data');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Form Updated',
        description: 'The form data has been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
      setIsEditing(false);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Helper to render the referral type icon
  const renderReferralTypeIcon = (type: string) => {
    switch (type) {
      case 'university':
        return <Building2 className="h-4 w-4 mr-1" />;
      case 'school':
        return <School className="h-4 w-4 mr-1" />;
      case 'individual':
        return <User className="h-4 w-4 mr-1" />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-10 w-10 text-destructive mb-4" />
        <h3 className="text-lg font-medium">Error loading verification queue</h3>
        <p className="text-sm text-muted-foreground mt-2">
          {error instanceof Error ? error.message : 'An unknown error occurred'}
        </p>
      </div>
    );
  }

  if (!pendingAssessments || pendingAssessments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Verification Queue</CardTitle>
          <CardDescription>
            No referrals waiting for verification at this time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
            <CheckCircle className="h-10 w-10 mb-4 text-primary/60" />
            <p>All caught up! Check back later for new referrals.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Verification Queue</CardTitle>
        <CardDescription>
          {pendingAssessments.length} referral{pendingAssessments.length !== 1 ? 's' : ''} waiting for verification
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Assessee</TableHead>
              <TableHead>Referring Party</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Received</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {pendingAssessments.map((assessment) => (
              <TableRow key={assessment.id}>
                <TableCell className="font-medium">
                  {assessment.assessee.fullName}
                </TableCell>
                <TableCell>
                  {assessment.referringUser ? assessment.referringUser.fullName : 'Unknown'}
                  {assessment.referringUser?.organization && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {assessment.referringUser.organization}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <Badge 
                    variant="outline" 
                    className="flex items-center space-x-1"
                  >
                    {renderReferralTypeIcon(assessment.referralType)}
                    <span>
                      {assessment.referralType.charAt(0).toUpperCase() + assessment.referralType.slice(1)}
                    </span>
                  </Badge>
                </TableCell>
                <TableCell>
                  <span className="text-sm">
                    {formatDistanceToNow(new Date(assessment.createdAt), { addSuffix: true })}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => setSelectedAssessment(assessment)}
                    >
                      View
                    </Button>
                    <Button 
                      size="sm" 
                      variant="default" 
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() => approveReferralMutation.mutate(assessment.id)}
                      disabled={approveReferralMutation.isPending || rejectReferralMutation.isPending}
                    >
                      Approve
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => rejectReferralMutation.mutate(assessment.id)}
                      disabled={approveReferralMutation.isPending || rejectReferralMutation.isPending}
                    >
                      Reject
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
      {selectedAssessment && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Referral Details</CardTitle>
              <CardDescription>
                Review the details of this referral
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Assessee Information</h3>
                  <div className="mt-2 space-y-1">
                    <p className="font-medium">{selectedAssessment.assessee.fullName}</p>
                    <p className="text-sm">DOB: {new Date(selectedAssessment.assessee.dateOfBirth).toLocaleDateString()}</p>
                    {selectedAssessment.assessee.email && (
                      <p className="text-sm">Email: {selectedAssessment.assessee.email}</p>
                    )}
                    {selectedAssessment.assessee.phone && (
                      <p className="text-sm">Phone: {selectedAssessment.assessee.phone}</p>
                    )}
                    {selectedAssessment.assessee.address && (
                      <p className="text-sm">Address: {selectedAssessment.assessee.address}</p>
                    )}
                  </div>
                </div>

                {selectedAssessment.referringUser && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Referring Party</h3>
                    <div className="mt-2 space-y-1">
                      <p className="font-medium">{selectedAssessment.referringUser.fullName}</p>
                      <p className="text-sm">Email: {selectedAssessment.referringUser.email}</p>
                      {selectedAssessment.referringUser.phone && (
                        <p className="text-sm">Phone: {selectedAssessment.referringUser.phone}</p>
                      )}
                      {selectedAssessment.referringUser.organization && (
                        <p className="text-sm">Organization: {selectedAssessment.referringUser.organization}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Referral Type</h3>
                <Badge className="flex items-center space-x-1">
                  {renderReferralTypeIcon(selectedAssessment.referralType)}
                  <span>
                    {selectedAssessment.referralType.charAt(0).toUpperCase() + selectedAssessment.referralType.slice(1)} Referral
                  </span>
                </Badge>
              </div>
              
              {/* Form data from notes based on referral type */}
              {selectedAssessment.notes && (
                <div className="p-4 border rounded-md bg-muted/10">
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <h3 className="text-base font-medium">
                        {selectedAssessment.referralType === 'university' ? 'University Referral Form' : 
                        selectedAssessment.referralType === 'individual' ? 'Individual Referral Form' : 
                        'Referral Form Data'}
                      </h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {isEditing ? 'Editing form data - make changes and save when complete' : 'View form data submitted with this referral'}
                      </p>
                    </div>
                    
                    <div className="flex space-x-2">
                      {!isEditing ? (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            console.log("Notes data:", selectedAssessment.notes);
                            try {
                              // First try to parse as JSON
                              if (selectedAssessment.notes && selectedAssessment.notes.trim().startsWith('{')) {
                                const parsedData = JSON.parse(selectedAssessment.notes);
                                console.log("Parsed JSON data:", parsedData);
                                setFormData(parsedData);
                              } else {
                                // If not valid JSON, treat as plain text and create a new object
                                console.log("Creating structured data from text");
                                const initialData = {
                                  notes: selectedAssessment.notes || '',
                                  // Add default fields based on referral type
                                  ...(selectedAssessment.referralType === 'university' 
                                    ? {
                                        universityName: selectedAssessment.referringUser?.organization || '',
                                        department: '',
                                        studentName: selectedAssessment.assessee.fullName || '',
                                        studentId: '',
                                        course: '',
                                        yearOfStudy: '',
                                        disabilityOfficer: selectedAssessment.referringUser?.fullName || '',
                                        reasonForReferral: '',
                                        additionalInfo: selectedAssessment.notes || ''
                                      }
                                    : {
                                        fullName: selectedAssessment.assessee.fullName || '',
                                        dateOfBirth: new Date(selectedAssessment.assessee.dateOfBirth).toLocaleDateString() || '',
                                        email: selectedAssessment.assessee.email || '',
                                        phone: selectedAssessment.assessee.phone || '',
                                        address: selectedAssessment.assessee.address || '',
                                        concernDetails: '',
                                        additionalInfo: selectedAssessment.notes || ''
                                      }
                                  )
                                };
                                setFormData(initialData);
                              }
                              
                              setIsEditing(true);
                              toast({
                                title: "Edit Mode Enabled",
                                description: "You can now edit the form fields",
                              });
                            } catch (error) {
                              const e = error as Error;
                              console.error("Parse error:", e);
                              toast({
                                title: "Error",
                                description: `Could not process form data: ${e.message}`,
                                variant: "destructive",
                              });
                            }
                          }}
                        >
                          <Pencil className="h-4 w-4 mr-1" />
                          Edit Form
                        </Button>
                      ) : (
                        <>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => setIsEditing(false)}
                          >
                            Cancel
                          </Button>
                          <Button 
                            variant="default" 
                            size="sm"
                            onClick={() => {
                              if (selectedAssessment && formData) {
                                updateFormDataMutation.mutate({
                                  assessmentId: selectedAssessment.id,
                                  formData
                                });
                              }
                            }}
                            disabled={updateFormDataMutation.isPending}
                          >
                            {updateFormDataMutation.isPending ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                Saving...
                              </>
                            ) : 'Save Changes'}
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                          
                          {selectedAssessment.referralType === 'university' && formData && (
                            <div className="space-y-6">
                              <div className="grid grid-cols-1 gap-4">
                                <h4 className="font-medium">University Information</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">University Name</label>
                                    <Input 
                                      value={formData.universityName || ''} 
                                      onChange={(e) => setFormData({...formData, universityName: e.target.value})}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Department</label>
                                    <Input 
                                      value={formData.department || ''} 
                                      onChange={(e) => setFormData({...formData, department: e.target.value})}
                                    />
                                  </div>
                                </div>
                              </div>
                              
                              <div className="grid grid-cols-1 gap-4">
                                <h4 className="font-medium">Student Information</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Student Name</label>
                                    <Input 
                                      value={formData.studentName || ''} 
                                      onChange={(e) => setFormData({...formData, studentName: e.target.value})}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Student ID</label>
                                    <Input 
                                      value={formData.studentId || ''} 
                                      onChange={(e) => setFormData({...formData, studentId: e.target.value})}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Course</label>
                                    <Input 
                                      value={formData.course || ''} 
                                      onChange={(e) => setFormData({...formData, course: e.target.value})}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Year of Study</label>
                                    <Input 
                                      value={formData.yearOfStudy || ''} 
                                      onChange={(e) => setFormData({...formData, yearOfStudy: e.target.value})}
                                    />
                                  </div>
                                </div>
                              </div>
                              
                              <div className="grid grid-cols-1 gap-4">
                                <h4 className="font-medium">Referral Details</h4>
                                <div className="grid grid-cols-1 gap-4">
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Disability Officer</label>
                                    <Input 
                                      value={formData.disabilityOfficer || ''} 
                                      onChange={(e) => setFormData({...formData, disabilityOfficer: e.target.value})}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Reason For Referral</label>
                                    <Textarea 
                                      value={formData.reasonForReferral || ''} 
                                      onChange={(e) => setFormData({...formData, reasonForReferral: e.target.value})}
                                      rows={4}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Additional Information</label>
                                    <Textarea 
                                      value={formData.additionalInfo || ''} 
                                      onChange={(e) => setFormData({...formData, additionalInfo: e.target.value})}
                                      rows={4}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                          
                          {selectedAssessment.referralType === 'individual' && formData && (
                            <div className="space-y-6">
                              <div className="grid grid-cols-1 gap-4">
                                <h4 className="font-medium">Assessee Information</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Full Name</label>
                                    <Input 
                                      value={formData.fullName || ''} 
                                      onChange={(e) => setFormData({...formData, fullName: e.target.value})}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Date of Birth</label>
                                    <Input 
                                      value={formData.dateOfBirth || ''} 
                                      onChange={(e) => setFormData({...formData, dateOfBirth: e.target.value})}
                                    />
                                  </div>
                                </div>
                              </div>
                              
                              <div className="grid grid-cols-1 gap-4">
                                <h4 className="font-medium">Contact Information</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Email</label>
                                    <Input 
                                      value={formData.email || ''} 
                                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Phone</label>
                                    <Input 
                                      value={formData.phone || ''} 
                                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Address</label>
                                    <Input 
                                      value={formData.address || ''} 
                                      onChange={(e) => setFormData({...formData, address: e.target.value})}
                                    />
                                  </div>
                                </div>
                              </div>
                              
                              <div className="grid grid-cols-1 gap-4">
                                <h4 className="font-medium">Referral Details</h4>
                                <div className="grid grid-cols-1 gap-4">
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Concern Details</label>
                                    <Textarea 
                                      value={formData.concernDetails || ''} 
                                      onChange={(e) => setFormData({...formData, concernDetails: e.target.value})}
                                      rows={4}
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <label className="text-sm text-muted-foreground">Additional Information</label>
                                    <Textarea 
                                      value={formData.additionalInfo || ''} 
                                      onChange={(e) => setFormData({...formData, additionalInfo: e.target.value})}
                                      rows={4}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      // Display mode (non-editing)
                      (() => {
                        console.log("Attempting to parse for display:", selectedAssessment.notes);
                        
                        // Check if the notes field is valid JSON
                        let formData: FormDataType = {};
                        let isValidJson = false;
                        
                        try {
                          if (selectedAssessment.notes && selectedAssessment.notes.trim().startsWith('{')) {
                            formData = JSON.parse(selectedAssessment.notes);
                            isValidJson = true;
                          }
                          
                          if (selectedAssessment.referralType === 'university') {
                            return (
                              <div className="space-y-4">
                                <div className="grid grid-cols-1 gap-3">
                                  <h4 className="font-medium">University Information</h4>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">University Name</p>
                                      <p>{formData.universityName || selectedAssessment.referringUser?.organization || 'Not specified'}</p>
                                    </div>
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Department</p>
                                      <p>{formData.department || 'Not specified'}</p>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="space-y-3">
                                  <h4 className="font-medium">Student Information</h4>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Student Name</p>
                                      <p>{formData.studentName || 'Not specified'}</p>
                                    </div>
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Student ID</p>
                                      <p>{formData.studentId || 'Not specified'}</p>
                                    </div>
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Course</p>
                                      <p>{formData.course || 'Not specified'}</p>
                                    </div>
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Year of Study</p>
                                      <p>{formData.yearOfStudy || 'Not specified'}</p>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="space-y-3">
                                  <h4 className="font-medium">Referral Details</h4>
                                  <div className="grid grid-cols-1 gap-3">
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Disability Officer</p>
                                      <p>{formData.disabilityOfficer || 'Not specified'}</p>
                                    </div>
                                    {formData.reasonForReferral && (
                                      <div className="border rounded p-2 bg-white">
                                        <p className="text-xs text-muted-foreground">Reason For Referral</p>
                                        <p className="whitespace-pre-wrap">{formData.reasonForReferral}</p>
                                      </div>
                                    )}
                                    {formData.additionalInfo && (
                                      <div className="border rounded p-2 bg-white">
                                        <p className="text-xs text-muted-foreground">Additional Information</p>
                                        <p className="whitespace-pre-wrap">{formData.additionalInfo}</p>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          } else if (selectedAssessment.referralType === 'individual') {
                            return (
                              <div className="space-y-4">
                                <div className="grid grid-cols-1 gap-3">
                                  <h4 className="font-medium">Assessee Information</h4>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Full Name</p>
                                      <p>{formData.fullName || selectedAssessment.assessee.fullName}</p>
                                    </div>
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Date of Birth</p>
                                      <p>{formData.dateOfBirth || new Date(selectedAssessment.assessee.dateOfBirth).toLocaleDateString()}</p>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="space-y-3">
                                  <h4 className="font-medium">Contact Information</h4>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Email</p>
                                      <p>{formData.email || selectedAssessment.assessee.email || 'Not specified'}</p>
                                    </div>
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Phone</p>
                                      <p>{formData.phone || selectedAssessment.assessee.phone || 'Not specified'}</p>
                                    </div>
                                    <div className="border rounded p-2 bg-white">
                                      <p className="text-xs text-muted-foreground">Address</p>
                                      <p>{formData.address || selectedAssessment.assessee.address || 'Not specified'}</p>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="space-y-3">
                                  <h4 className="font-medium">Referral Details</h4>
                                  <div className="grid grid-cols-1 gap-3">
                                    {formData.concernDetails && (
                                      <div className="border rounded p-2 bg-white">
                                        <p className="text-xs text-muted-foreground">Concern Details</p>
                                        <p className="whitespace-pre-wrap">{formData.concernDetails}</p>
                                      </div>
                                    )}
                                    {formData.additionalInfo && (
                                      <div className="border rounded p-2 bg-white">
                                        <p className="text-xs text-muted-foreground">Additional Information</p>
                                        <p className="whitespace-pre-wrap">{formData.additionalInfo}</p>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          } else if (isValidJson) {
                            return (
                              <div className="p-4 border rounded">
                                <pre className="text-xs overflow-auto max-h-60 whitespace-pre-wrap">
                                  {JSON.stringify(formData, null, 2)}
                                </pre>
                              </div>
                            );
                          } else {
                            // For plain text notes
                            return (
                              <div className="space-y-4">
                                <div className="grid grid-cols-1 gap-3">
                                  <h4 className="font-medium">Notes</h4>
                                  <div className="border rounded p-3 bg-white">
                                    <p className="whitespace-pre-wrap">{selectedAssessment.notes}</p>
                                  </div>
                                </div>
                              </div>
                            );
                          }
                        } catch (error) {
                          const e = error as Error;
                          console.error("Parse error during display:", e);
                          // If notes is not valid JSON, display as plain text
                          return (
                            <div className="border rounded p-2 bg-white">
                              <p className="whitespace-pre-wrap">{selectedAssessment.notes}</p>
                            </div>
                          );
                        }
                      })()
                    )}
                  </div>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Other Details</h3>
                <p className="text-sm">
                  Created: {new Date(selectedAssessment.createdAt).toLocaleString()}
                </p>
                <p className="text-sm">
                  Payment Status: {selectedAssessment.paymentStatus.charAt(0).toUpperCase() + selectedAssessment.paymentStatus.slice(1).replace('_', ' ')}
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-2">
              <Button 
                variant="outline" 
                onClick={() => setSelectedAssessment(null)}
              >
                Close
              </Button>
              <Button 
                variant="default" 
                className="bg-green-600 hover:bg-green-700"
                onClick={() => approveReferralMutation.mutate(selectedAssessment.id)}
                disabled={approveReferralMutation.isPending || rejectReferralMutation.isPending}
              >
                {approveReferralMutation.isPending ? (
                  <>
                    <Spinner className="mr-2" size="sm" /> 
                    Approving...
                  </>
                ) : (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve Referral
                  </>
                )}
              </Button>
              <Button 
                variant="destructive"
                onClick={() => rejectReferralMutation.mutate(selectedAssessment.id)}
                disabled={approveReferralMutation.isPending || rejectReferralMutation.isPending}
              >
                {rejectReferralMutation.isPending ? (
                  <>
                    <Spinner className="mr-2" size="sm" /> 
                    Rejecting...
                  </>
                ) : (
                  <>
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject Referral
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}
    </Card>
  );
}