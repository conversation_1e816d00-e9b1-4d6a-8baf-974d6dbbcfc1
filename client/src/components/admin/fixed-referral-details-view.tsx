import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardFooter 
} from "@/components/ui/card";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Form, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormDescription, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Spinner } from "@/components/ui/spinner";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  ChevronLeft,
  Save,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Building2,
  School,
  FileText,
  Clock,
  Tag,
  Edit,
  Loader2
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Define types for our data model
interface Assessment {
  id: number;
  assesseeId: number;
  referringUserId: number | null;
  referralType: 'university' | 'school' | 'individual';
  status: string;
  createdAt: string;
  notes: string | null;
  paymentStatus: string;
  assessee: {
    id: number;
    fullName: string;
    dateOfBirth: string;
    email: string | null;
    phone: string | null;
    address: string | null;
    courseProgram: string | null;
    yearOfStudy: string | null;
    parentId: number | null;
    parent?: {
      id: number;
      fullName: string;
      email: string;
      phone: string | null;
    } | null;
  };
  referringParty?: {
    id: number;
    fullName: string;
    organization: string | null;
    department: string | null;
    position: string | null;
    email: string;
    phone: string | null;
    role: string;
  } | null;
  referringUser?: {
    id: number;
    fullName: string;
    organization: string | null;
    department: string | null;
    position: string | null;
    email: string;
    phone: string | null;
    role: string;
  } | null;
  referringSchool?: {
    id: number;
    name: string;
    address: string | null;
    contactName: string | null;
    contactEmail: string | null;
    contactPhone: string | null;
  } | null;
}

// Create a schema for form validation
const referralFormSchema = z.object({
  // Assessee information
  assesseeFullName: z.string().min(1, "Assessee name is required"),
  assesseeDateOfBirth: z.string().optional(),
  assesseeEmail: z.string().email().optional().nullable(),
  assesseePhone: z.string().optional().nullable(),
  assesseeAddress: z.string().optional().nullable(),
  
  // Parent information (for under 16)
  parentFullName: z.string().optional().nullable(),
  parentEmail: z.string().email().optional().nullable(),
  parentPhone: z.string().optional().nullable(),
  parentRelationship: z.string().optional().nullable(),
  
  // Referring party information
  referringPartyName: z.string().optional().nullable(),
  referringPartyOrganization: z.string().optional().nullable(),
  referringPartyPosition: z.string().optional().nullable(),
  referringPartyEmail: z.string().email().optional().nullable(),
  referringPartyPhone: z.string().optional().nullable(),
  
  // School specific
  schoolName: z.string().optional().nullable(),
  schoolAddress: z.string().optional().nullable(),
  schoolContactName: z.string().optional().nullable(),
  schoolContactEmail: z.string().email().optional().nullable(),
  schoolContactPhone: z.string().optional().nullable(),
  
  // University specific
  departmentName: z.string().optional().nullable(),
  studentId: z.string().optional().nullable(),
  courseDetails: z.string().optional().nullable(),
  yearOfStudy: z.string().optional().nullable(),
  disabilityOfficer: z.string().optional().nullable(),
  
  // Common fields
  concernDetails: z.string().optional().nullable(),
  reasonForReferral: z.string().optional().nullable(),
  additionalInformation: z.string().optional().nullable(),
  internalNotes: z.string().optional().nullable(),
});

type ReferralFormValues = z.infer<typeof referralFormSchema>;

interface ReferralDetailsViewProps {
  assessmentId: number;
  onBack: () => void;
  onApprove?: (assessmentId: number) => void;
  onReject?: (assessmentId: number) => void;
  extraActions?: React.ReactNode;
}

export function ReferralDetailsView({
  assessmentId,
  onBack,
  onApprove,
  onReject,
  extraActions
}: ReferralDetailsViewProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Fetch assessment details
  const { 
    data: assessment, 
    isLoading, 
    error,
    refetch
  } = useQuery<Assessment>({
    queryKey: ['/api/admin/assessments', assessmentId],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/admin/assessments/${assessmentId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch assessment details');
      }
      return response.json();
    }
  });
  
  // Setup form with react-hook-form
  const form = useForm<ReferralFormValues>({
    resolver: zodResolver(referralFormSchema),
    defaultValues: {
      assesseeFullName: '',
      assesseeDateOfBirth: '',
      assesseeEmail: '',
      assesseePhone: '',
      assesseeAddress: '',
      parentFullName: '',
      parentEmail: '',
      parentPhone: '',
      parentRelationship: '',
      referringPartyName: '',
      referringPartyOrganization: '',
      referringPartyPosition: '',
      referringPartyEmail: '',
      referringPartyPhone: '',
      schoolName: '',
      schoolAddress: '',
      schoolContactName: '',
      schoolContactEmail: '',
      schoolContactPhone: '',
      departmentName: '',
      studentId: '',
      courseDetails: '',
      yearOfStudy: '',
      disabilityOfficer: '',
      concernDetails: '',
      reasonForReferral: '',
      additionalInformation: '',
      internalNotes: '',
    }
  });
  
  // Update form when assessment data is loaded
  useEffect(() => {
    if (assessment) {
      // Extract additional data from notes field if it exists
      let additionalData = {};
      if (assessment.notes) {
        try {
          // Try to parse as JSON first
          additionalData = JSON.parse(assessment.notes);
        } catch (e) {
          // If not valid JSON, use as plain text
          additionalData = { additionalInformation: assessment.notes };
        }
      }
      
      // Set values in the form
      form.reset({
        assesseeFullName: assessment.assessee?.fullName || '',
        assesseeDateOfBirth: assessment.assessee?.dateOfBirth ?
          format(new Date(assessment.assessee.dateOfBirth), 'yyyy-MM-dd') : '',
        assesseeEmail: assessment.assessee?.email || '',
        assesseePhone: assessment.assessee?.phone || '',
        assesseeAddress: assessment.assessee?.address || '',
        
        // Parent info (if available)
        parentFullName: assessment.assessee.parent?.fullName || '',
        parentEmail: assessment.assessee.parent?.email || '',
        parentPhone: assessment.assessee.parent?.phone || '',
        
        // Referring party info
        referringPartyName: assessment.referringUser?.fullName || assessment.referringParty?.fullName || '',
        referringPartyOrganization: assessment.referringUser?.organization || assessment.referringParty?.organization || '',
        referringPartyPosition: assessment.referringUser?.position || assessment.referringParty?.position || '',
        referringPartyEmail: assessment.referringUser?.email || assessment.referringParty?.email || '',
        referringPartyPhone: assessment.referringUser?.phone || assessment.referringParty?.phone || '',
        
        // School specific (if available)
        schoolName: assessment.referringSchool?.name || '',
        schoolAddress: assessment.referringSchool?.address || '',
        schoolContactName: assessment.referringSchool?.contactName || '',
        schoolContactEmail: assessment.referringSchool?.contactEmail || '',
        schoolContactPhone: assessment.referringSchool?.contactPhone || '',
        
        // Specific fields based on assessee data and additional data
        departmentName: assessment.referringUser?.department || assessment.referringParty?.department || 
                       (additionalData as any).departmentName || '',
        studentId: (additionalData as any).studentId || '',
        // Use the dedicated assessee fields first, then fall back to the additional data in notes
        courseDetails: assessment.assessee?.courseProgram ||
                      (additionalData as any).course ||
                      (additionalData as any).courseDetails || '',
        yearOfStudy: assessment.assessee?.yearOfStudy ||
                    (additionalData as any).yearOfStudy || '',
        disabilityOfficer: (additionalData as any).disabilityOfficer || '',
        
        // Common fields
        concernDetails: (additionalData as any).concernDetails || '',
        reasonForReferral: (additionalData as any).reasonForReferral || '',
        additionalInformation: (additionalData as any).additionalInfo || 
                              (additionalData as any).additionalInformation || '',
        internalNotes: (additionalData as any).internalNotes || '',
      });
    }
  }, [assessment, form]);
  
  // Mutation to save form changes
  const updateReferralMutation = useMutation({
    mutationFn: async (formData: ReferralFormValues) => {
      // First update assessee information
      if (assessment) {
        // Convert form data to match our database structure and API expectations
        const notes = JSON.stringify({
          departmentName: formData.departmentName,
          studentId: formData.studentId,
          courseDetails: formData.courseDetails,
          yearOfStudy: formData.yearOfStudy,
          disabilityOfficer: formData.disabilityOfficer,
          concernDetails: formData.concernDetails,
          reasonForReferral: formData.reasonForReferral,
          additionalInformation: formData.additionalInformation,
          internalNotes: formData.internalNotes,
        });
        
        // Update assessment notes
        const assessmentResponse = await apiRequest('PATCH', `/api/admin/update-assessment-notes/${assessmentId}`, {
          notes
        });
        
        if (!assessmentResponse.ok) {
          throw new Error('Failed to update assessment data');
        }
        
        // Update assessee information
        const assesseeResponse = await apiRequest('PATCH', `/api/admin/update-assessee/${assessment.assesseeId}`, {
          fullName: formData.assesseeFullName,
          // Properly handle the date by converting string to Date object
          dateOfBirth: formData.assesseeDateOfBirth ? new Date(formData.assesseeDateOfBirth) : null,
          email: formData.assesseeEmail || null,
          phone: formData.assesseePhone || null,
          address: formData.assesseeAddress || null,
          // Include university specific fields when updating assessee
          courseProgram: formData.courseDetails || null,
          yearOfStudy: formData.yearOfStudy || null,
        });
        
        if (!assesseeResponse.ok) {
          throw new Error('Failed to update assessee data');
        }
        
        // If this is a university referral and we have a referring user ID, update the user data
        if (assessment.referralType === 'university' && assessment.referringUserId) {
          const referringUserResponse = await apiRequest('PATCH', `/api/admin/update-user/${assessment.referringUserId}`, {
            fullName: formData.referringPartyName || null,
            organization: formData.referringPartyOrganization || null,
            position: formData.referringPartyPosition || null,
            department: formData.departmentName || null, // Store department directly in user record
            phone: formData.referringPartyPhone || null,
          });
          
          if (!referringUserResponse.ok) {
            // Log the error but don't fail the overall update
            console.error('Failed to update referring user data');
          }
        }
        
        return assessmentResponse.json();
      }
      
      throw new Error('Assessment not found');
    },
    onSuccess: () => {
      toast({
        title: 'Changes saved',
        description: 'Referral information has been updated successfully',
      });
      refetch();
      queryClient.invalidateQueries({ queryKey: ['/api/admin/pending-verifications'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
    onSettled: () => {
      setIsSubmitting(false);
    }
  });
  
  const onSubmit = (data: ReferralFormValues) => {
    setIsSubmitting(true);
    updateReferralMutation.mutate(data);
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Spinner size="lg" className="mb-4" />
            <p className="text-muted-foreground">Loading referral details...</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  // Render error state
  if (error || !assessment) {
    return (
      <Card className="w-full">
        <CardContent className="py-6">
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error instanceof Error ? error.message : 'Failed to load referral details'}
            </AlertDescription>
          </Alert>
          <Button onClick={onBack} variant="outline">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Queue
          </Button>
        </CardContent>
      </Card>
    );
  }
  
  // Helper function to render the referral badge
  const getReferralTypeBadge = () => {
    switch (assessment.referralType) {
      case 'university':
        return (
          <Badge variant="outline" className="flex items-center space-x-1">
            <Building2 className="h-4 w-4 mr-1" />
            <span>University</span>
          </Badge>
        );
      case 'school':
        return (
          <Badge variant="outline" className="flex items-center space-x-1">
            <School className="h-4 w-4 mr-1" />
            <span>School</span>
          </Badge>
        );
      case 'individual':
        return (
          <Badge variant="outline" className="flex items-center space-x-1">
            <User className="h-4 w-4 mr-1" />
            <span>Individual</span>
          </Badge>
        );
      default:
        return null;
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center mb-4">
          <Button 
            variant="ghost" 
            onClick={onBack} 
            className="mr-2 p-0 h-8 w-8"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div>
            <CardTitle className="text-xl flex items-center">
              Referral Details
              <span className="ml-3">
                {getReferralTypeBadge()}
              </span>
            </CardTitle>
            <CardDescription className="mt-1">
              Submitted {format(new Date(assessment.createdAt), 'PPP')} • Referral ID: {assessment.id}
            </CardDescription>
          </div>
        </div>
        <div className="bg-muted/40 rounded-md p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-muted-foreground mb-1">Assessee</p>
            <p className="font-medium">{assessment.assessee?.fullName || 'N/A'}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground mb-1">Referring Party</p>
            <p className="font-medium">
              {assessment.referringUser ? assessment.referringUser.fullName : 
                assessment.referringSchool ? assessment.referringSchool.name : 'Self-referred'}
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground mb-1">Status</p>
            <Badge variant={assessment.status === 'VerificationPending' ? 'outline' : 'default'}>
              {assessment.status}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-0 py-0">
        <Tabs defaultValue="student">
          <div className="px-6 pt-4 border-b">
            <TabsList className="w-full justify-start">
              {(assessment.referralType === 'university' || assessment.referralType === 'individual') && (
                <TabsTrigger value="student">Student Details</TabsTrigger>
              )}
              {assessment.referralType !== 'university' && (
                <TabsTrigger value="contact">Contact Information</TabsTrigger>
              )}
              {(assessment.referralType === 'individual' || (
                assessment.assessee.dateOfBirth &&
                new Date(assessment.assessee.dateOfBirth) > new Date(new Date().setFullYear(new Date().getFullYear() - 16))
              )) && (
                <TabsTrigger value="parent">Parent/Guardian Details</TabsTrigger>
              )}
              {assessment.referralType === 'university' && (
                <TabsTrigger value="university">University Details</TabsTrigger>
              )}
              {(assessment.referralType === 'school' || assessment.referralType === 'individual') && (
                <TabsTrigger value="school">School Details</TabsTrigger>
              )}
              <TabsTrigger value="notes">
                {assessment.referralType === 'individual' ? 'Assessment Details' : 'Notes & Additional Info'}
              </TabsTrigger>
            </TabsList>
          </div>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="px-6 py-4">
                {/* Student Details Tab for both university and individual referrals */}
                {(assessment.referralType === 'university' || assessment.referralType === 'individual') && (
                  <TabsContent value="student" className="mt-0 space-y-4 px-6 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="assesseeFullName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Full Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="assesseeDateOfBirth"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Date of Birth</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    
                      <FormField
                        control={form.control}
                        name="assesseeEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="assesseePhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="assesseeAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Address</FormLabel>
                          <FormControl>
                            <Textarea rows={2} {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                      <FormField
                        control={form.control}
                        name="studentId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Student ID</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="courseDetails"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Course/Program</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="yearOfStudy"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Year of Study</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="disabilityOfficer"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Disability Officer</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>
                )}
                
                {/* Parent/Guardian Details Tab */}
                {(assessment.referralType === 'individual' || (
                  assessment.assessee.dateOfBirth &&
                  new Date(assessment.assessee.dateOfBirth) > new Date(new Date().setFullYear(new Date().getFullYear() - 16))
                )) && (
                  <TabsContent value="parent" className="mt-0 space-y-4 px-6 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="parentFullName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Full Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    
                      <FormField
                        control={form.control}
                        name="parentEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="parentPhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>
                )}
                
                {assessment.referralType === 'university' && (
                  <TabsContent value="university" className="mt-0 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="referringPartyOrganization"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>University Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="departmentName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Department Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="referringPartyName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff Member Name</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="referringPartyPosition"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff Position</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="referringPartyEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Staff Email</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="referringPartyPhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Contact Phone</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                  </TabsContent>
                )}
                
                {(assessment.referralType === 'school' || assessment.referralType === 'individual') && (
                  <TabsContent value="school" className="mt-0 space-y-4">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">School Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="schoolName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>School Name</FormLabel>
                              <FormControl>
                                <Input {...field} value={field.value || ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="schoolAddress"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Address</FormLabel>
                            <FormControl>
                              <Textarea rows={2} {...field} value={field.value || ''} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">School Contact</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="schoolContactName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Contact Person</FormLabel>
                              <FormControl>
                                <Input {...field} value={field.value || ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="schoolContactEmail"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Contact Email</FormLabel>
                              <FormControl>
                                <Input {...field} value={field.value || ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="schoolContactPhone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Contact Phone</FormLabel>
                              <FormControl>
                                <Input {...field} value={field.value || ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </TabsContent>
                )}
                
                <TabsContent value="notes" className="mt-0 space-y-4">
                  <FormField
                    control={form.control}
                    name="reasonForReferral"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reason for Referral</FormLabel>
                        <FormControl>
                          <Textarea rows={3} {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="concernDetails"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Has the student had any previous SpLD assessments?</FormLabel>
                        <FormControl>
                          <Textarea rows={3} {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="additionalInformation"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Additional Information</FormLabel>
                        <FormControl>
                          <Textarea rows={3} {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="internalNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Admin Notes</FormLabel>
                        <FormControl>
                          <Textarea rows={3} {...field} value={field.value || ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
              </div>
              
              <CardFooter className="flex justify-between border-t px-6 py-4">
                <div className="flex space-x-2">
                  <Button 
                    type="button"
                    onClick={() => {
                      console.log("Save button clicked");
                      onSubmit(form.getValues());
                    }}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </div>
                
                <div className="flex space-x-2">
                  {onApprove && (
                    <Button 
                      variant="default"
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() => onApprove(assessmentId)}
                      disabled={isSubmitting}
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve Referral
                    </Button>
                  )}
                  
                  {onReject && (
                    <Button 
                      variant="destructive"
                      onClick={() => onReject(assessmentId)}
                      disabled={isSubmitting}
                    >
                      <XCircle className="mr-2 h-4 w-4" />
                      Reject Referral
                    </Button>
                  )}
                  
                  {extraActions}
                </div>
              </CardFooter>
            </form>
          </Form>
        </Tabs>
      </CardContent>
    </Card>
  );
}