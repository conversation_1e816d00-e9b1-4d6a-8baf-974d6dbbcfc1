import * as React from "react";
import { cn } from "@/lib/utils";

export interface StableInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

/**
 * Stable input that works just like a normal controlled input,
 * avoiding any local state or unnecessary complexity.
 */
const StableInput = React.forwardRef<HTMLInputElement, StableInputProps>(
  ({ className, value, onChange, type, ...props }, ref) => (
    <input
      type={type}
      className={cn(
        "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      ref={ref}
      value={value}
      onChange={onChange}
      {...props}
    />
  )
);

StableInput.displayName = "StableInput";

export { StableInput };