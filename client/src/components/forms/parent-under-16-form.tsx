import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { StableInput } from "@/components/forms/stable-input";
import { COUNTRIES } from "@/constants/countries";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChildDetailsSection,
  ParentGuardianSection,
  ReasonsForReferralSection,
  VisionDifficultiesSection,
  LanguageHistorySection,
  FamilialHistorySection,
  HealthAndDevelopmentalHistorySection,
  CurrentSituationSection,
  LiteracySection,
  NumeracySection,
  MemoryAttentionSection,
  SpeechLanguageCommunicationSection,
  OrganisationalSkillsSection,
  MotorSkillsSection,
  StrengthsSection
} from "./parent-under-16-sections";
import { SignatureInput } from "./signature-input";
import { useToast } from "@/hooks/use-toast";
import { useState, useEffect, useRef, useCallback } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Save } from "lucide-react";

// Create schema based on the Parent Pre-Assessment Questionnaire for under 16
const parentFormSchema = z.object({
  // Child details
  childFullName: z.string().min(2, "Name must be at least 2 characters"),
  childPreferredName: z.string().optional(),
  childDateOfBirth: z.string().min(1, "Date of birth is required"),
  childAge: z.string().min(1, "Age is required"),
  childSchoolYear: z.string().min(1, "School year is required"),
  childCountryOfBirth: z.enum(COUNTRIES, {
    required_error: "Country of birth is required",
  }),
  childDateMovedToUK: z.string().optional(),
  childAdopted: z.enum(["yes", "no", "prefer_not_to_say"]).optional(),
  childGender: z.enum(["male", "female", "neutral", "prefer_not_to_say"]),
  childPreferredPronoun: z.enum(["he", "she", "they", "other"]).optional(),
  childPreferredPronounOther: z.string().optional(),
  requestingParentTitle: z.enum(["mr", "mrs", "miss", "other"]).optional(),
  requestingParentTitleOther: z.string().optional(),
  requestingParentName: z.string().min(2, "Name must be at least 2 characters").optional(),
  childContactAddress: z.string().min(5, "Address is required").optional(),
  childContactPhone: z.string().min(5, "Phone number is required").optional(),
  childContactPhoneWork: z.string().optional(),
  childContactEmail: z.string().email("Please enter a valid email").optional(),
  childHearAbout: z.array(z.string()).optional(),
  childHearAboutOther: z.string().optional(),
  legalResponsibilityAgreement: z.enum(["yes", "no"]).optional(),

  // Reasons for referral
  referralConcerns: z.string().optional(),
  childViews: z.string().optional(),

  // Parent/Guardian details moved to section 2

  // Health and developmental history
  birthPregnancyDifficulties: z.enum(["yes", "no"]).optional(),
  birthPregnancyDifficultiesDetails: z.string().optional(),
  developmentalMilestonesOnTime: z.enum(["yes", "no"]).optional(),
  developmentalMilestonesDetails: z.string().optional(),
  seenOtherSpecialists: z.enum(["yes", "no"]).optional(),
  seenOtherSpecialistsDetails: z.string().optional(),
  seriousIllnesses: z.enum(["yes", "no"]).optional(),
  seriousIllnessesDetails: z.string().optional(),
  currentMedication: z.enum(["yes", "no"]).optional(),
  currentMedicationDetails: z.string().optional(),
  hearingHistoricalIssues: z.enum(["yes", "no"]).optional(),
  hearingHistoricalIssuesDetails: z.string().optional(),

  // Health and developmental history - additional
  passedPhonicsTest: z.enum(["yes", "no"]).optional(),
  passedPhonicsTestDetails: z.string().optional(),
  schoolingDisrupted: z.enum(["yes", "no"]).optional(),
  schoolingDisruptedDetails: z.string().optional(),
  covidEducationalExperience: z.string().optional(),
  teachersDiscussedDifficulties: z.enum(["yes", "no"]).optional(),
  teachersDiscussedDifficultiesDetails: z.string().optional(),
  childSeenOtherSpecialists: z.enum(["yes", "no"]).optional(),
  childSeenOtherSpecialistsDetails: z.string().optional(),
  receivedSupportIntervention: z.enum(["yes", "no"]).optional(),
  receivedSupportInterventionDetails: z.string().optional(),
  supportReading: z.array(z.string()).optional(),
  supportMaths: z.array(z.string()).optional(),
  supportSpelling: z.array(z.string()).optional(),
  supportWriting: z.array(z.string()).optional(),

  // Current situation
  currentLevelEnglish: z.string().optional(),
  currentLevelMaths: z.string().optional(),
  difficultyReadingSeverity: z.enum(["Slight", "Moderate", "Severe"]).optional(),
  difficultySpellingSeverity: z.enum(["Slight", "Moderate", "Severe"]).optional(),
  difficultyWritingSeverity: z.enum(["Slight", "Moderate", "Severe"]).optional(),
  difficultyMathsSeverity: z.enum(["Slight", "Moderate", "Severe"]).optional(),
  difficultySportsGamesSeverity: z.enum(["Slight", "Moderate", "Severe"]).optional(),
  specialistHelpAtSchool: z.enum(["yes", "no"]).optional(),
  specialistHelpAtSchoolDetails: z.string().optional(),
  tuitionOutsideSchool: z.enum(["yes", "no"]).optional(),
  tuitionOutsideSchoolDetails: z.string().optional(),

  // Language and linguistic history
  otherLanguagesSpoken: z.enum(["yes", "no"]).optional(),
  otherLanguagesSpokenDetails: z.string().optional(),

  // Literacy
  literacyOverview: z.string().optional(),
  difficultyRecallingAlphabet: z.enum(["yes", "no"]).optional(),
  difficultyRecallingAlphabetDetails: z.string().optional(),

  // Difficulties section removed
  familyLearningDifficulties: z.enum(["yes", "no"]),
  familyLearningDifficultiesDetails: z.string().optional(),

  // Vision difficulties
  visualHistory: z.enum(["yes", "no"]).optional(),
  visualHistoryDetails: z.string().optional(),
  lastSightTestDate: z.string().optional(),
  prescriptionMade: z.enum(["yes", "no"]).optional(),
  prescriptionDetails: z.string().optional(),
  wearPrescribedGlasses: z.enum(["yes", "no"]).optional(),
  usedColouredOverlays: z.enum(["yes", "no"]).optional(),
  usedColouredOverlaysDetails: z.string().optional(),
  screenTimeHours: z.string().optional(),
  readingHours: z.string().optional(),
  increasedScreenTime: z.string().optional(),
  visionQ1: z.string().optional(),
  visionQ2: z.string().optional(),
  visionQ3: z.string().optional(),
  visionQ4: z.string().optional(),
  visionQ5: z.string().optional(),
  visionQ6: z.string().optional(),
  visionQ7: z.string().optional(),
  visionQ8: z.string().optional(),
  visionQ9: z.string().optional(),
  visionQ10: z.string().optional(),
  visionQ11: z.string().optional(),
  visionQ12: z.string().optional(),
  visionQ13: z.string().optional(),
  visionQ14: z.string().optional(),
  visionQ15: z.string().optional(),
  visionQ16: z.string().optional(),

  // Numeracy
  numeracyOverview: z.string().optional(),
  difficultyTellingTime: z.enum(["yes", "no"]).optional(),
  difficultyTellingTimeDetails: z.string().optional(),

  // Memory, attention and concentration
  memoryAttentionConcentrationDifficulties: z.enum(["yes", "no"]).optional(),
  memoryAttentionConcentrationDetails: z.string().optional(),

  // Speech, language, communication and social skills
  speechLanguageCommunicationDifficulties: z.enum(["yes", "no"]).optional(),
  speechLanguageCommunicationDifficultiesDetails: z.string().optional(),
  socialSkillsBehaviourPeerRelationshipsDifficulties: z.enum(["yes", "no"]).optional(),
  socialSkillsBehaviourPeerRelationshipsDifficultiesDetails: z.string().optional(),
  selfEsteemConfidenceDifficulties: z.enum(["yes", "no"]).optional(),
  selfEsteemConfidenceDifficultiesDetails: z.string().optional(),

  // Organisational skills
  goodOrganisationalSkills: z.enum(["yes", "no"]).optional(),
  goodOrganisationalSkillsDetails: z.string().optional(),

  // Motor skills
  fineGrossMotorSkillsDifficulties: z.enum(["yes", "no"]).optional(),
  fineGrossMotorSkillsDifficultiesDetails: z.string().optional(),
  leftRightConfusion: z.enum(["yes", "no"]).optional(),
  leftRightConfusionDetails: z.string().optional(),

  // Strengths
  childStrengths: z.string().optional(),

  // Signature and agreement
  parentSignature: z.string().optional(),
  parentPrintName: z.string().optional(),
  parentRelationshipToChild: z.string().optional(),
  parentSignatureDate: z.string().optional(),

  // Summary section removed along with vision/hearing and agreement fields
});

export type ParentFormValues = z.infer<typeof parentFormSchema>;

interface ParentUnder16FormProps {
  formId: number;
  initialData?: any;
  formData?: any;
  onSubmit: (data: any) => void;
  onSaveDraft: (data: ParentFormValues) => void;
}

export function ParentUnder16Form({
  formId,
  initialData,
  formData,
  onSubmit,
  onSaveDraft,
}: ParentUnder16FormProps) {
  const { toast } = useToast();
  const [currentSection, setCurrentSection] = useState(() => {
    // Try to get saved section from localStorage
    const savedSection = localStorage.getItem(`parentForm-${formId}-section`);
    return savedSection ? parseInt(savedSection) : 1;
  });

  // For accordion functionality
  const [openSection, setOpenSection] = useState<string>(() => {
    const savedSection = localStorage.getItem(`parentForm-${formId}-section`);
    return savedSection ? `section${savedSection}` : "";
  });

  const isInitialRender = useRef(true);
  // Form now has 15 sections including new areas
  const totalSections = 15;

  // Create form with initialData if available, or default values
  const form = useForm<ParentFormValues>({
    resolver: zodResolver(parentFormSchema),
    defaultValues: initialData
      ? {
          ...initialData,
          // Ensure required fields have fallback values
          childGender: initialData.childGender || "prefer_not_to_say",
          childPreferredPronoun: initialData.childPreferredPronoun || "they",
          referralConcerns: initialData.referralConcerns || "",
          childViews: initialData.childViews || "",
        }
        : {
          childGender: "prefer_not_to_say",
          childPreferredPronoun: "they",
          childAdopted: undefined,
          legalResponsibilityAgreement: undefined,
          birthPregnancyDifficulties: undefined,
          developmentalMilestonesOnTime: undefined,
          seenOtherSpecialists: undefined,
          seriousIllnesses: undefined,
          currentMedication: undefined,
          hearingHistoricalIssues: undefined,
          passedPhonicsTest: undefined,
          passedPhonicsTestDetails: "",
          schoolingDisrupted: undefined,
          schoolingDisruptedDetails: "",
          covidEducationalExperience: "",
          teachersDiscussedDifficulties: undefined,
          teachersDiscussedDifficultiesDetails: "",
          childSeenOtherSpecialists: undefined,
          childSeenOtherSpecialistsDetails: "",
          receivedSupportIntervention: undefined,
          receivedSupportInterventionDetails: "",
          supportReading: [],
          supportMaths: [],
          supportSpelling: [],
          supportWriting: [],
          currentLevelEnglish: "",
          currentLevelMaths: "",
          difficultyReadingSeverity: "",
          difficultySpellingSeverity: "",
          difficultyWritingSeverity: "",
          difficultyMathsSeverity: "",
          difficultySportsGamesSeverity: "",
          specialistHelpAtSchool: undefined,
          specialistHelpAtSchoolDetails: "",
          tuitionOutsideSchool: undefined,
          tuitionOutsideSchoolDetails: "",
          otherLanguagesSpoken: undefined,
          otherLanguagesSpokenDetails: "",
          literacyOverview: "",
          difficultyRecallingAlphabet: undefined,
          difficultyRecallingAlphabetDetails: "",
          // fields removed with Learning Difficulties section
          familyLearningDifficulties: "no",
          familyLearningDifficultiesDetails: "",
          visualHistory: undefined,
          prescriptionMade: undefined,
          wearPrescribedGlasses: undefined,
          usedColouredOverlays: undefined,
          visualHistoryDetails: "",
          lastSightTestDate: "",
          prescriptionDetails: "",
          usedColouredOverlaysDetails: "",
          screenTimeHours: "",
          readingHours: "",
          increasedScreenTime: "",
          visionQ1: "",
          visionQ2: "",
          visionQ3: "",
          visionQ4: "",
          visionQ5: "",
          visionQ6: "",
          visionQ7: "",
          visionQ8: "",
          visionQ9: "",
          visionQ10: "",
          visionQ11: "",
          visionQ12: "",
          visionQ13: "",
          visionQ14: "",
          visionQ15: "",
          visionQ16: "",
          referralConcerns: "",
          childViews: "",
          numeracyOverview: "",
          difficultyTellingTime: undefined,
          difficultyTellingTimeDetails: "",
          memoryAttentionConcentrationDifficulties: undefined,
          memoryAttentionConcentrationDetails: "",
          speechLanguageCommunicationDifficulties: undefined,
          speechLanguageCommunicationDifficultiesDetails: "",
          socialSkillsBehaviourPeerRelationshipsDifficulties: undefined,
          socialSkillsBehaviourPeerRelationshipsDifficultiesDetails: "",
          selfEsteemConfidenceDifficulties: undefined,
          selfEsteemConfidenceDifficultiesDetails: "",
          goodOrganisationalSkills: undefined,
          goodOrganisationalSkillsDetails: "",
          fineGrossMotorSkillsDifficulties: undefined,
          fineGrossMotorSkillsDifficultiesDetails: "",
          leftRightConfusion: undefined,
          leftRightConfusionDetails: "",
          childStrengths: "",
          parentSignature: "",
          parentPrintName: "",
          parentRelationshipToChild: "",
          parentSignatureDate: "",
        },
  });

  const saveFormStateToLocalStorage = useCallback(() => {
    const currentData = form.getValues();
    try {
      localStorage.setItem(
        `parentForm-${formId}-data`,
        JSON.stringify(currentData),
      );
    } catch (e) {
      console.error("Error saving form data to localStorage:", e);
    }
  }, [form, formId]);

  const watchedChildDOB = form.watch("childDateOfBirth");

  useEffect(() => {
    if (watchedChildDOB) {
      const dob = new Date(watchedChildDOB);
      if (!isNaN(dob.getTime())) {
        const today = new Date();
        let age = today.getFullYear() - dob.getFullYear();
        const m = today.getMonth() - dob.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
          age--;
        }
        form.setValue("childAge", age.toString());
      }
    } else {
      form.setValue("childAge", "");
    }
  }, [watchedChildDOB, form]);

  const watchedSignature = form.watch("parentSignature");

  useEffect(() => {
    if (watchedSignature) {
      const today = new Date().toISOString().split("T")[0];
      form.setValue("parentSignatureDate", today);
    } else {
      form.setValue("parentSignatureDate", "");
    }
  }, [watchedSignature, form]);

  // Effect to sync currentSection with openSection
  useEffect(() => {
    if (!isInitialRender.current) {
      // Set the accordion section that corresponds to current step
      setOpenSection(`section${currentSection}`);
      console.log(`Effect: Setting openSection to section${currentSection}`);
    }
  }, [currentSection]);

  // Effect to mark initial render complete and initialize form data from both sources correctly
  useEffect(() => {
    // Mark initial render as complete
    isInitialRender.current = false;

    // Handle preloaded data properly - merge localStorage with initial data
    const savedForm = localStorage.getItem(`parentForm-${formId}-data`);
    let savedData = null;

    try {
      if (savedForm) {
        savedData = JSON.parse(savedForm);
        console.log("Found saved form data in localStorage");
      }
    } catch (e) {
      console.error("Error parsing saved form data:", e);
    }

    // If we have saved data, populate the form with it
    if (savedData) {
      // Handle legacy field names
      if (savedData.familyLearningDetails) {
        savedData.familyLearningDifficultiesDetails = savedData.familyLearningDetails;
        delete savedData.familyLearningDetails;
      }

      // Reset form with saved data to ensure it's loaded correctly
      Object.keys(savedData).forEach((key) => {
        try {
          form.setValue(key as any, savedData[key]);
        } catch (e) {
          console.error(`Error setting form value for ${key}:`, e);
        }
      });
    } else if (initialData) {
      // If no saved data but we have initial data, use that
      try {
        // Map initialData (which is likely form responses) to form fields
        if (initialData.familyLearningDetails) {
          initialData.familyLearningDifficultiesDetails = initialData.familyLearningDetails;
          delete initialData.familyLearningDetails;
        }

        Object.keys(initialData).forEach((key) => {
          const value = initialData[key];
          if (value !== undefined && value !== null) {
            form.setValue(key as any, value);
          }
        });
      } catch (e) {
        console.error("Error setting initial form data:", e);
      }
    }

    // This will run on component unmount
    return () => {
      // We want to keep the localStorage data even when unmounting
      // to support navigation away and back to the form
    };
  }, [form, formId, initialData]);

  const goToNextSection = () => {
    if (currentSection < totalSections) {
      saveFormStateToLocalStorage();
      const nextSection = currentSection + 1;
      localStorage.setItem(
        `parentForm-${formId}-section`,
        nextSection.toString(),
      );
      setCurrentSection(nextSection);
      window.scrollTo(0, 0);
    }
  };

  const goToPreviousSection = () => {
    if (currentSection > 1) {
      saveFormStateToLocalStorage();
      const prevSection = currentSection - 1;
      localStorage.setItem(
        `parentForm-${formId}-section`,
        prevSection.toString(),
      );
      setCurrentSection(prevSection);
      window.scrollTo(0, 0);
    }
  };

  const handleSaveDraft = () => {
    saveFormStateToLocalStorage();
    const currentData = form.getValues();
    onSaveDraft(currentData);
    localStorage.setItem(
      `parentForm-${formId}-section`,
      currentSection.toString(),
    );
    toast({
      title: "Draft saved",
      description: "Your form has been saved as a draft.",
    });
  };


  const handleAccordionChange = useCallback(
    (value: string) => {
      setOpenSection(value);
      if (!value) return;
      const sectionMatch = value.match(/section(\d+)/);
      if (!sectionMatch) return;
      const newSection = parseInt(sectionMatch[1]);
      saveFormStateToLocalStorage();
      setCurrentSection(newSection);
      localStorage.setItem(
        `parentForm-${formId}-section`,
        newSection.toString(),
      );
      const el = document.getElementById(value);
      if (el) {
        requestAnimationFrame(() => {
          const top = el.getBoundingClientRect().top + window.scrollY - 80;
          window.scrollTo({ top, behavior: "smooth" });
        });
      }
    },
    [saveFormStateToLocalStorage, formId],
  );

  const handleSubmit = (values: ParentFormValues) => {
    // Always submit the complete raw form values to ensure all data is captured
    // The form-page component will handle the proper formatting and API submission
    console.log("Parent form submitting values:", values);
    console.log(
      "Number of fields being submitted:",
      Object.keys(values).length,
    );

    onSubmit(values);

    // Clear localStorage data after successful submission
    localStorage.removeItem(`parentForm-${formId}-data`);
    localStorage.removeItem(`parentForm-${formId}-section`);

    toast({
      title: "Form submitted",
      description: "The parent questionnaire has been submitted successfully.",
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <div className="space-y-8">
          <Card className="border-0">
            <CardHeader>
              <CardTitle>Pre-Assessment Questionnaire (Under 16 Years)</CardTitle>
              <CardDescription>
                Please complete each section to provide information about your
                child's needs. You can work through each section at your own
                pace and save your progress at any time.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
          {/* Section 1: Child's Information */}
          <Accordion
            type="single"
            collapsible
            value={openSection}
            onValueChange={handleAccordionChange}
          >
            <AccordionItem id="section1" value="section1" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    1
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Child Details</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <ChildDetailsSection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 2: Parent/Guardian Information */}
            <AccordionItem id="section2" value="section2" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    2
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">
                      Parent/Guardian Details
                    </h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <ParentGuardianSection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 3: Reasons for Referral */}
            <AccordionItem id="section3" value="section3" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    3
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Reasons for Referral</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <ReasonsForReferralSection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 4: Vision and Visual Difficulties */}
            <AccordionItem id="section4" value="section4" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    4
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Vision and Visual Difficulties</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <VisionDifficultiesSection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 5: Language and Linguistic History */}
            <AccordionItem id="section5" value="section5" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    5
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Language and Linguistic History</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <LanguageHistorySection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 6: Familial History */}
            <AccordionItem id="section6" value="section6" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    6
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Familial History</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <FamilialHistorySection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 7: Health and Developmental History */}
            <AccordionItem id="section7" value="section7" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    7
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Health and Developmental History</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <HealthAndDevelopmentalHistorySection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 8: Current Situation */}
            <AccordionItem id="section8" value="section8" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    8
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Current Situation</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CurrentSituationSection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 9: Literacy */}
            <AccordionItem id="section9" value="section9" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    9
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Literacy</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <LiteracySection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 10: Numeracy */}
            <AccordionItem id="section10" value="section10" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    10
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Numeracy</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <NumeracySection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 11: Memory, Attention and Concentration */}
            <AccordionItem id="section11" value="section11" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    11
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Memory, Attention and Concentration</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <MemoryAttentionSection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 12: Speech, Oral Language, Communication and Social Skills */}
            <AccordionItem id="section12" value="section12" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    12
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Speech, Oral Language, Communication and Social Skills</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SpeechLanguageCommunicationSection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 13: Organisational Skills */}
            <AccordionItem id="section13" value="section13" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    13
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Organisational Skills</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <OrganisationalSkillsSection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 14: Fine and Gross Motor Skills */}
            <AccordionItem id="section14" value="section14" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    14
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Fine and Gross Motor Skills</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <MotorSkillsSection />
              </AccordionContent>
            </AccordionItem>

            {/* Section 15: Strengths */}
            <AccordionItem id="section15" value="section15" className="border-0">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                    15
                  </div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Strengths</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <StrengthsSection />
              </AccordionContent>
            </AccordionItem>

          </Accordion>
        </div>

        <div className="mt-8 flex flex-col md:flex-row md:space-x-6 space-y-6 md:space-y-0">
          <div className="md:flex-1">
            <FormField
              control={form.control}
              name="parentSignature"
              render={({ field }) => (
                <FormItem className="h-full">
                  <FormLabel>Signed</FormLabel>
                  <FormControl>
                    <SignatureInput {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex flex-col space-y-6 md:flex-1">
            <FormField
              control={form.control}
              name="parentPrintName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Print Name</FormLabel>
                  <FormControl>
                    <StableInput {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parentRelationshipToChild"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Relationship to child</FormLabel>
                  <FormControl>
                    <StableInput {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parentSignatureDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Dated</FormLabel>
                  <FormControl>
                    <StableInput type="date" readOnly {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <div className="flex space-x-2">
            <Button
              type="button"
              variant="secondary"
              onClick={handleSaveDraft}
            >
              <Save className="mr-2 h-4 w-4" />
              Save Draft
            </Button>
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? "Submitting..." : "Submit Form"}
            </Button>
          </div>
        </div>
            </CardContent>
          </Card>
        </div>
      </form>
    </Form>
  );
}
