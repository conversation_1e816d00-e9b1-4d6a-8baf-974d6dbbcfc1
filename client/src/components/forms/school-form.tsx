import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { StableInput } from "@/components/forms/stable-input";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useToast } from "@/hooks/use-toast";
import { useState, useEffect, useRef, useCallback } from "react";
import { Check, ChevronLeft, ChevronRight, Edit, Save } from "lucide-react";
import { SchoolFormSection } from "./school-form-sections";
import { SignatureInput } from "./signature-input";

// Create schema based on the School Pre-Assessment Form
const schoolFormSchema = z.object({
  // Child details
  childFullName: z.string().optional(),
  school: z.string().optional(),
  yearGroup: z.string().optional(),
  
  // Person completing form
  personCompletingName: z.string().optional(),
  personCompletingTitle: z.string().optional(),
  personCompletingRole: z.string().optional(),
  dateCompleted: z.string().optional(),
  
  // SENCO contact
  sencoContactDetails: z.string().optional(),
  
  // Parent/Guardian details
  parentFullName: z.string().optional(),
  parentTitle: z.string().optional(),
  parentRelationship: z.string().optional(),
  parentAddress: z.string().optional(),
  parentPostcode: z.string().optional(),
  parentMobile: z.string().optional(),
  parentEmail: z.string().optional(),
  
  // School Performance - Key Stage 1
  ks1English: z.string().optional(),
  ks1Maths: z.string().optional(),
  ks1Science: z.string().optional(),
  
  // School Performance - Key Stage 2 
  ks2Reading: z.string().optional(),
  ks2Writing: z.string().optional(),
  ks2GPS: z.string().optional(),
  ks2Maths: z.string().optional(),
  ks2Science: z.string().optional(),
  
  // School Performance - Key Stage 3
  ks3English: z.string().optional(),
  ks3Maths: z.string().optional(),
  ks3Science: z.string().optional(),
  
  // Phonics test (primary schools)
  phonicsTestPassed: z.enum(["Y", "N"]).optional(),
  phonicsTestEndOfYearOne: z.enum(["Y", "N"]).optional(),
  phonicsTestYear: z.string().optional(),
  phonicsTestScore: z.string().optional(),
  
  // Academic performance ratings
  speakingListeningRating: z.enum(["below_average", "average", "above_average"]).optional(),
  readingAccuracyRating: z.enum(["below_average", "average", "above_average"]).optional(),
  readingComprehensionRating: z.enum(["below_average", "average", "above_average"]).optional(),
  writingRating: z.enum(["below_average", "average", "above_average"]).optional(),
  spellingRating: z.enum(["below_average", "average", "above_average"]).optional(),
  mathsRating: z.enum(["below_average", "average", "above_average"]).optional(),
  scienceRating: z.enum(["below_average", "average", "above_average"]).optional(),
  
  // Other subject ratings
  humanitiesRating: z.enum(["below_average", "average", "above_average"]).optional(),
  peRating: z.enum(["below_average", "average", "above_average"]).optional(),
  artRating: z.enum(["below_average", "average", "above_average"]).optional(),
  dtRating: z.enum(["below_average", "average", "above_average"]).optional(),
  ictRating: z.enum(["below_average", "average", "above_average"]).optional(),
  otherRating: z.enum(["below_average", "average", "above_average"]).optional(),
  
  // Recent assessments
  recentAssessmentsDetails: z.string().optional(),
  
  // Learning difficulties
  writtenWorkDifficulty: z.enum(["Y", "N"]).optional(),
  losingThingsDifficulty: z.enum(["Y", "N"]).optional(),
  startingWorkDifficulty: z.enum(["Y", "N"]).optional(),
  selfOrganizationDifficulty: z.enum(["Y", "N"]).optional(),
  copyingDifficulty: z.enum(["Y", "N"]).optional(),
  fineMotorDifficulty: z.enum(["Y", "N"]).optional(),
  rememberingInstructionsDifficulty: z.enum(["Y", "N"]).optional(),
  grossMotorDifficulty: z.enum(["Y", "N"]).optional(),
  verbalWrittenDiscrepancy: z.enum(["Y", "N"]).optional(),

  // Literacy and Numeracy details
  literacyDetails: z.string().optional(),
  numeracyDetails: z.string().optional(),

  // Memory, attention and concentration
  memoryAttentionDifficulty: z.enum(["Y", "N"]).optional(),
  memoryAttentionDetails: z.string().optional(),
  
  // Attitude to work
  attitudeToWork: z.array(z.string()).optional(),
  
  // Peer relationships
  peerRelationships: z.array(z.string()).optional(),
  
  // SEN monitoring
  senMonitored: z.enum(["Y", "N"]).optional(),
  iepOrPlp: z.enum(["Y", "N"]).optional(),
  currentSupportDetails: z.string().optional(),
  supportProvider: z.string().optional(),
  supportType: z.string().optional(),
  sessionLength: z.string().optional(),
  sessionFrequency: z.string().optional(),

  // External agency involvement
  externalAgencyInvolved: z.enum(["Y", "N"]).optional(),
  externalAgencyDetails: z.string().optional(),
  
  // Observed behaviors
  observedBehaviors: z.array(z.string()).optional(),

  // Additional comments
  additionalComments: z.string().optional(),

  // Speech, language and social skills
  speechLanguageCommunicationDifficulty: z.enum(["Y", "N"]).optional(),
  speechLanguageCommunicationDetails: z.string().optional(),
  socialSkillsBehaviourPeerRelationshipsDifficulty: z.enum(["Y", "N"]).optional(),
  socialSkillsBehaviourPeerRelationshipsDetails: z.string().optional(),
  selfEsteemConfidenceDifficulty: z.enum(["Y", "N"]).optional(),
  selfEsteemConfidenceDetails: z.string().optional(),

  // Organisational skills
  goodOrganisationalSkills: z.enum(["Y", "N"]).optional(),
  goodOrganisationalSkillsDetails: z.string().optional(),

  // Motor skills
  fineGrossMotorSkillsDifficulty: z.enum(["Y", "N"]).optional(),
  fineGrossMotorSkillsDetails: z.string().optional(),

  // Strengths and other info
  strengths: z.string().optional(),
  otherInformation: z.string().optional(),

  // Signature section
  schoolSignature: z.string().optional(),
  schoolPrintName: z.string().optional(),
  schoolRelationshipToChild: z.string().optional(),
  schoolSignatureDate: z.string().optional(),
});

type SchoolFormValues = z.infer<typeof schoolFormSchema>;

interface SchoolFormProps {
  formId: number;
  initialData?: any;
  formData?: any;
  onSubmit: (data: any) => void;
  onSaveDraft: (data: SchoolFormValues) => void;
}

export function SchoolForm({ formId, initialData, formData, onSubmit, onSaveDraft }: SchoolFormProps) {
  const { toast } = useToast();
  
  // Track if we're in draft saving mode
  const [justSavedDraft, setJustSavedDraft] = useState(false);
  const isInitialRender = useRef(true);
  
  // Clear any stale data on component load if we have initialData
  // This prevents old data from persisting when starting a new form
  useEffect(() => {
    // Don't clear anything if we just saved a draft
    if (justSavedDraft) {
      console.log("Just saved draft, not clearing localStorage");
      return;
    }
    
    // Check if we have data in localStorage
    const localData = localStorage.getItem(`schoolForm-${formId}-data`);
    
    // Check if this is a brand new form vs. resuming a form
    const hasInitialData = initialData && Object.keys(initialData).some(
      key => initialData[key] !== null && initialData[key] !== undefined
    );
    
    const hasLocalData = !!localData;
    
    if (hasLocalData) {
      console.log(`Found saved draft data in localStorage for form ${formId}`);
    } else if (!hasInitialData) {
      // Only clear localStorage if this is a brand new form (not resuming)
      localStorage.removeItem(`schoolForm-${formId}-data`);
      localStorage.removeItem(`schoolForm-${formId}-section`);
      console.log(`Cleared localStorage data for new form ${formId}`);
    } else {
      console.log(`Resuming form ${formId} with existing data from DB, not clearing localStorage`);
    }
  }, [formId, initialData, justSavedDraft]);
  
  // Store current section in a state variable, starting with default section 1
  // (the correct section will be loaded via useEffect)
  const [currentSection, setCurrentSection] = useState(1);
  const [openSection, setOpenSection] = useState<string | undefined>(undefined);
  const totalSections = 14;
  
  // Try to get saved form data from localStorage first, then use initialData, then default
  const savedFormData = (() => {
    try {
      const saved = localStorage.getItem(`schoolForm-${formId}-data`);
      return saved ? JSON.parse(saved) : null;
    } catch (e) {
      return null;
    }
  })();
  
  const form = useForm<SchoolFormValues>({
    resolver: zodResolver(schoolFormSchema),
    defaultValues: savedFormData || initialData || {
      // Key Stage results
      ks1English: "",
      ks1Maths: "",
      ks1Science: "",
      ks2Reading: "",
      ks2Writing: "",
      ks2GPS: "",
      ks2Maths: "",
      ks2Science: "",
      ks3English: "",
      ks3Maths: "",
      ks3Science: "",

      // Phonics test defaults
      phonicsTestPassed: "",
      phonicsTestEndOfYearOne: "",
      phonicsTestYear: "",
      
      // Other form fields
      speakingListeningRating: "",
      readingAccuracyRating: "",
      readingComprehensionRating: "",
      writingRating: "",
      spellingRating: "",
      mathsRating: "",
      scienceRating: "",
      humanitiesRating: "",
      peRating: "",
      artRating: "",
      dtRating: "",
      ictRating: "",
      otherRating: "",
      writtenWorkDifficulty: "",
      losingThingsDifficulty: "",
      startingWorkDifficulty: "",
      selfOrganizationDifficulty: "",
      copyingDifficulty: "",
      fineMotorDifficulty: "",
      rememberingInstructionsDifficulty: "",
      grossMotorDifficulty: "",
      verbalWrittenDiscrepancy: "",
      literacyDetails: "",
      numeracyDetails: "",
      memoryAttentionDifficulty: "",
      memoryAttentionDetails: "",
      senMonitored: "",
      iepOrPlp: "",
      attitudeToWork: [],
      peerRelationships: [],
      observedBehaviors: [],
      currentSupportDetails: "",
      supportProvider: "",
      supportType: "",
      sessionLength: "",
      sessionFrequency: "",
      externalAgencyInvolved: "",
      externalAgencyDetails: "",
      additionalComments: "",

      speechLanguageCommunicationDifficulty: "",
      speechLanguageCommunicationDetails: "",
      socialSkillsBehaviourPeerRelationshipsDifficulty: "",
      socialSkillsBehaviourPeerRelationshipsDetails: "",
      selfEsteemConfidenceDifficulty: "",
      selfEsteemConfidenceDetails: "",
      goodOrganisationalSkills: "",
      goodOrganisationalSkillsDetails: "",
      fineGrossMotorSkillsDifficulty: "",
      fineGrossMotorSkillsDetails: "",
      strengths: "",
      otherInformation: "",

      schoolSignature: "",
      schoolPrintName: "",
      schoolRelationshipToChild: "",
      schoolSignatureDate: ""
    },
  });

  const watchedSignature = form.watch("schoolSignature");

  useEffect(() => {
    if (watchedSignature) {
      const today = new Date().toISOString().split("T")[0];
      form.setValue("schoolSignatureDate", today);
    } else {
      form.setValue("schoolSignatureDate", "");
    }
  }, [watchedSignature, form]);
  
  // Initialize section from localStorage - prevents jumps between sections
  useEffect(() => {
    try {
      // Initialize section reference - if we find none, this will stay as 1
      let targetSectionNumber = 1;
      
      // First check if there's any persistent backup data which has highest priority
      const persistentBackupData = localStorage.getItem(`schoolForm-${formId}-data-persistent`);
      const persistentSection = localStorage.getItem(`schoolForm-${formId}-section-persistent`);
      
      // Next check if there's any regular localStorage data (second priority)
      const savedFormData = localStorage.getItem(`schoolForm-${formId}-data`);
      const savedSection = localStorage.getItem(`schoolForm-${formId}-section`);
      
      // First handle data restoration - start with persistent data
      if (persistentBackupData) {
        console.log(`Found persistent backup data for form ${formId}, restoring it`);
        
        try {
          // Parse and apply data from persistent backup
          const parsedData = JSON.parse(persistentBackupData);
          
          // Apply data to form quietly - no validation errors during initialization
          Object.keys(parsedData).forEach(key => {
            try {
              if (parsedData[key] !== undefined && parsedData[key] !== null) {
                // Type assertion to handle the dynamic keys
                form.setValue(key as any, parsedData[key], { shouldValidate: false });
              }
            } catch (err) {
              // Silently skip field errors - we don't want to break initialization
              console.log(`Field ${key} could not be set - skipping`);
            }
          });
          
          // Also copy to regular localStorage to keep them in sync
          localStorage.setItem(`schoolForm-${formId}-data`, persistentBackupData);
        } catch (parseError) {
          console.error(`Error parsing persistent data:`, parseError);
        }
        
        // Check for persistent section information
        if (persistentSection) {
          const sectionNum = parseInt(persistentSection);
          if (sectionNum >= 1 && sectionNum <= totalSections) {
            console.log(`Found persistent section ${sectionNum} from persistent backup`);
            targetSectionNumber = sectionNum;
            // Also copy to regular localStorage
            localStorage.setItem(`schoolForm-${formId}-section`, persistentSection);
          }
        }
      } 
      // If no persistent data but regular data exists
      else if (savedFormData) { 
        try {
          // Parse and apply data from regular localStorage
          const parsedData = JSON.parse(savedFormData);
          
          // Apply data to form quietly
          Object.keys(parsedData).forEach(key => {
            try {
              if (parsedData[key] !== undefined && parsedData[key] !== null) {
                // Type assertion to handle the dynamic keys
                form.setValue(key as any, parsedData[key], { shouldValidate: false });
              }
            } catch (err) {
              console.log(`Field ${key} could not be set - skipping`);
            }
          });
        } catch (parseError) {
          console.error(`Error parsing saved data:`, parseError);
        }
        
        // Check for saved section
        if (savedSection) {
          const sectionNum = parseInt(savedSection);
          if (sectionNum >= 1 && sectionNum <= totalSections) {
            console.log(`Found saved section ${sectionNum} from localStorage`);
            targetSectionNumber = sectionNum;
          }
        }
      } else {
        // If no data found at all, make sure section data is cleared to prevent jumps
        localStorage.removeItem(`schoolForm-${formId}-section`);
        console.log(`No saved form data found, will start at section 1`);
      }
      
      // If this is the INITIAL load of the component (not during re-renders or section changes):
      if (isInitialRender.current) {
        isInitialRender.current = false;
        
        // Set the internal section number but don't open any sections
        if (targetSectionNumber !== currentSection) {
          console.log(`Initial load: setting internal section tracking to ${targetSectionNumber}`);
          setCurrentSection(targetSectionNumber);
        }
        
        // Always ensure all sections are closed on initial load
        // This forces the user to explicitly open a section
        setOpenSection(undefined);
        console.log(`Initial load: all accordion sections closed`);
      }
    } catch (error) {
      console.error("Error in form initialization:", error);
    }
  }, [formId, totalSections, form, currentSection]);
  
  // Only clear localStorage on component unmount for completed forms
  useEffect(() => {
    return () => {
      // Check if form is marked as completed
      const formStatus = localStorage.getItem(`schoolForm-${formId}-status`);
      if (formStatus === 'completed') {
        console.log(`Form ${formId} is marked as completed, clearing localStorage data on unmount`);
        localStorage.removeItem(`schoolForm-${formId}-data`);
        localStorage.removeItem(`schoolForm-${formId}-section`);
        localStorage.removeItem(`schoolForm-${formId}-status`);
      } else {
        console.log(`Form ${formId} is not completed, preserving localStorage data on unmount`);
      }
    };
  }, [formId]);
  
  const goToNextSection = () => {
    // Using minimal timeout to ensure clean state updates
    if (currentSection < totalSections) {
      try {
        // First silently save current data to localStorage
        saveDraftToLocalStorage();
        
        // Calculate the next section
        const nextSectionNum = currentSection + 1;
        console.log(`Moving from section ${currentSection} to section ${nextSectionNum}`);
        
        // Update localStorage with new section
        localStorage.setItem(`schoolForm-${formId}-section`, nextSectionNum.toString());
        
        // Use a very minimal timeout to ensure React has time to process state changes
        setTimeout(() => {
          setCurrentSection(nextSectionNum);
          console.log(`⭐ Setting current section to ${nextSectionNum}`);
          
          // Scroll to top for better user experience
          window.scrollTo(0, 0);
        }, 5); // Very short timeout - just enough to break the call stack
      } catch (error) {
        console.error("Error in goToNextSection:", error);
      }
    }
  };
  
  const goToPreviousSection = () => {
    // Using minimal timeout to ensure clean state updates
    if (currentSection > 1) {
      try {
        // First silently save current data to localStorage
        saveDraftToLocalStorage();
        
        // Calculate the previous section
        const prevSectionNum = currentSection - 1;
        console.log(`Moving from section ${currentSection} to section ${prevSectionNum}`);
        
        // Update localStorage with new section 
        localStorage.setItem(`schoolForm-${formId}-section`, prevSectionNum.toString());
        
        // Use a very minimal timeout to ensure React has time to process state changes
        setTimeout(() => {
          setCurrentSection(prevSectionNum);
          console.log(`⭐ Setting current section to ${prevSectionNum}`);
          
          // Scroll to top for better user experience
          window.scrollTo(0, 0);
        }, 5); // Very short timeout - just enough to break the call stack
      } catch (error) {
        console.error("Error in goToPreviousSection:", error);
      }
    }
  };
  
  // Save draft without triggering a toast notification
  const saveDraftToLocalStorage = () => {
    try {
      // Only save to localStorage
      const currentData = form.getValues();
      localStorage.setItem(`schoolForm-${formId}-data`, JSON.stringify(currentData));
      
      // Directly set section in localStorage
      localStorage.setItem(`schoolForm-${formId}-section`, String(currentSection));
      console.log(`Silent save to localStorage: section ${currentSection}`);
    } catch (error) {
      console.error("Error saving to localStorage:", error);
    }
  };
  
  // Explicit save draft triggered by user clicking Save Draft button
  const handleSaveDraft = async () => {
    try {
      // Capture current section before any operations
      const currentSectionBeforeSave = currentSection;
      // Remember the current open section
      const openSectionBeforeSave = openSection;
      console.log(`Saving draft from section ${currentSectionBeforeSave}`);
      
      // Set flag to prevent clearing localStorage on reload/rerender
      setJustSavedDraft(true);
      
      // Get current form data
      const currentData = form.getValues();
      
      // First save to localStorage (both regular and persistent)
      localStorage.setItem(`schoolForm-${formId}-data`, JSON.stringify(currentData));
      localStorage.setItem(`schoolForm-${formId}-data-persistent`, JSON.stringify(currentData));
      
      // Explicitly save the current section to both regular and persistent storage
      localStorage.setItem(`schoolForm-${formId}-section`, currentSectionBeforeSave.toString());
      localStorage.setItem(`schoolForm-${formId}-section-persistent`, currentSectionBeforeSave.toString());
      
      // Also save status indicators
      localStorage.setItem(`schoolForm-${formId}-status`, 'draft');
      localStorage.setItem(`schoolForm-${formId}-status-persistent`, 'draft');
      
      // Call the handler passed from parent component to save to server
      onSaveDraft(currentData);
      
      // Notify user
      toast({
        title: "Draft saved",
        description: "Your form has been saved as a draft.",
      });
      
      // Explicitly set the section back to what it was before saving
      // This prevents the form from jumping to another section
      setTimeout(() => {
        if (currentSection !== currentSectionBeforeSave) {
          console.log(`Correcting section from ${currentSection} back to ${currentSectionBeforeSave} after save`);
          setCurrentSection(currentSectionBeforeSave);
        }
        // Preserve the open/closed state of sections
        setOpenSection(openSectionBeforeSave);
      }, 50);
      
      // Reset the flag after a delay to allow for component rerender
      setTimeout(() => {
        setJustSavedDraft(false);
      }, 1000);
    } catch (error) {
      console.error("Error in handleSaveDraft:", error);
      toast({
        title: "Error saving draft",
        description: "There was a problem saving your draft. Please try again.",
        variant: "destructive"
      });
    }
  };


  const handleAccordionChange = useCallback(
    (value: string | undefined) => {
      setOpenSection(value);
      if (!value) return;
      const sectionMatch = value.match(/section(\d+)/);
      if (!sectionMatch) return;
      const newSection = parseInt(sectionMatch[1]);
      saveDraftToLocalStorage();
      setCurrentSection(newSection);
      localStorage.setItem(`schoolForm-${formId}-section`, newSection.toString());
      window.scrollTo(0, 0);
    },
    [saveDraftToLocalStorage, formId]
  );
  
  const handleSubmit = (values: SchoolFormValues) => {
    try {
      console.log(`Submitting form from section: ${currentSection}`);
      
      // Format the form values into the expected response format
      const formattedResponses = formData?.questions?.map((question: any) => {
        const questionKey = question.questionKey;
        const value = values[questionKey as keyof SchoolFormValues] || "";

        return {
          questionId: question.id,
          questionKey: questionKey,
          value: typeof value === 'string' ? value : JSON.stringify(value),
        };
      }) || [];
      
      // Filter out any undefined values
      const validResponses = formattedResponses.filter((r: any) => r.questionId && r.value !== undefined);
      
      // Submit the formatted responses
      onSubmit({
        responses: validResponses,
        status: "completed"
      });
      
      // Set the form status to completed
      localStorage.setItem(`schoolForm-${formId}-status`, 'completed');
      
      // Only clear localStorage data after successful submission
      console.log("Clearing localStorage data after successful submission");
      localStorage.removeItem(`schoolForm-${formId}-data`);
      localStorage.removeItem(`schoolForm-${formId}-section`);
      
      // Add extra debug info
      setTimeout(() => {
        const storedData = localStorage.getItem(`schoolForm-${formId}-data`);
        const storedSection = localStorage.getItem(`schoolForm-${formId}-section`);
        const storedStatus = localStorage.getItem(`schoolForm-${formId}-status`);
        console.log(`After submission - Data exists: ${!!storedData}, Section exists: ${!!storedSection}, Status: ${storedStatus}`);
      }, 50);
      
      toast({
        title: "Form submitted",
        description: "The school questionnaire has been submitted successfully.",
      });
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      toast({
        title: "Error submitting form",
        description: "There was a problem submitting your form. Please try again.",
        variant: "destructive"
      });
    }
  };
  
  const attitudeOptions = [
    { value: "keen", label: "Keen" },
    { value: "distracts_others", label: "Distracts others" },
    { value: "independent", label: "Independent" },
    { value: "competent", label: "Competent" },
    { value: "works_well_with_help", label: "Works well with help" },
    { value: "slow", label: "Slow" },
    { value: "distractible", label: "Distractible" },
    { value: "lacks_interest", label: "Lacks interest" }
  ];
  
  const peerRelationshipOptions = [
    { value: "popular", label: "Popular" },
    { value: "withdrawn", label: "Withdrawn" },
    { value: "regular_friendship_problems", label: "Has regular friendship problems" },
    { value: "accepted", label: "Accepted" },
    { value: "friendly", label: "Friendly" },
    { value: "avoids_others", label: "Avoids others" },
    { value: "dominant", label: "Dominant" },
    { value: "one_special_friend", label: "Has one special friend" }
  ];
  
  const observedBehaviorOptions = [
    { value: "good_bad_days", label: "Has obvious good and bad days for no apparent reason" },
    { value: "sequential_difficulties", label: "Has difficulty remembering anything in a sequential order, e.g. times tables, days of the week, the alphabet" },
    { value: "direction_difficulties", label: "Has a poor sense of direction and confuses left and right" },
    { value: "slow_written_processing", label: "Speed of processing written language is slow" },
    { value: "word_finding_difficulties", label: "Forgetful of words/can't find the right word" },
    { value: "slow_spoken_processing", label: "Speed of processing spoken language is slow" },
    { value: "poor_concentration", label: "Poor concentration" },
    { value: "memory_problems", label: "Exhibits memory problems e.g. difficulty remembering a list of instructions" },
    { value: "poor_written_work", label: "Has a poor standard of written work compared with oral ability" },
    { value: "messy_work", label: "Produces messy work with many crossings out" },
    { value: "poor_handwriting", label: "Has poor handwriting, possibly with reversals and badly formed letters" },
    { value: "poor_pencil_grip", label: "Has poor pencil grip" },
    { value: "poorly_set_work", label: "Produces badly set out written work, doesn't stay close to the margin" },
    { value: "letter_confusion", label: "Is persistently confused by letters which look similar e.g. b/d" },
    { value: "phonetic_spelling", label: "Produces phonetic and bizarre spelling: not in line with age/ability" }
  ];
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <Card className="border-0">
          <CardHeader>
            <CardTitle>
              Pre-Assessment Questionnaire (Under 16 Years) - School
            </CardTitle>
            <CardDescription>
              Please complete each section to provide information about the pupil. You can work
              through each section at your own pace and save your progress at any time.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-6 space-y-2">
          <ul className="list-disc pl-5 text-sm text-gray-500 space-y-1">
            <li>An assessment is being carried out to clarify this child’s learning needs and their parent has consented to this questionnaire being sent to you to be completed.</li>
            <li>It would be most appreciated if you, or another member of staff who knows the young person well, could complete as much information as possible on the form below.</li>
            <li>Information from their school is very useful and helps to provide a wider context in which to place their needs.</li>
            <li>This assessment cannot be used as part of an application for exam access arrangements, under JCQ regulations, without prior agreement.</li>
            <li>Your support is very much appreciated.</li>
          </ul>
          <p className="text-sm text-gray-500">All information will be treated confidentially.</p>
        </div>
        
        <div className="space-y-4">
          {/* Section 1: Pupil Details */}
          <Accordion
            type="single"
            collapsible
            defaultValue={undefined}
            value={openSection}
            onValueChange={handleAccordionChange}
          >
            <AccordionItem value="section1" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">1</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Pupil Details</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={1} />
              </AccordionContent>
            </AccordionItem>
            
            {/* Section 2: Parent/Guardian Details */}
            <AccordionItem value="section2" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">2</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Parent/Guardian Details</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={2} />
              </AccordionContent>
            </AccordionItem>
            
            {/* Section 3: Key Stage Results */}
            <AccordionItem value="section3" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">3</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Key Stage Results</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={3} />
              </AccordionContent>
            </AccordionItem>
            
            {/* Section 4: Difficulties with Written Work */}
            <AccordionItem value="section4" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">4</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Difficulties with Written Work</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={4} />
              </AccordionContent>
            </AccordionItem>
            
            {/* Section 5: Special Educational Needs (SEN) Status and Support */}
            <AccordionItem value="section5" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">5</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Special Educational Needs (SEN) Status and Support</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={5} />
              </AccordionContent>
            </AccordionItem>
            
            {/* Section 6: External Agency Involvement */}
            <AccordionItem value="section6" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">6</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">External Agency Involvement</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={6} />
              </AccordionContent>
            </AccordionItem>

            {/* Section 7: Literacy */}
            <AccordionItem value="section7" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">7</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Literacy</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={7} />
              </AccordionContent>
            </AccordionItem>

            {/* Section 8: Numeracy */}
            <AccordionItem value="section8" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">8</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Numeracy</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={8} />
              </AccordionContent>
            </AccordionItem>

            {/* Section 9: Memory, Attention and Concentration */}
            <AccordionItem value="section9" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">9</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Memory, Attention and Concentration</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={9} />
              </AccordionContent>
            </AccordionItem>

            {/* Section 10: Speech, Oral Language, Communication and Social Skills */}
            <AccordionItem value="section10" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">10</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Speech, Oral Language, Communication and Social Skills</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={10} />
              </AccordionContent>
            </AccordionItem>

            {/* Section 11: Organisational Skills */}
            <AccordionItem value="section11" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">11</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Organisational Skills</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={11} />
              </AccordionContent>
            </AccordionItem>

            {/* Section 12: Fine and Gross Motor Skills */}
            <AccordionItem value="section12" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">12</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Fine and Gross Motor Skills</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={12} />
              </AccordionContent>
            </AccordionItem>

            {/* Section 13: Strengths */}
            <AccordionItem value="section13" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">13</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Strengths</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={13} />
              </AccordionContent>
            </AccordionItem>

            {/* Section 14: Any Other Information */}
            <AccordionItem value="section14" className="border-none">
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-medium">14</div>
                  <div className="text-left">
                    <h3 className="text-base font-medium">Any Other Information</h3>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <SchoolFormSection section={14} />
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        <div className="mt-8 flex flex-col md:flex-row md:space-x-6 space-y-6 md:space-y-0">
          <div className="md:flex-1">
            <FormField
              control={form.control}
              name="schoolSignature"
              render={({ field }) => (
                <FormItem className="h-full">
                  <FormLabel>Signed</FormLabel>
                  <FormControl>
                    <SignatureInput {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex flex-col space-y-6 md:flex-1">
            <FormField
              control={form.control}
              name="schoolPrintName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Print Name</FormLabel>
                  <FormControl>
                    <StableInput {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="schoolRelationshipToChild"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Position in school</FormLabel>
                  <FormControl>
                    <StableInput {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="schoolSignatureDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Dated</FormLabel>
                  <FormControl>
                    <StableInput type="date" readOnly {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <div className="flex space-x-2">
            <Button
              type="button"
              variant="secondary"
              onClick={handleSaveDraft}
            >
              <Save className="mr-2 h-4 w-4" />
              Save Draft
            </Button>
            <Button
              type="submit"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? "Submitting..." : "Submit Form"}
            </Button>
          </div>
        </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  );
}
