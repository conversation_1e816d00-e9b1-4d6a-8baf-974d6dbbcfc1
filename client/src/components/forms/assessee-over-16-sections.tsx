import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { StableInput } from "@/components/forms/stable-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { COUNTRIES } from "@/constants/countries";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { StableTextarea } from "@/components/forms/stable-textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import type { AssesseeFormValues } from "./assessee-over-16-form";

export function PersonalInformationSection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <StableInput
                  placeholder="Enter your full name"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="preferredName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Preferred Name</FormLabel>
              <FormControl>
                <StableInput
                  placeholder="Enter your preferred name"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

      </div>
      
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="dateOfBirth"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Date of Birth</FormLabel>
              <FormControl>
                <StableInput
                  type="date"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="age"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Age</FormLabel>
              <FormControl>
                <StableInput
                  placeholder="Age will be calculated"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                  readOnly
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="countryOfBirth"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Country of Birth</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {COUNTRIES.map((c) => (
                    <SelectItem key={c} value={c}>
                      {c}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="dateMovedToUK"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Date Moved to UK (if applicable)</FormLabel>
              <FormControl>
                <StableInput
                  type="date"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormDescription>
                To be assessed you will need to have continuously lived in an
                English-speaking country and to have been regularly speaking
                English for a minimum of 7 years.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="schoolYear"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Current School Year / Year of Study</FormLabel>
              <FormControl>
                <StableInput
                  placeholder="e.g. Year 12 / 1st Year University"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="educationalEstablishment"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Current Educational Establishment</FormLabel>
              <FormControl>
                <StableInput
                  placeholder="Enter your school/university name"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="gender"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Gender</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-1"
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="male" />
                    </FormControl>
                    <FormLabel className="font-normal">Male</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="female" />
                    </FormControl>
                    <FormLabel className="font-normal">Female</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="neutral" />
                    </FormControl>
                    <FormLabel className="font-normal">Gender Neutral</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="prefer_not_to_say" />
                    </FormControl>
                    <FormLabel className="font-normal">Prefer not to say</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="preferredPronoun"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Preferred Pronoun</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-1"
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="he" />
                    </FormControl>
                    <FormLabel className="font-normal">He/Him</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="she" />
                    </FormControl>
                    <FormLabel className="font-normal">She/Her</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="they" />
                    </FormControl>
                    <FormLabel className="font-normal">They/Them</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="other" />
                    </FormControl>
                    <FormLabel className="font-normal">Other (please state)</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
          {form.watch("preferredPronoun") === "other" && (
            <FormField
              control={form.control}
              name="preferredPronounOther"
              render={({ field }) => (
                <FormItem className="md:col-start-2">
                  <FormLabel>Please specify</FormLabel>
                  <FormControl>
                    <StableInput
                      placeholder="Enter preferred pronoun"
                      value={field.value || ''}
                      onChange={field.onChange}
                      onBlur={field.onBlur}
                      name={field.name}
                      ref={field.ref}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
      </div>
      
      <div className="border-t pt-4 mt-6">
        <h3 className="text-lg font-medium mb-4">Contact Information</h3>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="contactAddress"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Address (including postcode)</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Enter your full address"
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="contactPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone (Mobile)</FormLabel>
                      <FormControl>
                        <StableInput
                          placeholder="Enter your mobile number"
                          value={field.value || ''}
                          onChange={field.onChange}
                          onBlur={field.onBlur}
                          name={field.name}
                          ref={field.ref}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contactPhoneWork"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone (Work)</FormLabel>
                      <FormControl>
                        <StableInput
                          placeholder="Enter your work phone"
                          value={field.value || ''}
                          onChange={field.onChange}
                          onBlur={field.onBlur}
                          name={field.name}
                          ref={field.ref}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contactEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <StableInput
                          placeholder="Enter your email address"
                          type="email"
                          value={field.value || ''}
                          onChange={field.onChange}
                          onBlur={field.onBlur}
                          name={field.name}
                          ref={field.ref}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
        </div>
      </div>

      <FormField
        control={form.control}
        name="hearAbout"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>How did you hear about NeuroElevate?</FormLabel>
            <FormControl>
              <div className="flex flex-col space-y-2">
                {[
                  { label: 'Facebook', value: 'facebook' },
                  { label: 'Google Search', value: 'google_search' },
                  { label: 'Advert', value: 'advert' },
                  { label: 'Recommendation', value: 'recommendation' },
                  { label: 'Other', value: 'other' }
                ].map(opt => (
                  <label key={opt.value} className="flex items-center space-x-2">
                    <Checkbox
                      checked={Array.isArray(field.value) ? field.value.includes(opt.value) : false}
                      onCheckedChange={(checked) => {
                        let current: string[] = Array.isArray(field.value) ? field.value : [];
                        if (checked) {
                          current = [...current, opt.value];
                        } else {
                          current = current.filter(v => v !== opt.value);
                        }
                        field.onChange(current);
                      }}
                    />
                    <span className="font-normal">{opt.label}</span>
                  </label>
                ))}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {Array.isArray(form.watch("hearAbout")) && form.watch("hearAbout").includes("other") && (
        <FormField
          control={form.control}
          name="hearAboutOther"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Please specify</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Please specify"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function DevelopmentalHistorySection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="birthPregnancyDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Are you aware of any difficulties encountered during your birth or the pregnancy?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("birthPregnancyDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="birthPregnancyDifficultiesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="developmentalMilestonesOnTime"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Did you meet normal developmental milestones on time such as walking, talking, riding a bike?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("developmentalMilestonesOnTime") === "no" && (
        <FormField
          control={form.control}
          name="developmentalMilestonesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If no, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="seenOtherSpecialists"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Have you ever seen any other specialists (e.g. speech specialists) or been assessed for learning difficulties such as dyslexia?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("seenOtherSpecialists") === "yes" && (
        <FormField
          control={form.control}
          name="seenOtherSpecialistsDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="seriousIllnesses"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Have you ever suffered from any serious illnesses?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("seriousIllnesses") === "yes" && (
        <FormField
          control={form.control}
          name="seriousIllnessesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="currentMedication"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Are you currently taking any medication?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("currentMedication") === "yes" && (
        <FormField
          control={form.control}
          name="currentMedicationDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="hearingWithinNormalLimits"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Is your hearing within normal limits?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("hearingWithinNormalLimits") === "no" && (
        <FormField
          control={form.control}
          name="hearingWithinNormalLimitsDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If no, please provide more details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="hearingHistoricalIssues"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Have you had any historical difficulties with hearing i.e. glue ear or grommets?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("hearingHistoricalIssues") === "yes" && (
        <FormField
          control={form.control}
          name="hearingHistoricalIssuesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide more details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function VisionVisualDifficultiesSection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <div className="bg-red-100 p-4 rounded-md text-sm">
        In order to proceed with the assessment, you MUST have had a sight test
        within the last 2 years. In some cases, difficulties with reading are
        caused by visual difficulties that are not related to learning.
        Therefore, if having answered the questions below, you suspect there are
        visual difficulties* you MUST discuss this at the eye test so that the
        Optician (Optometrist) carrying out the eye test, can refer you to an
        Ophthalmologist for further investigation, prior to the assessment.
      </div>

      <FormField
        control={form.control}
        name="visualHistory"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Have you had any history of visual difficulties / problems with
              sight / visual impairment?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("visualHistory") === "yes" && (
        <FormField
          control={form.control}
          name="visualHistoryDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="lastSightTestDate"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              What date did you last have a sight test by an optometrist
              (optician)?
            </FormLabel>
            <FormControl>
              <StableInput
                type="date"
                value={field.value || ''}
                onChange={field.onChange}
                onBlur={field.onBlur}
                name={field.name}
                ref={field.ref}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="prescriptionMade"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Was any prescription made?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("prescriptionMade") === "yes" && (
        <FormField
          control={form.control}
          name="prescriptionDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, were advised to wear the prescription glasses/contact lenses for distance (e.g. for watching television or for driving) or near (e.g. reading) or both?"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="wearPrescribedGlasses"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Do you wear the prescribed glasses/contact lenses? You must bring
              them with you to the assessment, unless they are for distance only.
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="usedColouredOverlays"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Have you ever used coloured overlays / colour-tinted glasses?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("usedColouredOverlays") === "yes" && (
        <FormField
          control={form.control}
          name="usedColouredOverlaysDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder={`If yes please provide the following information:\nWho advised and provided them?\n\nWhy were they recommended?\n\nDid they help? If yes, in what way?\n\nDo you still use them? If not, why not?`}
                  className="min-h-[150px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="screenTimeHours"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Approximately how many hours per working/study day do you spend at
              a screen (phone, tablet, computer) etc?
            </FormLabel>
            <FormControl>
              <StableInput
                value={field.value || ''}
                onChange={field.onChange}
                onBlur={field.onBlur}
                name={field.name}
                ref={field.ref}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="readingHours"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Approximately how many additional hours per day do you spend
              reading books, newspapers, comics or other paper-based texts?
            </FormLabel>
            <FormControl>
              <StableInput
                value={field.value || ''}
                onChange={field.onChange}
                onBlur={field.onBlur}
                name={field.name}
                ref={field.ref}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="increasedScreenTime"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Has your screen /reading /near work time increased recently? If so,
              by how much?
            </FormLabel>
            <FormControl>
              <StableInput
                value={field.value || ''}
                onChange={field.onChange}
                onBlur={field.onBlur}
                name={field.name}
                ref={field.ref}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Visual difficulties questionnaire */}
      <div className="space-y-2">
        <p className="text-sm font-medium">
          Response categories: Always = every day&nbsp;&nbsp; Often = several times a week but not necessarily every day&nbsp;&nbsp; Sometimes = 2-3 times a month&nbsp;&nbsp; Rarely = only once every few months / a year
          <br />
          <span className="font-normal">*Visual difficulties should be investigated if you answered ‘always’ or ‘sometimes’ to several questions.</span>
        </p>
        <Table className="border text-center text-sm">
          <TableHeader>
            <TableRow>
              <TableHead className="text-left">Question</TableHead>
              <TableHead>Never</TableHead>
              <TableHead>Rarely</TableHead>
              <TableHead>Sometimes</TableHead>
              <TableHead>Often</TableHead>
              <TableHead>Always</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[
              { key: "visionQ1", q: "Do you get headaches when you read?" },
              { key: "visionQ2", q: "Does reading make your eyes feel sore, gritty or watery?" },
              { key: "visionQ3", q: "Does reading make you feel tired or sleepy?" },
              { key: "visionQ4", q: "Do you become restless or fidgety or distracted when reading?" },
              { key: "visionQ5", q: "Do you become less comfortable the longer you read?" },
              { key: "visionQ6", q: "Do you prefer dim light to bright light for reading?" },
              { key: "visionQ7", q: "Does reading from white paper seem too bright or glaring?" },
              { key: "visionQ8", q: "Do parts of the white page between the words form patterns when you read?" },
              { key: "visionQ9", q: "Does the print or background shimmer or appear coloured as you read?" },
              { key: "visionQ10", q: "Does print appear to jitter or move on the page as you read?" },
              { key: "visionQ11", q: "Do you screw your eyes up when reading?" },
              { key: "visionQ12", q: "Do you rub your eyes to relieve the strain when you are reading?" },
              { key: "visionQ13", q: "Does text appear blurred, or go in and out of focus, when you read?" },
              { key: "visionQ14", q: "Do you move your eyes around or blink to keep text clear when you are reading?" },
              { key: "visionQ15", q: "Do objects in the distance appear more blurred after you have been reading?" },
              { key: "visionQ16", q: "Do you lose your place when reading?" },
              { key: "visionQ17", q: "Do you re-read or skip words or lines when reading?" },
              { key: "visionQ18", q: "Do you use a marker or your finger to stop you losing the place when you read?" },
              { key: "visionQ19", q: "Do you cover or close one eye when reading?" },
              { key: "visionQ20", q: "Do the words, page or book appear double when you are reading?" },
            ].map(({ key, q }, index) => (
              <FormField key={key} control={form.control} name={key} render={({ field }) => (
                <TableRow>
                  <TableCell className="text-left">{index + 1}. {q}</TableCell>
                  {(["never", "rarely", "sometimes", "often", "always"] as const).map(opt => (
                    <TableCell key={opt}>
                      <Checkbox
                        checked={field.value === opt}
                        onCheckedChange={() => field.onChange(opt)}
                      />
                    </TableCell>
                  ))}
                </TableRow>
              )} />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export function FamilialHistorySection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="isAdopted"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Are you adopted?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="prefer_not_to_say" />
                  </FormControl>
                  <FormLabel className="font-normal">Prefer Not to Say</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="familyLearningDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Have any other family members experienced difficulties with spelling / reading / writing / learning?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("familyLearningDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="familyLearningDifficultiesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="If yes, provide further details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function EducationalHistorySection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="secondarySchools"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Which secondary schools did you attend?</FormLabel>
            <FormControl>
              <Textarea className="min-h-[80px]" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="goodSubjects"
        render={({ field }) => (
          <FormItem>
            <FormLabel>What subjects were you good at?</FormLabel>
            <FormControl>
              <Textarea className="min-h-[80px]" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="difficultiesNoticedAge"
        render={({ field }) => (
          <FormItem>
            <FormLabel>How old were you when your difficulties were first noticed?</FormLabel>
            <FormControl>
              <Textarea className="min-h-[80px]" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <FormField
          control={form.control}
          name="goodTeacherRelationship"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Did you have a good relationship with your teachers?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-row space-x-4"
                >
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="yes" />
                    </FormControl>
                    <FormLabel className="font-normal">Yes</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="no" />
                    </FormControl>
                    <FormLabel className="font-normal">No</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="workedHard"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Did you work as hard in school as you might have done?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-row space-x-4"
                >
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="yes" />
                    </FormControl>
                    <FormLabel className="font-normal">Yes</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="no" />
                    </FormControl>
                    <FormLabel className="font-normal">No</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="couldNotKeepUp"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Did you feel you could not keep up, academically, with the others in your class?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div>
        <h4 className="font-medium mb-2">Did you have any difficulties at school with any of the following?</h4>
        <Table className="border text-center text-sm">
          <TableHeader>
            <TableRow>
              <TableHead className="text-left">Area</TableHead>
              <TableHead className="text-center">Slight</TableHead>
              <TableHead className="text-center">Moderate</TableHead>
              <TableHead className="text-center">Severe</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[
              { key: "diffReading", q: "Reading" },
              { key: "diffSpelling", q: "Spelling" },
              { key: "diffWriting", q: "Writing" },
              { key: "diffMaths", q: "Mathematics" },
              { key: "diffEssays", q: "Essays" },
              { key: "diffRevision", q: "Revision" },
              { key: "diffSportsGames", q: "Sports and Games" },
            ].map(({ key, q }) => (
              <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
                <TableRow>
                  <TableCell className="text-left">{q}</TableCell>
                  {(["slight", "moderate", "severe"] as const).map(opt => (
                    <TableCell key={opt}>
                      <Checkbox
                        checked={field.value === opt}
                        onCheckedChange={() => field.onChange(opt)}
                      />
                    </TableCell>
                  ))}
                </TableRow>
              )} />
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="space-y-2">
        <FormLabel>Please give details of any study you have done since school</FormLabel>
        <Table className="border text-sm">
          <TableHeader>
            <TableRow>
              <TableHead>College/University</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Course</TableHead>
              <TableHead>Qualification gained</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 4 }).map((_, idx) => (
              <TableRow key={idx}>
                <TableCell>
                  <FormField control={form.control} name={`study${idx + 1}College` as any} render={({ field }) => (
                    <Textarea className="min-h-[60px]" {...field} />
                  )} />
                </TableCell>
                <TableCell>
                  <FormField control={form.control} name={`study${idx + 1}Date` as any} render={({ field }) => (
                    <Textarea className="min-h-[60px]" {...field} />
                  )} />
                </TableCell>
                <TableCell>
                  <FormField control={form.control} name={`study${idx + 1}Course` as any} render={({ field }) => (
                    <Textarea className="min-h-[60px]" {...field} />
                  )} />
                </TableCell>
                <TableCell>
                  <FormField control={form.control} name={`study${idx + 1}Qualification` as any} render={({ field }) => (
                    <Textarea className="min-h-[60px]" {...field} />
                  )} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export function WorkHistorySection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="currentlyInWork"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Are you currently in work?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("currentlyInWork") === "yes" && (
        <>
          <FormField
            control={form.control}
            name="currentEmployer"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea
                    placeholder="If Yes, what is the name of your employer?"
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="currentJobTitle"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea
                    placeholder="What is your current job title / role?"
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}

      <FormField
        control={form.control}
        name="previousWorkDetails"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Please give details of any previous work you have done
            </FormLabel>
            <FormControl>
              <Textarea className="min-h-[100px]" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

export function CurrentSituationSection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="assessmentReason"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Textarea
                placeholder="Briefly explain why you wish to be assessed"
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="currentSituationDetails"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Textarea
                placeholder="Please summarise your difficulties and say if there is anything you would like help with in particular. Have any strategies worked for you so far?  For example, when planning your work do you mostly think in pictures or words or both? Please include any information which you feel may be relevant."
                className="min-h-[150px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

export function LiteracySection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <p className="text-muted-foreground mb-4">Do you have problems with</p>

      <div>
        <h3 className="text-lg font-medium mb-2">Word Reading</h3>
        <div className="space-y-4 pl-4">
          {[
            { key: "readingIdentifySounds", q: "Identifying the sounds in words?" },
            { key: "readingAloudFearIncorrect", q: "Reading aloud and fear of getting it incorrect?" },
            { key: "readingFluencyAccuracy", q: "Reading fluently and accurately?" },
            { key: "readingSpeed", q: "Speed of reading?" },
          ].map(({ key, q }) => (
            <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>{q}</FormLabel>
                <FormControl>
                  <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Comprehension</h3>
        <div className="space-y-4 pl-4">
          {[
            { key: "comprehensionUnderstandingRead", q: "Understanding what you have read?" },
            { key: "comprehensionKeyPoints", q: "Identifying key points when faced with large quantities of information?" },
          ].map(({ key, q }) => (
            <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>{q}</FormLabel>
                <FormControl>
                  <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Writing</h3>
        <div className="space-y-4 pl-4">
          {[
            { key: "writingTakingNotes", q: "Taking notes, e.g. at meetings or lectures?" },
            { key: "writingCopyingInfo", q: "Copying information from one source to another?" },
            { key: "writingReports", q: "Producing written reports, essays, or other lengthy documents?" },
            { key: "writingProofreading", q: "Proofreading your written work?" },
            { key: "writingSummarising", q: "Summarising information?" },
            { key: "writingFillingForms", q: "Filling in forms correctly?" },
            { key: "writingMuddleWords", q: "Do you sometimes muddle up words in sentences so that they don’t make sense or are grammatically incorrect?" },
            { key: "writingLongSentences", q: "Do you write long, rambling sentences?" },
            { key: "writingWriteEverything", q: "Do you tend to write down everything as it comes into your head?" },
            { key: "writingAvoidWriting", q: "Do you avoid writing in front of others?" },
            { key: "writingMissPunctuation", q: "Do you miss out full stops, commas, and other punctuation marks?" },
          ].map(({ key, q }) => (
            <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>{q}</FormLabel>
                <FormControl>
                  <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Spelling</h3>
        <div className="space-y-4 pl-4">
          {[
            { key: "spellingManyErrors", q: "Do you feel your work contains many spelling errors?" },
            { key: "spellingMisspellEasy", q: "Do you miss-spell ‘easy’ words?" },
            { key: "spellingMissOutEndings", q: "Do you miss out little words or the endings of words?" },
            { key: "spellingAvoidHardWords", q: "Do you avoid using words you cannot spell?" },
          ].map(({ key, q }) => (
            <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>{q}</FormLabel>
                <FormControl>
                  <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
          ))}
        </div>
      </div>
    </div>
  );
}

export function PlanningOrganisationalSection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <p className="text-muted-foreground mb-4">Do you have problems with</p>
      <div className="space-y-4 pl-4">
        {[
          { key: "planningAhead", q: "Planning ahead?" },
          { key: "planningOrganising", q: "Organising yourself?" },
          { key: "planningPrioritisingWorkload", q: "Prioritising your workload?" },
          { key: "planningMeetingDeadlines", q: "Meeting deadlines?" },
          { key: "planningWorkingUnderPressure", q: "Working under pressure of time (e.g., in examinations)?" },
          { key: "planningPutOffTasks", q: "Do you put off starting tasks until the last minute?" },
          { key: "planningConfusedDatesTimes", q: "Do you get confused over dates and times and miss appointments?" },
        ].map(({ key, q }) => (
          <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>{q}</FormLabel>
              <FormControl>
                <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="yes" />
                    </FormControl>
                    <FormLabel className="font-normal">Yes</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="no" />
                    </FormControl>
                    <FormLabel className="font-normal">No</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />
        ))}
      </div>
    </div>
  );
}

export function MemoryAttentionSection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <div className="space-y-4 pl-4">
        {[
          { key: "memoryRememberInstructions", q: "Do you have difficulties remembering instructions/new information?" },
          { key: "memoryLoseConcentration", q: "Do you often lose concentration?" },
          { key: "memoryMultiplicationTables", q: "Did you find it difficult to learn your multiplication tables?" },
          { key: "memoryLoseTrackTasks", q: "Do you sometimes lose track of where you are in a task and must start again?" },
          { key: "memoryRememberSequences", q: "Do you find it hard to remember sequences of letters or numbers such as telephone numbers or car registrations?" },
        ].map(({ key, q }) => (
          <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>{q}</FormLabel>
              <FormControl>
                <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="yes" />
                    </FormControl>
                    <FormLabel className="font-normal">Yes</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="no" />
                    </FormControl>
                    <FormLabel className="font-normal">No</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />
        ))}
      </div>
    </div>
  );
}

export function NumberEstimationSection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <div className="space-y-4 pl-4">
        {[
          { key: "numberForgetOperations", q: "Do you tend to forget mathematical operations that are used infrequently?" },
          { key: "numberHardWithoutCalculator", q: "Do you find it hard to calculate sums in arithmetic without a calculator?" },
          { key: "numberCalculationsInHead", q: "Do you find it difficult to do calculations in your head?" },
          { key: "numberManageFinances", q: "Do you find it hard to manage your day-to-day finances?" },
        ].map(({ key, q }) => (
          <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>{q}</FormLabel>
              <FormControl>
                <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="yes" />
                    </FormControl>
                    <FormLabel className="font-normal">Yes</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="no" />
                    </FormControl>
                    <FormLabel className="font-normal">No</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />
        ))}
      </div>
    </div>
  );
}

export function FineGrossMotorSection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Orientation</h3>
        <div className="space-y-4 pl-4">
          {[
            { key: "orientationLeftRight", q: "Do you have difficulty telling left from right?" },
            { key: "orientationRememberDirections", q: "Do you find it hard to remember directions?" },
            { key: "orientationRoadSigns", q: "Do you have difficulties reading road signs especially when driving?" },
            { key: "orientationMapReading", q: "Is map reading, or finding your way to a strange place confusing?" },
          ].map(({ key, q }) => (
            <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>{q}</FormLabel>
                <FormControl>
                  <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Coordination and Dexterity</h3>
        <div className="space-y-4 pl-4">
          {[
            { key: "coordinationLeftRight", q: "Do you have difficulty telling left from right?" },
            { key: "coordinationPoorCoordination", q: "Do you have poor coordination?" },
            { key: "coordinationLearnPracticalTasks", q: "Find it difficult to learn how to do practical tasks?" },
            { key: "coordinationSmallTools", q: "Find it difficult to work with small tools or components?" },
            { key: "coordinationKeyboardMouse", q: "Have difficulties in using a keyboard or mouse?" },
            { key: "coordinationDropThings", q: "Often drop things, or bump into things?" },
            { key: "coordinationLearningDrive", q: "Did you find it difficult learning to drive?" },
            { key: "coordinationCurrentDriving", q: "Do you have any current difficulties with driving?" },
          ].map(({ key, q }) => (
            <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>{q}</FormLabel>
                <FormControl>
                  <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
          ))}
        </div>
      </div>
    </div>
  );
}

export function SocialCommunicationSection() {
  const form = useFormContext<AssesseeFormValues>();
  const socialYes = [
    form.watch("socialWorkingRelationships"),
    form.watch("socialFriendships"),
    form.watch("socialEyeContact"),
    form.watch("socialSituations"),
  ].some((v) => v === "yes");

  return (
    <div className="space-y-6">
      <p className="text-muted-foreground mb-4">
        In day to day experiences at work, or on any courses you have taken, have you had difficulties with any of the following
      </p>

      <div>
        <h3 className="text-lg font-medium mb-2">Social</h3>
        <div className="space-y-4 pl-4">
          {[
            { key: "socialWorkingRelationships", q: "Do you have any difficulties developing good working relationships?" },
            { key: "socialFriendships", q: "Do you have any difficulty developing friendships?" },
            { key: "socialEyeContact", q: "Do you find it hard to make eye contact with people?" },
            { key: "socialSituations", q: "Do you feel uncomfortable in social situations?" },
          ].map(({ key, q }) => (
            <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>{q}</FormLabel>
                <FormControl>
                  <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
          ))}

          {socialYes && (
            <FormField
              control={form.control}
              name="socialDetails"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea placeholder="If you answered yes to any of the above questions, please give details:" className="min-h-[100px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Communication</h3>
        <div className="space-y-4 pl-4">
          {[
            { key: "communicationThinkWords", q: "Find it difficult to think of the words to express what you want to say?" },
            { key: "communicationLoseTrack", q: "Lose track of what you want to say, or what other people are saying?" },
            { key: "communicationGroupDiscussions", q: "Have difficulty following the conversation in group discussions?" },
            { key: "communicationMisinterpreted", q: "Sometimes find you have completely misinterpreted what you have been asked?" },
            { key: "communicationFreezePublic", q: "Get confused or freeze up if you must speak or read aloud in public?" },
            { key: "communicationTelephoneMessages", q: "Sometimes find it difficult to take telephone messages and pass them on accurately?" },
          ].map(({ key, q }) => (
            <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>{q}</FormLabel>
                <FormControl>
                  <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-row space-x-4">
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )} />
          ))}
        </div>
      </div>
    </div>
  );
}

export function StrengthsSection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="strengths"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Textarea
                placeholder="Please provide information about your strengths, what you are good at, hobbies you enjoy etc."
                className="min-h-[150px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

export function OtherInformationSection() {
  const form = useFormContext<AssesseeFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="helpCompletingQuestionnaire"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Did anyone help you to complete this questionnaire?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("helpCompletingQuestionnaire") === "yes" && (
        <FormField
          control={form.control}
          name="helpCompletingQuestionnaireDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="If yes, please provide further details"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="assistanceDailyLiving"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Do you receive assistance with day-to-day living, for example, from a carer?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("assistanceDailyLiving") === "yes" && (
        <FormField
          control={form.control}
          name="assistanceDailyLivingDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="lf yes, please give further information on what you need help with, such as washing and dressing, or managing your finances etc."
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="diagnosedLearningDisability"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Have you ever been told you have, or been diagnosed with, a learning disability, e.g., moderate learning difficulty, severe learning difficulty, or global learning difficulty?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("diagnosedLearningDisability") === "yes" && (
        <FormField
          control={form.control}
          name="diagnosedLearningDisabilityDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="lf yes, please provide further details"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="receivePIPorDLA"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Do you receive the Personal Independence Payment (PIP), or the Disability Living Allowance (DLA)?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("receivePIPorDLA") === "yes" && (
        <FormField
          control={form.control}
          name="receivePIPorDLADetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="lf yes, please provide further details"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="otherInformation"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Textarea
                placeholder="Any other information not covered within this questionnaire that we should know before the assessment"
                className="min-h-[150px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

