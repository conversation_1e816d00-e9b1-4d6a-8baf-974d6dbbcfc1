import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { StableInput } from "@/components/forms/stable-input";
import { COUNTRIES } from "@/constants/countries";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  PersonalInformationSection,
  DevelopmentalHistorySection,
  VisionVisualDifficultiesSection,
  FamilialHistorySection,
  EducationalHistorySection,
  WorkHistorySection,
  CurrentSituationSection,
  LiteracySection,
  PlanningOrganisationalSection,
  MemoryAttentionSection,
  NumberEstimationSection,
  FineGrossMotorSection,
  SocialCommunicationSection,
  StrengthsSection,
  OtherInformationSection,
} from "./assessee-over-16-sections";
import { useToast } from "@/hooks/use-toast";
import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Check, XCircle, Edit, Save } from "lucide-react";

// Create schema based on the Assessee Pre-Assessment Questionnaire for over 16
// Separate schema for each form section to ensure complete isolation
const personalInfoSchema = z.object({
  // Personal details only
  fullName: z.string().min(2, "Name must be at least 2 characters"),
  preferredName: z.string().optional(),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  age: z.string().min(1, "Age is required"),
  schoolYear: z.string().optional(),
  countryOfBirth: z.enum(COUNTRIES, {
    required_error: "Country of birth is required",
  }),
  dateMovedToUK: z.string().optional(),
  educationalEstablishment: z.string().optional(), // Made optional for now
  gender: z.enum(["male", "female", "neutral", "prefer_not_to_say"]),
  preferredPronoun: z.enum(["he", "she", "they", "other"]),
  preferredPronounOther: z.string().optional(),
  // Moving contact details to personal info section
  contactAddress: z.string().min(5, "Address is required"),
  contactPhone: z.string().min(5, "Phone number is required"),
  contactPhoneWork: z.string().optional(),
  contactEmail: z.string().email("Please enter a valid email"),
  hearAbout: z.array(z.string()).optional(),
  hearAboutOther: z.string().optional(),
});

// Combined schema for final submission
const assesseeFormSchema = personalInfoSchema.extend({
  // Developmental history
  birthPregnancyDifficulties: z.enum(["yes", "no"]).optional(),
  birthPregnancyDifficultiesDetails: z.string().optional(),
  developmentalMilestonesOnTime: z.enum(["yes", "no"]).optional(),
  developmentalMilestonesDetails: z.string().optional(),
  seenOtherSpecialists: z.enum(["yes", "no"]).optional(),
  seenOtherSpecialistsDetails: z.string().optional(),
  seriousIllnesses: z.enum(["yes", "no"]).optional(),
  seriousIllnessesDetails: z.string().optional(),
  currentMedication: z.enum(["yes", "no"]).optional(),
  currentMedicationDetails: z.string().optional(),
  hearingWithinNormalLimits: z.enum(["yes", "no"]).optional(),
  hearingWithinNormalLimitsDetails: z.string().optional(),
  hearingHistoricalIssues: z.enum(["yes", "no"]).optional(),
  hearingHistoricalIssuesDetails: z.string().optional(),

  // Familial history
  isAdopted: z.enum(["yes", "no", "prefer_not_to_say"]).optional(),
  familyLearningDifficulties: z.enum(["yes", "no"]).optional(),
  familyLearningDifficultiesDetails: z.string().optional(),

  // Educational history
  secondarySchools: z.string().optional(),
  goodSubjects: z.string().optional(),
  difficultiesNoticedAge: z.string().optional(),
  goodTeacherRelationship: z.enum(["yes", "no"]).optional(),
  workedHard: z.enum(["yes", "no"]).optional(),
  couldNotKeepUp: z.enum(["yes", "no"]).optional(),
  diffReading: z.enum(["slight", "moderate", "severe"]).optional(),
  diffSpelling: z.enum(["slight", "moderate", "severe"]).optional(),
  diffWriting: z.enum(["slight", "moderate", "severe"]).optional(),
  diffMaths: z.enum(["slight", "moderate", "severe"]).optional(),
  diffEssays: z.enum(["slight", "moderate", "severe"]).optional(),
  diffRevision: z.enum(["slight", "moderate", "severe"]).optional(),
  diffSportsGames: z.enum(["slight", "moderate", "severe"]).optional(),
  study1College: z.string().optional(),
  study1Date: z.string().optional(),
  study1Course: z.string().optional(),
  study1Qualification: z.string().optional(),
  study2College: z.string().optional(),
  study2Date: z.string().optional(),
  study2Course: z.string().optional(),
  study2Qualification: z.string().optional(),
  study3College: z.string().optional(),
  study3Date: z.string().optional(),
  study3Course: z.string().optional(),
  study3Qualification: z.string().optional(),
  study4College: z.string().optional(),
  study4Date: z.string().optional(),
  study4Course: z.string().optional(),
  study4Qualification: z.string().optional(),

  // Work history
  currentlyInWork: z.enum(["yes", "no"]).optional(),
  currentEmployer: z.string().optional(),
  currentJobTitle: z.string().optional(),
  previousWorkDetails: z.string().optional(),

  // Current situation
  assessmentReason: z.string().optional(),
  currentSituationDetails: z.string().optional(),

  // Literacy
  readingIdentifySounds: z.enum(["yes", "no"]).optional(),
  readingAloudFearIncorrect: z.enum(["yes", "no"]).optional(),
  readingFluencyAccuracy: z.enum(["yes", "no"]).optional(),
  readingSpeed: z.enum(["yes", "no"]).optional(),

  comprehensionUnderstandingRead: z.enum(["yes", "no"]).optional(),
  comprehensionKeyPoints: z.enum(["yes", "no"]).optional(),

  writingTakingNotes: z.enum(["yes", "no"]).optional(),
  writingCopyingInfo: z.enum(["yes", "no"]).optional(),
  writingReports: z.enum(["yes", "no"]).optional(),
  writingProofreading: z.enum(["yes", "no"]).optional(),
  writingSummarising: z.enum(["yes", "no"]).optional(),
  writingFillingForms: z.enum(["yes", "no"]).optional(),
  writingMuddleWords: z.enum(["yes", "no"]).optional(),
  writingLongSentences: z.enum(["yes", "no"]).optional(),
  writingWriteEverything: z.enum(["yes", "no"]).optional(),
  writingAvoidWriting: z.enum(["yes", "no"]).optional(),
  writingMissPunctuation: z.enum(["yes", "no"]).optional(),

  spellingManyErrors: z.enum(["yes", "no"]).optional(),
  spellingMisspellEasy: z.enum(["yes", "no"]).optional(),
  spellingMissOutEndings: z.enum(["yes", "no"]).optional(),
  spellingAvoidHardWords: z.enum(["yes", "no"]).optional(),

  // Planning and organisational ability
  planningAhead: z.enum(["yes", "no"]).optional(),
  planningOrganising: z.enum(["yes", "no"]).optional(),
  planningPrioritisingWorkload: z.enum(["yes", "no"]).optional(),
  planningMeetingDeadlines: z.enum(["yes", "no"]).optional(),
  planningWorkingUnderPressure: z.enum(["yes", "no"]).optional(),
  planningPutOffTasks: z.enum(["yes", "no"]).optional(),
  planningConfusedDatesTimes: z.enum(["yes", "no"]).optional(),

  // Memory, attention and concentration
  memoryRememberInstructions: z.enum(["yes", "no"]).optional(),
  memoryLoseConcentration: z.enum(["yes", "no"]).optional(),
  memoryMultiplicationTables: z.enum(["yes", "no"]).optional(),
  memoryLoseTrackTasks: z.enum(["yes", "no"]).optional(),
  memoryRememberSequences: z.enum(["yes", "no"]).optional(),

  // Number, estimation and calculation
  numberForgetOperations: z.enum(["yes", "no"]).optional(),
  numberHardWithoutCalculator: z.enum(["yes", "no"]).optional(),
  numberCalculationsInHead: z.enum(["yes", "no"]).optional(),
  numberManageFinances: z.enum(["yes", "no"]).optional(),

  // Fine and gross motor skills - orientation
  orientationLeftRight: z.enum(["yes", "no"]).optional(),
  orientationRememberDirections: z.enum(["yes", "no"]).optional(),
  orientationRoadSigns: z.enum(["yes", "no"]).optional(),
  orientationMapReading: z.enum(["yes", "no"]).optional(),

  // Fine and gross motor skills - coordination and dexterity
  coordinationLeftRight: z.enum(["yes", "no"]).optional(),
  coordinationPoorCoordination: z.enum(["yes", "no"]).optional(),
  coordinationLearnPracticalTasks: z.enum(["yes", "no"]).optional(),
  coordinationSmallTools: z.enum(["yes", "no"]).optional(),
  coordinationKeyboardMouse: z.enum(["yes", "no"]).optional(),
  coordinationDropThings: z.enum(["yes", "no"]).optional(),
  coordinationLearningDrive: z.enum(["yes", "no"]).optional(),
  coordinationCurrentDriving: z.enum(["yes", "no"]).optional(),

  // Social and communication skills - social
  socialWorkingRelationships: z.enum(["yes", "no"]).optional(),
  socialFriendships: z.enum(["yes", "no"]).optional(),
  socialEyeContact: z.enum(["yes", "no"]).optional(),
  socialSituations: z.enum(["yes", "no"]).optional(),
  socialDetails: z.string().optional(),

  // Social and communication skills - communication
  communicationThinkWords: z.enum(["yes", "no"]).optional(),
  communicationLoseTrack: z.enum(["yes", "no"]).optional(),
  communicationGroupDiscussions: z.enum(["yes", "no"]).optional(),
  communicationMisinterpreted: z.enum(["yes", "no"]).optional(),
  communicationFreezePublic: z.enum(["yes", "no"]).optional(),
  communicationTelephoneMessages: z.enum(["yes", "no"]).optional(),

  // Strengths
  strengths: z.string().optional(),

  // Other information
  helpCompletingQuestionnaire: z.enum(["yes", "no"]).optional(),
  helpCompletingQuestionnaireDetails: z.string().optional(),
  assistanceDailyLiving: z.enum(["yes", "no"]).optional(),
  assistanceDailyLivingDetails: z.string().optional(),
  diagnosedLearningDisability: z.enum(["yes", "no"]).optional(),
  diagnosedLearningDisabilityDetails: z.string().optional(),
  receivePIPorDLA: z.enum(["yes", "no"]).optional(),
  receivePIPorDLADetails: z.string().optional(),
  otherInformation: z.string().optional(),

});

export type AssesseeFormValues = z.infer<typeof assesseeFormSchema>;

const requiredFieldsPerSection: Record<number, Array<keyof AssesseeFormValues>> = {
  1: [
    "fullName",
    "dateOfBirth",
    "age",
    "countryOfBirth",
    "contactAddress",
    "contactPhone",
    "contactEmail",
  ],
};

interface AssesseeOver16FormProps {
  formId?: number;
  initialData?: any;
  formData?: any;
  onSubmit: (data: any) => void;
  onSaveDraft?: (data: AssesseeFormValues) => void;
  isPublicForm?: boolean;
}

export function AssesseeOver16Form({ formId = 0, initialData, formData, onSubmit, onSaveDraft, isPublicForm = false }: AssesseeOver16FormProps) {
  const { toast } = useToast();
  
  // Debug to see what data we're receiving
  useEffect(() => {
    console.log('AssesseeOver16Form received formData:', formData);
    if (formData?.assessee) {
      console.log('Assessee information:', formData.assessee);
    }
  }, [formData]);
  
  // Always start at section 1 when entering the form
  const [currentSection, setCurrentSection] = useState(1);
  
  // For accordion functionality - track the currently open section.
  // Use an empty string to represent no open section to keep the accordion
  // controlled at all times.
  const [openSection, setOpenSection] = useState<string>("");
  
  const isInitialRender = useRef(true);
  
  // Main form for all fields
  const form = useForm<AssesseeFormValues>({
    resolver: zodResolver(assesseeFormSchema),
    defaultValues: initialData ? {
      ...initialData,
    } : {
      gender: "prefer_not_to_say",
      preferredPronoun: "they",
      preferredPronounOther: "",
    },
    mode: "onBlur", // Only validate when field loses focus
  });

  const watchAllFields = form.watch();

  const isConditionalFieldRequired = useCallback(
    (field: keyof AssesseeFormValues) => {
      const allValues = watchAllFields as Record<string, any>;
      if (typeof field !== "string") return true;

      if (field.endsWith("Details")) {
        const base = field.replace(/Details$/, "");
        const trigger = allValues[base];
        return trigger === "yes";
      }

      if (field.endsWith("Other")) {
        const base = field.replace(/Other$/, "");
        const trigger = allValues[base];
        if (Array.isArray(trigger)) {
          return trigger.includes("other");
        }
        return trigger === "other";
      }

      return true;
    },
    [watchAllFields]
  );

  const sectionCompletion = useMemo(() => {
    const errors = form.formState.errors as Record<string, any>;
    const completion: Record<number, boolean> = {};
    for (const [section, fields] of Object.entries(requiredFieldsPerSection)) {
      completion[Number(section)] = (fields as Array<keyof AssesseeFormValues>).every((field) => {
        if (!isConditionalFieldRequired(field)) return true;
        const value = (watchAllFields as any)[field];
        const error = errors[field];
        if (typeof value === "boolean") {
          return value === true && !error;
        }
        return value !== undefined && value !== "" && !error;
      });
    }
    return completion;
  }, [watchAllFields, form.formState.errors, isConditionalFieldRequired]);
  
  // Save form state to localStorage - with debounce to prevent input issues
  const saveFormStateToLocalStorage = useCallback(() => {
    // Only save when not typing (prevents interrupting input)
    if (document.activeElement?.tagName !== 'INPUT' && 
        document.activeElement?.tagName !== 'TEXTAREA') {
      try {
        const formValues = form.getValues();
        localStorage.setItem(`assesseeForm-${formId}-data`, JSON.stringify(formValues));
        console.log('Form state saved to localStorage:', formValues);
      } catch (e) {
        console.error('Error saving form state to localStorage:', e);
      }
    }
  }, [form, formId]);

  // Function to handle navigation between sections
  const navigateToSection = useCallback((newSection: number) => {
    // First, save the current form state to ensure no data is lost
    saveFormStateToLocalStorage();
    
    // Update the current section
    setCurrentSection(newSection);
    localStorage.setItem(`assesseeForm-${formId}-section`, newSection.toString());
    console.log(`Navigating to section ${newSection}`);
  }, [saveFormStateToLocalStorage, formId]);
  
  // Navigation functions
  
  const handleSaveDraft = useCallback(() => {
    // Get current form values with all fields properly captured
    const currentData = form.getValues();
    
    console.log("Saving draft with all form data:", currentData);
    
    // Save to localStorage directly here instead of using saveFormStateToLocalStorage
    try {
      localStorage.setItem(`assesseeForm-${formId}-data`, JSON.stringify(currentData));
    } catch (e) {
      console.error('Error saving form data to localStorage:', e);
    }
    
    // Call the parent component's onSaveDraft function if it exists
    if (onSaveDraft) {
      onSaveDraft(currentData);
    }
    
    // Show success message
    toast({
      title: "Draft saved",
      description: "Your form has been saved as a draft",
    });
  }, [form, formId, onSaveDraft, toast]);

  
  // Handle accordion change to navigate between sections
  const handleAccordionChange = useCallback(
    (value: string) => {
      // Always update the open section with the value from the accordion
      setOpenSection(value);

      // When collapsing (value is empty), no further action is needed
      if (!value) return;

      // Extract section number from the value (e.g., "section1" -> 1)
      const sectionMatch = value.match(/section(\d+)/);
      if (!sectionMatch) return;

      const newSection = parseInt(sectionMatch[1]);

      // Save current form state
      saveFormStateToLocalStorage();

      // Update current section
      setCurrentSection(newSection);

      // Persist section in localStorage
      localStorage.setItem(
        `assesseeForm-${formId}-section`,
        newSection.toString()
      );

      // Smoothly scroll the opened section into view so its content is visible
      const el = document.getElementById(value);
      if (el) {
        // Wait for the accordion animation to finish before scrolling
        requestAnimationFrame(() => {
          // Use the element's position relative to the page so we can offset
          const top = el.getBoundingClientRect().top + window.scrollY - 80; // 80px for sticky header
          window.scrollTo({ top, behavior: "smooth" });
        });
      }
    },
    [saveFormStateToLocalStorage, formId]
  );
  
  // Custom function to safely set form values without triggering validation
  const safelySetFormValue = useCallback((key: string, value: any) => {
    try {
      if (value !== undefined && value !== null) {
        form.setValue(key as any, value, {
          shouldValidate: false,     // Don't validate immediately
          shouldDirty: false,        // Don't mark as dirty (modified)
          shouldTouch: false         // Don't mark as touched
        });
      }
    } catch (e) {
      console.error(`Error setting form value for ${key}:`, e);
    }
  }, [form]);

  // Watch for changes to date of birth to automatically calculate age
  const watchedDOB = form.watch("dateOfBirth");

  useEffect(() => {
    if (watchedDOB) {
      const dob = new Date(watchedDOB);
      if (!isNaN(dob.getTime())) {
        const today = new Date();
        let age = today.getFullYear() - dob.getFullYear();
        const m = today.getMonth() - dob.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
          age--;
        }
        safelySetFormValue("age", age.toString());
      }
    } else {
      safelySetFormValue("age", "");
    }
  }, [watchedDOB, safelySetFormValue]);

  // Effect for data loading and form initialization
  useEffect(() => {
    // Mark initial render complete
    isInitialRender.current = false;
    
    // Try to load saved form data from localStorage first
    const savedForm = localStorage.getItem(`assesseeForm-${formId}-data`);
    let savedData = null;
    
    try {
      if (savedForm) {
        savedData = JSON.parse(savedForm);
        console.log('Found saved form data in localStorage:', savedData);
      }
    } catch (e) {
      console.error('Error parsing saved form data:', e);
    }
    
    // Pre-populate with assessee information if available
    if (formData?.assessee) {
      const assessee = formData.assessee;
      console.log('Pre-populating form with assessee data:', assessee);
      
      // Set personal information fields
      if (assessee.fullName) {
        console.log('Setting fullName:', assessee.fullName);
        safelySetFormValue('fullName', assessee.fullName);
      }
      
      if (assessee.dateOfBirth) {
        // Format date if needed
        const formattedDate = typeof assessee.dateOfBirth === 'string' 
          ? assessee.dateOfBirth.split('T')[0] // Extract YYYY-MM-DD from ISO date
          : assessee.dateOfBirth;
        console.log('Setting dateOfBirth:', formattedDate);
        safelySetFormValue('dateOfBirth', formattedDate);
      }
      
      // Set age if available or calculate from DOB
      if (assessee.age) {
        console.log('Setting age from provided value:', assessee.age);
        safelySetFormValue('age', assessee.age.toString());
      } else if (assessee.dateOfBirth) {
        // Calculate age from date of birth
        const dob = new Date(assessee.dateOfBirth);
        const today = new Date();
        let age = today.getFullYear() - dob.getFullYear();
        
        // Adjust age if birthday hasn't occurred yet this year
        const m = today.getMonth() - dob.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
          age--;
        }
        
        console.log('Setting calculated age:', age);
        safelySetFormValue('age', age.toString());
      }
    }
    
    // If we have saved data, populate the form with it (overrides assessee data)
    if (savedData) {
      // Loop through all saved fields and apply them to the form
      Object.keys(savedData).forEach((key) => {
        if (savedData[key] !== undefined && savedData[key] !== null) {
          safelySetFormValue(key, savedData[key]);
        }
      });
    }
  }, [form, formData, formId, safelySetFormValue]);
  
  const handleFormSubmit = (values: AssesseeFormValues) => {
    console.log("Form values for final submission:", values);
    
    // CRITICAL FIX: Ensure personal information is properly preserved
    if (formData?.assessee) {
      const assessee = formData.assessee;
      
      // Make sure personal info is always preserved
      if (!values.fullName && assessee.fullName) {
        console.log('Restoring fullName from assessee data for final submit:', assessee.fullName);
        values.fullName = assessee.fullName;
      }
      
      if (!values.dateOfBirth && assessee.dateOfBirth) {
        const formattedDate = typeof assessee.dateOfBirth === 'string'
          ? assessee.dateOfBirth.split('T')[0] 
          : assessee.dateOfBirth;
        console.log('Restoring dateOfBirth from assessee data for final submit:', formattedDate);
        values.dateOfBirth = formattedDate;
      }
      
      if (!values.age && assessee.age) {
        console.log('Restoring age from assessee data for final submit:', assessee.age);
        values.age = assessee.age.toString();
      }
    }
    
    // Save final version to localStorage before submission
    localStorage.setItem(`assesseeForm-${formId}-data`, JSON.stringify(values));
    
    // Always submit the complete raw form values to ensure all data is captured
    // The form-page component will handle the proper formatting and API submission
    console.log("Submitting form values:", values);
    console.log("Number of fields being submitted:", Object.keys(values).length);
    
    onSubmit(values);

    // Clear localStorage data after successful submission
    localStorage.removeItem(`assesseeForm-${formId}-data`);
    localStorage.removeItem(`assesseeForm-${formId}-section`);

    // Show success message
    toast({
      title: "Form submitted",
      description: "Your assessment form has been submitted successfully",
    });
  };
  
  // SECTION CONTENT COMPONENTS
  
return (
  
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)}>
        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Pre-Assessment Questionnaire (Over 16 Years)</CardTitle>
              <CardDescription>
                Please complete all sections of this form to provide us with the information needed for your assessment.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion
                type="single"
                collapsible={true}
                value={openSection}
                onValueChange={handleAccordionChange}
              >
                <AccordionItem id="section1" value="section1">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        1
                      </div>
                      <span>Personal Information</span>
                      {requiredFieldsPerSection[1] && (
                        sectionCompletion[1] ? (
                          <Check className="ml-2 h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="ml-2 h-4 w-4 text-destructive" />
                        )
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <PersonalInformationSection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section2" value="section2">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        2
                      </div>
                      <span>Developmental History</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <DevelopmentalHistorySection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section3" value="section3">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        3
                      </div>
                      <span>Vision and Visual Difficulties</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <VisionVisualDifficultiesSection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section4" value="section4">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        4
                      </div>
                      <span>Familial History</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <FamilialHistorySection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section5" value="section5">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        5
                      </div>
                      <span>Educational History</span>
                      {requiredFieldsPerSection[5] && (
                        sectionCompletion[5] ? (
                          <Check className="ml-2 h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="ml-2 h-4 w-4 text-destructive" />
                        )
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <EducationalHistorySection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section6" value="section6">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        6
                      </div>
                      <span>Work History</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <WorkHistorySection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section7" value="section7">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        7
                      </div>
                      <span>Current Situation</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <CurrentSituationSection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section8" value="section8">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        8
                      </div>
                      <span>Literacy</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <LiteracySection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section9" value="section9">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        9
                      </div>
                      <span>Planning and Organisational Ability</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <PlanningOrganisationalSection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section10" value="section10">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        10
                      </div>
                      <span>Memory, Attention and Concentration</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <MemoryAttentionSection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section11" value="section11">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        11
                      </div>
                      <span>Number, Estimation and Calculation</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <NumberEstimationSection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section12" value="section12">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        12
                      </div>
                      <span>Fine and Gross Motor Skills</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <FineGrossMotorSection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section13" value="section13">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        13
                      </div>
                      <span>Social and Communication Skills</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <SocialCommunicationSection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section14" value="section14">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        14
                      </div>
                      <span>Strengths</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <StrengthsSection />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem id="section15" value="section15">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center">
                      <div className="flex h-6 w-6 rounded-full items-center justify-center bg-primary text-primary-foreground mr-2 text-xs">
                        15
                      </div>
                      <span>Other Information</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <OtherInformationSection />
                  </AccordionContent>
                </AccordionItem>

              </Accordion>

              {/* Navigation and save buttons */}
              <div className="mt-8 flex justify-end">
                <div className="flex space-x-2">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={handleSaveDraft}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Save Draft
                  </Button>
                  <Button type="submit">
                    Submit
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </form>
    </Form>
  );
}

