import React from "react";
import type { SchoolFormValues } from "./school-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useFormContext } from "react-hook-form";

const attitudeOptions = [
  { value: "keen", label: "Keen" },
  { value: "distracts_others", label: "Distracts others" },
  { value: "independent", label: "Independent" },
  { value: "competent", label: "Competent" },
  { value: "works_well_with_help", label: "Works well with help" },
  { value: "slow", label: "Slow to complete work" },
  { value: "distractible", label: "Distractible" },
  { value: "lacks_interest", label: "Displays a lack of interest" },
];

const peerRelationshipOptions = [
  [
    { value: "popular", label: "Popular" },
    { value: "withdrawn", label: "Withdrawn" },
  ],
  [
    { value: "accepted", label: "Accepted" },
    { value: "better_with_younger", label: "Better with younger children" },
  ],
  [
    { value: "friendly", label: "Friendly" },
    { value: "avoids_others", label: "Avoids others" },
  ],
  [
    { value: "dominant", label: "Dominant in friendships" },
    { value: "one_special_friend", label: "Has one special friend" },
  ],
];

export function SchoolFormSection({ section }: { section: number }) {
  const form = useFormContext<SchoolFormValues>();
  switch (section) {
    case 1:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField control={form.control} name="childFullName" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Child's full name</FormLabel>
                    <FormControl>
                      <Input placeholder="Full name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="yearGroup" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Year group</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Year 5" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField control={form.control} name="school" render={({ field }) => (
                  <FormItem>
                    <FormLabel>School</FormLabel>
                    <FormControl>
                      <Input placeholder="School name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="personCompletingName" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name of person completing this form</FormLabel>
                    <FormControl>
                      <Input placeholder="Your name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField control={form.control} name="personCompletingTitle" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title (Mr/Mrs/Ms/Miss)</FormLabel>
                    <FormControl>
                      <Input placeholder="Your title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="personCompletingRole" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role in School</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. SENCO, Class Teacher" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField control={form.control} name="dateCompleted" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date form completed</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="sencoContactDetails" render={({ field }) => (
                  <FormItem>
                    <FormLabel>School SENCO contact details</FormLabel>
                    <FormControl>
                      <Textarea placeholder="SENCO contact information" className="min-h-[80px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
            </CardContent>
          </Card>
        </div>
      );
    case 2:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField control={form.control} name="parentFullName" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name(s)</FormLabel>
                    <FormControl>
                      <Input placeholder="Parent/guardian name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="parentTitle" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title (Mr/Mrs/Ms/Miss)</FormLabel>
                    <FormControl>
                      <Input placeholder="Parent/guardian title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              <FormField control={form.control} name="parentRelationship" render={({ field }) => (
                <FormItem>
                  <FormLabel>Relationship to child</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. Father, Mother, Guardian" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="parentAddress" render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Full address" className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="parentPostcode" render={({ field }) => (
                <FormItem>
                  <FormLabel>Postcode</FormLabel>
                  <FormControl>
                    <Input placeholder="Postcode" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField control={form.control} name="parentMobile" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mobile number(s)</FormLabel>
                    <FormControl>
                      <Input placeholder="Contact number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="parentEmail" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="Email address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
          </CardContent>
        </Card>
      </div>
      );
    case 3:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <Table className="border text-sm">
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-left whitespace-nowrap">SATs / End of Key Stage Results</TableHead>
                    <TableHead className="text-left">English</TableHead>
                    <TableHead className="text-left">Maths</TableHead>
                    <TableHead className="text-left">Science</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="text-left font-medium">Key Stage 1</TableCell>
                    <TableCell>
                      <FormField
                        control={form.control}
                        name="ks1English"
                        render={({ field }) => (
                          <Input className="h-8 border-none bg-transparent" {...field} />
                        )}
                      />
                    </TableCell>
                    <TableCell>
                      <FormField
                        control={form.control}
                        name="ks1Maths"
                        render={({ field }) => (
                          <Input className="h-8 border-none bg-transparent" {...field} />
                        )}
                      />
                    </TableCell>
                    <TableCell>
                      <FormField
                        control={form.control}
                        name="ks1Science"
                        render={({ field }) => (
                          <Input className="h-8 border-none bg-transparent" {...field} />
                        )}
                      />
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="text-left font-medium">Key Stage 2</TableCell>
                    <TableCell>
                      <FormField
                        control={form.control}
                        name="ks2Reading"
                        render={({ field }) => (
                          <Input className="h-8 border-none bg-transparent" {...field} />
                        )}
                      />
                    </TableCell>
                    <TableCell>
                      <FormField
                        control={form.control}
                        name="ks2Maths"
                        render={({ field }) => (
                          <Input className="h-8 border-none bg-transparent" {...field} />
                        )}
                      />
                    </TableCell>
                    <TableCell>
                      <FormField
                        control={form.control}
                        name="ks2Science"
                        render={({ field }) => (
                          <Input className="h-8 border-none bg-transparent" {...field} />
                        )}
                      />
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="text-left font-medium">Key Stage 3</TableCell>
                    <TableCell>
                      <FormField
                        control={form.control}
                        name="ks3English"
                        render={({ field }) => (
                          <Input className="h-8 border-none bg-transparent" {...field} />
                        )}
                      />
                    </TableCell>
                    <TableCell>
                      <FormField
                        control={form.control}
                        name="ks3Maths"
                        render={({ field }) => (
                          <Input className="h-8 border-none bg-transparent" {...field} />
                        )}
                      />
                    </TableCell>
                    <TableCell>
                      <FormField
                        control={form.control}
                        name="ks3Science"
                        render={({ field }) => (
                          <Input className="h-8 border-none bg-transparent" {...field} />
                        )}
                      />
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>

              <Table className="border text-center text-sm">
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-left">Area</TableHead>
                    <TableHead className="text-center">Below Average</TableHead>
                    <TableHead className="text-center">Average</TableHead>
                    <TableHead className="text-center">Above Average</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[
                    { key: "speakingListeningRating", label: "Speaking and listening" },
                    { key: "readingAccuracyRating", label: "Reading accuracy" },
                    { key: "humanitiesRating", label: "Humanities" },
                    { key: "readingComprehensionRating", label: "Reading comprehension" },
                    { key: "peRating", label: "PE" },
                    { key: "writingRating", label: "Writing" },
                    { key: "artRating", label: "Art" },
                    { key: "spellingRating", label: "Spelling" },
                    { key: "dtRating", label: "DT" },
                    { key: "mathsRating", label: "Maths" },
                    { key: "ictRating", label: "ICT" },
                    { key: "scienceRating", label: "Science" },
                    { key: "otherRating", label: "Other" },
                  ].map(({ key, label }) => (
                    <FormField key={key} control={form.control} name={key as any} render={({ field }) => (
                      <TableRow>
                        <TableCell className="text-left font-medium">{label}</TableCell>
                        {(["below_average", "average", "above_average"] as const).map(opt => (
                          <TableCell key={opt} className="text-center">
                            <Checkbox
                              checked={field.value === opt}
                              onCheckedChange={() => field.onChange(opt)}
                            />
                          </TableCell>
                        ))}
                      </TableRow>
                    )} />
                  ))}
                </TableBody>
              </Table>

              <FormField control={form.control} name="phonicsTestPassed" render={({ field }) => (
                <FormItem>
                  <FormLabel>Did the child pass the Phonics Test?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />

              {form.watch("phonicsTestPassed") === "Y" && (
                <div className="space-y-4">
                  <FormField control={form.control} name="phonicsTestEndOfYearOne" render={({ field }) => (
                    <FormItem>
                      <FormLabel>If yes was that at the end of year 1 or year 2?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                          <FormItem className="flex items-center space-x-2">
                            <FormControl>
                              <RadioGroupItem value="Y" />
                            </FormControl>
                            <FormLabel className="font-normal">Year 1</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2">
                            <FormControl>
                              <RadioGroupItem value="N" />
                            </FormControl>
                            <FormLabel className="font-normal">Year 2</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />

                  {form.watch("phonicsTestEndOfYearOne") === "N" && (
                    <FormField control={form.control} name="phonicsTestYear" render={({ field }) => (
                      <FormItem>
                        <FormLabel>If later, please provide the year group</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  )}
                </div>
              )}

              <FormField
                control={form.control}
                name="recentAssessmentsDetails"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="Please detail any recent assessments including test names, dates and results:"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        </div>
      );
    case 4:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <p className="font-medium">Does the child have any difficulty with</p>
              <div className="space-y-4">
                <FormField control={form.control} name="writtenWorkDifficulty" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Planning and organising written work?</FormLabel>
                    <FormControl>
                      <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="Y" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="N" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="startingWorkDifficulty" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Getting started with written work?</FormLabel>
                    <FormControl>
                      <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="Y" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="N" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="copyingDifficulty" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Copying from the board?</FormLabel>
                    <FormControl>
                      <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="Y" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="N" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={form.control} name="rememberingInstructionsDifficulty" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Remembering instructions?</FormLabel>
                    <FormControl>
                      <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="Y" />
                          </FormControl>
                          <FormLabel className="font-normal">Yes</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <RadioGroupItem value="N" />
                          </FormControl>
                          <FormLabel className="font-normal">No</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>
              <Separator className="my-4" />
              <FormField control={form.control} name="verbalWrittenDiscrepancy" render={({ field }) => (
                <FormItem>
                  <FormLabel>Is there a discrepancy between the child’s verbal ability and written work?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
        </div>
      );
    case 5:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="senMonitored" render={({ field }) => (
                <FormItem>
                  <FormLabel>Is this child being monitored for Special Educational Needs?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="iepOrPlp" render={({ field }) => (
                <FormItem>
                  <FormLabel>Is there an individual Education Plan (IEP)/Personalised Learning Plan (PLP)?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="currentSupportDetails" render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea placeholder="Please detail any current support/provision this child is receiving" className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="supportProvider" render={({ field }) => (
                <FormItem>
                  <FormLabel>Who gives this support (role in school)?</FormLabel>
                  <FormControl>
                    <Textarea className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="supportType" render={({ field }) => (
                <FormItem>
                  <FormLabel>What type of support?</FormLabel>
                  <FormControl>
                    <Textarea className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="sessionLength" render={({ field }) => (
                <FormItem>
                  <FormLabel>Length of session(s)?</FormLabel>
                  <FormControl>
                    <Textarea className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="sessionFrequency" render={({ field }) => (
                <FormItem>
                  <FormLabel>Frequency of support (times per week)?</FormLabel>
                  <FormControl>
                    <Textarea className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
        </div>
      );
    case 6:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="externalAgencyInvolved" render={({ field }) => (
                <FormItem>
                  <FormLabel>Has this child been discussed/assessed/monitored by any external agencies, e.g. Educational Psychologist, Behaviour support, Learning support etc.</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              {form.watch("externalAgencyInvolved") === "Y" && (
                <FormField control={form.control} name="externalAgencyDetails" render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea placeholder="If Yes, please give details" className="min-h-[80px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              )}
              <FormField control={form.control} name="additionalComments" render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea placeholder="If the child has an Education Health and Care Plan, please share a copy of the most recent Annual Review or other relevant information." className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
        </div>
      );
    case 7:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="literacyDetails" render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea placeholder="Please describe the child’s current strengths and difficulties with Literacy" className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
        </div>
      );
    case 8:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="numeracyDetails" render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea placeholder="Please describe the child’s current strengths and difficulties with Numeracy" className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
        </div>
      );
    case 9:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="memoryAttentionDifficulty" render={({ field }) => (
                <FormItem>
                  <FormLabel>Does the child have difficulties with memory, attention and concentration?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              {form.watch("memoryAttentionDifficulty") === "Y" && (
                <FormField control={form.control} name="memoryAttentionDetails" render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea placeholder="If yes, please provide further details" className="min-h-[80px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              )}
              <FormField control={form.control} name="attitudeToWork" render={({ field }) => (
                <FormItem>
                  <FormLabel>Attitude to work – please tick/highlight all that apply:</FormLabel>
                  <FormControl>
                    <Table className="border text-sm">
                      <TableBody>
                        {attitudeOptions.map(opt => (
                          <TableRow key={opt.value}>
                            <TableCell className="text-left">{opt.label}</TableCell>
                            <TableCell className="text-center">
                              <Checkbox
                                checked={Array.isArray(field.value) ? field.value.includes(opt.value) : false}
                                onCheckedChange={(checked) => {
                                  let current: string[] = Array.isArray(field.value) ? field.value : [];
                                  if (checked) {
                                    current = [...current, opt.value];
                                  } else {
                                    current = current.filter(v => v !== opt.value);
                                  }
                                  field.onChange(current);
                                }}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
        </div>
      );
    case 10:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="speechLanguageCommunicationDifficulty" render={({ field }) => (
                <FormItem>
                  <FormLabel>Are there any current difficulties with speech, oral language or communication?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              {form.watch("speechLanguageCommunicationDifficulty") === "Y" && (
                <FormField control={form.control} name="speechLanguageCommunicationDetails" render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea placeholder="If yes, please provide further details" className="min-h-[80px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              )}

              <FormField control={form.control} name="socialSkillsBehaviourPeerRelationshipsDifficulty" render={({ field }) => (
                <FormItem>
                  <FormLabel>Does the child have difficulties with social skills, behaviour, peer relationships or emotional adjustment?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              {form.watch("socialSkillsBehaviourPeerRelationshipsDifficulty") === "Y" && (
                <FormField control={form.control} name="socialSkillsBehaviourPeerRelationshipsDetails" render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea placeholder="If yes, please provide further details" className="min-h-[80px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              )}

              <FormField control={form.control} name="selfEsteemConfidenceDifficulty" render={({ field }) => (
                <FormItem>
                  <FormLabel>Does the child have difficulties with self-esteem and confidence?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              {form.watch("selfEsteemConfidenceDifficulty") === "Y" && (
                <FormField control={form.control} name="selfEsteemConfidenceDetails" render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea placeholder="If yes, please provide further details" className="min-h-[80px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              )}

              <FormField control={form.control} name="peerRelationships" render={({ field }) => (
                <FormItem>
                  <FormLabel>Peer relationships – please tick/highlight all that apply:</FormLabel>
                  <FormControl>
                    <Table className="border text-sm">
                      <TableBody>
                        {peerRelationshipOptions.map((pair, idx) => (
                          <TableRow key={idx}>
                            {pair.map(opt => (
                              <React.Fragment key={opt.value}>
                                <TableCell className="text-left">{opt.label}</TableCell>
                                <TableCell className="text-center">
                                  <Checkbox
                                    checked={Array.isArray(field.value) ? field.value.includes(opt.value) : false}
                                    onCheckedChange={(checked) => {
                                      let current: string[] = Array.isArray(field.value) ? field.value : [];
                                      if (checked) {
                                        current = [...current, opt.value];
                                      } else {
                                        current = current.filter(v => v !== opt.value);
                                      }
                                      field.onChange(current);
                                    }}
                                  />
                                </TableCell>
                              </React.Fragment>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
        </div>
      );
    case 11:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="goodOrganisationalSkills" render={({ field }) => (
                <FormItem>
                  <FormLabel>Does the child have good organisational skills?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              {form.watch("goodOrganisationalSkills") === "N" && (
                <FormField control={form.control} name="goodOrganisationalSkillsDetails" render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea placeholder="If no, please provide further details" className="min-h-[80px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              )}
            </CardContent>
          </Card>
        </div>
      );
    case 12:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="fineGrossMotorSkillsDifficulty" render={({ field }) => (
                <FormItem>
                  <FormLabel>Does the child have any difficulties with fine and gross motor skills e.g. body awareness, movement and balance?</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex space-x-4">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="Y" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="N" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              {form.watch("fineGrossMotorSkillsDifficulty") === "Y" && (
                <FormField control={form.control} name="fineGrossMotorSkillsDetails" render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea placeholder="If yes, please provide further details" className="min-h-[80px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              )}
            </CardContent>
          </Card>
        </div>
      );
    case 13:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="strengths" render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea placeholder="Please provide information about the child’s strengths, what they are good at and what they enjoy doing at school" className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
        </div>
      );
    case 14:
      return (
        <div className="space-y-6">
          <Card className="border-none shadow-none">
            <CardContent className="p-0 space-y-4">
              <FormField control={form.control} name="otherInformation" render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea placeholder="Please provide any other information that would be useful for the assessor to know and what you hope to achieve from the assessment" className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </CardContent>
          </Card>
        </div>
      );
    default:
      return null;
  }
}
