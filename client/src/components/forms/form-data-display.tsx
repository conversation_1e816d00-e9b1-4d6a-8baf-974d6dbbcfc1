import React from 'react';

interface FormDataDisplayProps {
  status?: string;
  completedAt?: string | null;
  completedById?: number | null;
}

const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-GB', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

export function FormDataDisplay({ status, completedAt, completedById }: FormDataDisplayProps) {
  if (!status && !completedAt && !completedById) {
    return null;
  }

  return (
    <div className="text-sm text-muted-foreground mb-6 space-y-1">
      {status && <p>Status: {status.replace('_', ' ')}</p>}
      {completedAt && <p>Completed at: {formatDate(completedAt)}</p>}
      {completedById && <p>Completed by user ID: {completedById}</p>}
    </div>
  );
}

export default FormDataDisplay;
