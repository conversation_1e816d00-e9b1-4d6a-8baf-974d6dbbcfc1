import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { StableInput } from "@/components/forms/stable-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { COUNTRIES } from "@/constants/countries";
import { StableTextarea } from "@/components/forms/stable-textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { ParentFormValues } from "./parent-under-16-form";

export function ChildDetailsSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="p-0 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="childFullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Child's Full Name</FormLabel>
                  <FormControl>
                    <StableInput placeholder="Full name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="childPreferredName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Preferred Name</FormLabel>
                  <FormControl>
                    <StableInput placeholder="Preferred name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="childDateOfBirth"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date of Birth</FormLabel>
                  <FormControl>
                    <StableInput type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="childAge"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Age</FormLabel>
                  <FormControl>
                    <StableInput
                      type="text"
                      placeholder="Child's current age"
                      {...field}
                      readOnly
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="childSchoolYear"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>School Year</FormLabel>
                  <FormControl>
                    <StableInput
                      type="text"
                      placeholder="e.g., Year 5"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="childCountryOfBirth"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country of Birth</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select country" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {COUNTRIES.map((c) => (
                        <SelectItem key={c} value={c}>
                          {c}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="childDateMovedToUK"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date Moved to UK (if applicable)</FormLabel>
                  <FormControl>
                    <StableInput type="date" {...field} />
                  </FormControl>
                  <FormDescription>
                    To be assessed they need to have continuously lived in an
                    English-speaking country and to have been regularly
                    speaking English for a minimum of 7 years.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="childAdopted"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Is the child adopted?</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-row space-x-4"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="prefer_not_to_say" />
                        </FormControl>
                        <FormLabel className="font-normal">Prefer not to say</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="childGender"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>How does the child identify themselves?</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="male" />
                        </FormControl>
                        <FormLabel className="font-normal">Male</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="female" />
                        </FormControl>
                        <FormLabel className="font-normal">Female</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="neutral" />
                        </FormControl>
                        <FormLabel className="font-normal">Gender neutral</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="prefer_not_to_say" />
                        </FormControl>
                        <FormLabel className="font-normal">Prefer not to say</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="childPreferredPronoun"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    Which pronoun would you like us to use in the report?
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="he" />
                        </FormControl>
                        <FormLabel className="font-normal">He/Him</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="she" />
                        </FormControl>
                        <FormLabel className="font-normal">She/Her</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="they" />
                        </FormControl>
                        <FormLabel className="font-normal">They/Them</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="other" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Other (please state)
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("childPreferredPronoun") === "other" && (
              <FormField
                control={form.control}
                name="childPreferredPronounOther"
                render={({ field }) => (
                  <FormItem className="md:col-start-2">
                    <FormLabel>Please specify</FormLabel>
                    <FormControl>
                      <StableInput
                        placeholder="Enter preferred pronoun"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
          )}
          />
        )}
      </div>


        </CardContent>
      </Card>
    </div>
  );
}

export function ParentGuardianSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="p-0 space-y-4">
            <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="requestingParentTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-row space-x-4"
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="mr" />
                            </FormControl>
                            <FormLabel className="font-normal">Mr</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="mrs" />
                            </FormControl>
                            <FormLabel className="font-normal">Mrs</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="miss" />
                            </FormControl>
                            <FormLabel className="font-normal">Miss</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="other" />
                            </FormControl>
                            <FormLabel className="font-normal">Other title</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="requestingParentName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name of parent/guardian requesting the assessment</FormLabel>
                      <FormControl>
                        <StableInput placeholder="Full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              {form.watch("requestingParentTitle") === "other" && (
                <FormField
                  control={form.control}
                  name="requestingParentTitleOther"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Please specify title</FormLabel>
                      <FormControl>
                        <StableInput placeholder="Enter title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              <FormField
                control={form.control}
                name="childContactAddress"
                render={({ field }) => (
                  <FormItem>
                      <FormLabel>Full Address (including postcode)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter full address"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="childContactPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone (Mobile)</FormLabel>
                        <FormControl>
                          <StableInput
                            placeholder="Enter mobile number"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="childContactPhoneWork"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone (Work)</FormLabel>
                        <FormControl>
                          <StableInput
                            placeholder="Enter work phone"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="childContactEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <StableInput
                            type="email"
                            placeholder="Enter email address"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
            </div>

          <FormField
            control={form.control}
            name="childHearAbout"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>How did you hear about NeuroElevate?</FormLabel>
                <FormControl>
                  <div className="flex flex-col space-y-2">
                    {[
                      { label: "Facebook", value: "facebook" },
                      { label: "Google Search", value: "google_search" },
                      { label: "Advert", value: "advert" },
                      { label: "Recommendation", value: "recommendation" },
                      { label: "Other", value: "other" },
                    ].map((opt) => (
                      <label
                        key={opt.value}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          checked={
                            Array.isArray(field.value)
                              ? field.value.includes(opt.value)
                              : false
                          }
                          onCheckedChange={(checked) => {
                            let current: string[] = Array.isArray(field.value)
                              ? field.value
                              : [];
                            if (checked) {
                              current = [...current, opt.value];
                            } else {
                              current = current.filter((v) => v !== opt.value);
                            }
                            field.onChange(current);
                          }}
                        />
                        <span className="font-normal">{opt.label}</span>
                      </label>
                    ))}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {Array.isArray(form.watch("childHearAbout")) &&
            form.watch("childHearAbout").includes("other") && (
              <FormField
                control={form.control}
                name="childHearAboutOther"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Please specify</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Please specify" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

          <FormField
            control={form.control}
            name="legalResponsibilityAgreement"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>
                  Do all those with legal responsibility for the child agree to
                  this assessment?
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-row space-x-4"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="yes" />
                      </FormControl>
                      <FormLabel className="font-normal">Yes</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="no" />
                      </FormControl>
                      <FormLabel className="font-normal">No</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export function ReasonsForReferralSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="p-0 space-y-4">
          <FormField
            control={form.control}
            name="referralConcerns"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea
                    placeholder="What are your concerns about your child?"
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="childViews"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea
                    placeholder="What views has your child expressed?"
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
}

function DevelopmentalHistorySection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="birthPregnancyDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Did you experience any difficulties encountered during the pregnancy and birth of your child?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("birthPregnancyDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="birthPregnancyDifficultiesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="developmentalMilestonesOnTime"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Were all the normal developmental milestones reached on time, such as walking, talking, riding a bike?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("developmentalMilestonesOnTime") === "no" && (
        <FormField
          control={form.control}
          name="developmentalMilestonesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If no, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="seenOtherSpecialists"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Has your child ever had any Speech and Language difficulties?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("seenOtherSpecialists") === "yes" && (
        <FormField
          control={form.control}
          name="seenOtherSpecialistsDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="seriousIllnesses"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Is there a history of ear infections glue ear or grommets?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("seriousIllnesses") === "yes" && (
        <FormField
          control={form.control}
          name="seriousIllnessesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="currentMedication"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>ls your child’s hearing currently within normal limits?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("currentMedication") === "yes" && (
        <FormField
          control={form.control}
          name="currentMedicationDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="hearingHistoricalIssues"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>ls your child on any regular medication that may be relevant?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("hearingHistoricalIssues") === "yes" && (
        <FormField
          control={form.control}
          name="hearingHistoricalIssuesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="IPlease give details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function VisionDifficultiesSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <div className="bg-red-100 p-4 rounded-md text-sm">
        In order to proceed with the assessment, the child MUST have had a sight
        test within the last 2 years. In some cases, difficulties with reading
        are caused by visual difficulties that are not related to learning.
        Therefore, if having answered the questions below, you suspect there are
        visual difficulties* you MUST discuss this at the eye test so that the
        Optician (Optometrist) carrying out the eye test, can refer your child
        to an Ophthalmologist for further investigation, prior to the
        assessment.
      </div>

      <FormField
        control={form.control}
        name="visualHistory"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Has your child had any history of visual difficulties / problems
              with sight / visual impairment?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("visualHistory") === "yes" && (
        <FormField
          control={form.control}
          name="visualHistoryDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, provide further details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="lastSightTestDate"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              What date did your child last have a sight test by an optometrist?
            </FormLabel>
            <FormControl>
              <StableInput type="date" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="prescriptionMade"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Was any prescription made?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("prescriptionMade") === "yes" && (
        <FormField
          control={form.control}
          name="prescriptionDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide details"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="wearPrescribedGlasses"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Does your child wear the prescribed glasses/contact lenses?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="usedColouredOverlays"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Has your child ever used coloured overlays or colour-tinted glasses?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("usedColouredOverlays") === "yes" && (
        <FormField
          control={form.control}
          name="usedColouredOverlaysDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder={`If yes please provide the following information:\nWho advised and provided them?\n\nWhy were they recommended?\n\nDid they help? If yes, in what way?\n\nDo you still use them? If not, why not?`}
                  className="min-h-[150px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="screenTimeHours"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Approximately how many hours per working/study day does your child
              spend at a screen?
            </FormLabel>
            <FormControl>
              <StableInput {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="readingHours"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Approximately how many additional hours per day does your child
              spend reading printed texts?
            </FormLabel>
            <FormControl>
              <StableInput {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="increasedScreenTime"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Has your child's screen or reading time increased recently? If so,
              by how much?
            </FormLabel>
            <FormControl>
              <StableInput {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Visual difficulties questionnaire */}
      <div className="space-y-2">
        <p className="text-sm font-medium">
          Response categories: Always = every day&nbsp;&nbsp; Often = several
          times a week&nbsp;&nbsp; Sometimes = 2-3 times a month&nbsp;&nbsp;
          Rarely = only once every few months / a year
        </p>
        <p className="text-sm italic">
          *Visual difficulties should be investigated if you answered ‘always’ or
          ‘sometimes’ to several questions.
        </p>
        <Table className="border text-center text-sm">
          <TableHeader>
            <TableRow>
              <TableHead className="text-left font-semibold text-foreground">
                Section for parents/ carers
              </TableHead>
              <TableHead className="text-center font-semibold text-foreground">
                Never
              </TableHead>
              <TableHead className="text-center font-semibold text-foreground">
                Rarely
              </TableHead>
              <TableHead className="text-center font-semibold text-foreground">
                Sometimes
              </TableHead>
              <TableHead className="text-center font-semibold text-foreground">
                Often
              </TableHead>
              <TableHead className="text-center font-semibold text-foreground">
                Always
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[
              {
                key: "visionQ1",
                q: "Does your child get headaches when they are reading?"
              },
              {
                key: "visionQ2",
                q: "Does your child report that reading makes their eyes feel sore, gritty or watery?"
              },
              {
                key: "visionQ3",
                q: "Does your child report feeling tired or sleepy during or after reading?"
              },
              {
                key: "visionQ4",
                q: "Have you noticed your child become restless or fidgety or distracted when reading?"
              },
              {
                key: "visionQ5",
                q: "Have you noticed your child rubbing their eyes when they are reading?"
              },
              {
                key: "visionQ6",
                q: "Have you noticed your child screwing up their eyes when reading?"
              },
              {
                key: "visionQ7",
                q: "Have you noticed your child tilting their head to one side when reading?"
              },
              {
                key: "visionQ8",
                q: "Have you noticed your child moving their eyes around or blinking frequently when they are reading?"
              },
              {
                key: "visionQ9",
                q: "Have you noticed your child holding a paper or book very close to their eyes when reading?"
              },
              {
                key: "visionQ10",
                q: "How often does your child use a marker or their finger to keep their place when reading?"
              },
              {
                key: "visionQ11",
                q: "Have you noticed that your child frequently loses their place when reading?"
              },
              {
                key: "visionQ12",
                q: "Have you noticed your child covering or closing one eye when reading?"
              },
            ].map(({ key, q }, index) => (
              <FormField key={key} control={form.control} name={key} render={({ field }) => (
                <TableRow>
                  <TableCell className="text-left">{index + 1}. {q}</TableCell>
                  {["never", "rarely", "sometimes", "often", "always"].map(opt => (
                    <TableCell key={opt}>
                      <Checkbox
                        checked={field.value === opt}
                        onCheckedChange={() => field.onChange(opt)}
                      />
                    </TableCell>
                  ))}
                </TableRow>
              )} />
            ))}

            <TableRow>
              <TableCell colSpan={6} className="text-left font-semibold">
                Section for child
              </TableCell>
            </TableRow>

            {[
              { key: "visionQ13", q: "When you read, do you see two of each word?" },
              { key: "visionQ14", q: "When you read, do the words you read look blurry (or fuzzy, or unclear)?" },
              { key: "visionQ15", q: "When you are reading, do the words move on the page?" },
              { key: "visionQ16", q: "When your teachers ask you to copy something from a screen at the front of the classroom, can you see what is written on the screen?" },
            ].map(({ key, q }, index) => (
              <FormField key={key} control={form.control} name={key} render={({ field }) => (
                <TableRow>
                  <TableCell className="text-left">{12 + index + 1}. {q}</TableCell>
                  {["never", "rarely", "sometimes", "often", "always"].map(opt => (
                    <TableCell key={opt}>
                      <Checkbox
                        checked={field.value === opt}
                        onCheckedChange={() => field.onChange(opt)}
                      />
                    </TableCell>
                  ))}
                </TableRow>
              )} />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export function LanguageHistorySection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <p className="text-muted-foreground mb-4">
        To be assessed your child will need to have continuously lived in an
        English-speaking country and to have been regularly speaking English for
        a minimum of seven years. This is because the tests used are heavily
        influenced by an English-speaking culture.
      </p>

      <FormField
        control={form.control}
        name="otherLanguagesSpoken"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Are any languages other than English spoken at home?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("otherLanguagesSpoken") === "yes" && (
        <FormField
          control={form.control}
          name="otherLanguagesSpokenDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If YES, please give details including if English is the main language. If English is the second language, are there any difficulties in their first language?"
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function FamilialHistorySection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">

      <FormField
        control={form.control}
        name="familyLearningDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Have any other family members experienced difficulties with spelling / reading / learning?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("familyLearningDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="familyLearningDifficultiesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="If yes, provide further details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function HealthAndDevelopmentalHistorySection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <DevelopmentalHistorySection />
      <FormField
        control={form.control}
        name="passedPhonicsTest"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Did your child pass the Phonics Test?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("passedPhonicsTest") === "yes" && (
        <FormField
          control={form.control}
          name="passedPhonicsTestDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, was that at the end of year one or year two?"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="schoolingDisrupted"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Has your child’s schooling been disrupted in any way other than during the Covid 19 pandemic?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("schoolingDisrupted") === "yes" && (
        <FormField
          control={form.control}
          name="schoolingDisruptedDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide more information"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="covidEducationalExperience"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              What was your child’s educational experience during the Covid 19 pandemic?
            </FormLabel>
            <FormControl>
              <StableTextarea
                placeholder={
                  "Were they educated largely at school (i.e., children of key workers) or at home?\n\nIf they were at school, how did they respond to smaller classes? If they were working online, how did they get on with this?"
                }
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="teachersDiscussedDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Have any of your child’s teachers discussed any difficulties your child is experiencing?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("teachersDiscussedDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="teachersDiscussedDifficultiesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide more information"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="childSeenOtherSpecialists"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Has your child seen any other specialists (e.g. Educational Psychologist, Advisory teacher etc)?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("childSeenOtherSpecialists") === "yes" && (
        <FormField
          control={form.control}
          name="childSeenOtherSpecialistsDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide copies of reports."
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="receivedSupportIntervention"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Has your child received any support or intervention in the past?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("receivedSupportIntervention") === "yes" && (
        <FormField
          control={form.control}
          name="receivedSupportInterventionDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please provide more information, such as the nature or name of the programme"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <div>
        <p className="font-semibold mb-2">
          Has your child received support for any of the following (please tick as appropriate)
        </p>
        <Table className="border text-center text-sm">
          <TableHeader>
            <TableRow>
              <TableHead className="text-left font-semibold text-foreground"></TableHead>
              <TableHead className="text-center font-semibold text-foreground">1:1 in school</TableHead>
              <TableHead className="text-center font-semibold text-foreground">Private Tutor</TableHead>
              <TableHead className="text-center font-semibold text-foreground">Small Group</TableHead>
              <TableHead className="text-center font-semibold text-foreground">Whole Class</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[{ key: "supportReading", label: "Reading" }, { key: "supportMaths", label: "Maths" }, { key: "supportSpelling", label: "Spelling" }, { key: "supportWriting", label: "Writing" }].map(({ key, label }) => (
              <FormField
                key={key}
                control={form.control}
                name={key as keyof ParentFormValues}
                render={({ field }) => (
                  <TableRow>
                    <TableCell className="text-left">{label}</TableCell>
                    {["1:1 in school", "Private Tutor", "Small Group", "Whole Class"].map((opt) => (
                      <TableCell key={opt}>
                        <Checkbox
                          checked={Array.isArray(field.value) ? field.value.includes(opt) : false}
                          onCheckedChange={(checked) => {
                            let current: string[] = Array.isArray(field.value) ? field.value : [];
                            if (checked) {
                              current = [...current, opt];
                            } else {
                              current = current.filter((v) => v !== opt);
                            }
                            field.onChange(current);
                          }}
                        />
                      </TableCell>
                    ))}
                  </TableRow>
                )}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export function CurrentSituationSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <div>
        <p className="font-semibold mb-2">
          Current National Curriculum Levels (if appropriate)
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="currentLevelEnglish"
            render={({ field }) => (
              <FormItem>
                <FormLabel>English</FormLabel>
                <FormControl>
                  <StableInput type="text" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="currentLevelMaths"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Maths</FormLabel>
                <FormControl>
                  <StableInput type="text" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <div>
        <p className="font-semibold mb-2">
          Did you have any difficulties at school with any of the following?
        </p>
        <Table className="border text-center text-sm">
          <TableHeader>
            <TableRow>
              <TableHead className="text-left font-semibold text-foreground"></TableHead>
              <TableHead className="text-center font-semibold text-foreground">Slight</TableHead>
              <TableHead className="text-center font-semibold text-foreground">Moderate</TableHead>
              <TableHead className="text-center font-semibold text-foreground">Severe</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[
              { key: "difficultyReadingSeverity", label: "Reading" },
              { key: "difficultySpellingSeverity", label: "Spelling" },
              { key: "difficultyWritingSeverity", label: "Writing" },
              { key: "difficultyMathsSeverity", label: "Mathematics" },
              { key: "difficultySportsGamesSeverity", label: "Sports & Games" },
            ].map(({ key, label }) => (
              <FormField
                key={key}
                control={form.control}
                name={key as keyof ParentFormValues}
                render={({ field }) => (
                  <TableRow>
                    <TableCell className="text-left">{label}</TableCell>
                    {["Slight", "Moderate", "Severe"].map((opt) => (
                      <TableCell key={opt}>
                        <Checkbox
                          checked={field.value === opt}
                          onCheckedChange={() => field.onChange(opt)}
                        />
                      </TableCell>
                    ))}
                  </TableRow>
                )}
              />
            ))}
          </TableBody>
        </Table>
      </div>

      <FormField
        control={form.control}
        name="specialistHelpAtSchool"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Is there any specialist help currently given at school?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("specialistHelpAtSchool") === "yes" && (
        <FormField
          control={form.control}
          name="specialistHelpAtSchoolDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder={
                    "If yes, please give details, (e.g., Teaching Assistant support, extra time in exams, EHCP, specialist tuition)\n\nPlease share any relevant Individual Support Plan/Individual Education Plan/Education Health and Care Plan/Speech and Language Report/Occupational Therapy Report if available."
                  }
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="tuitionOutsideSchool"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Is your child currently receiving any tuition outside of school?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("tuitionOutsideSchool") === "yes" && (
        <FormField
          control={form.control}
          name="tuitionOutsideSchoolDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please give details of the support being received and how often"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function LiteracySection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="literacyOverview"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <StableTextarea
                placeholder="Please describe your child’s current strengths and difficulties with reading, writing and spelling"
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="difficultyRecallingAlphabet"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Does your child have difficulty recalling the alphabet or other known sequences?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("difficultyRecallingAlphabet") === "yes" && (
        <FormField
          control={form.control}
          name="difficultyRecallingAlphabetDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please give details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function NumeracySection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="numeracyOverview"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <StableTextarea
                placeholder="Please describe your child’s current strengths and difficulties with Numeracy"
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="difficultyTellingTime"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Does your child have difficulty telling the time?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("difficultyTellingTime") === "yes" && (
        <FormField
          control={form.control}
          name="difficultyTellingTimeDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please give details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function MemoryAttentionSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="memoryAttentionConcentrationDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Does your child have difficulties with memory, attention or concentration?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("memoryAttentionConcentrationDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="memoryAttentionConcentrationDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please give details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function SpeechLanguageCommunicationSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="speechLanguageCommunicationDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Are there any current difficulties with speech, oral language or communication?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("speechLanguageCommunicationDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="speechLanguageCommunicationDifficultiesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please give details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="socialSkillsBehaviourPeerRelationshipsDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Does your child have difficulties with social skills, behaviour, peer relationships or emotional adjustment?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("socialSkillsBehaviourPeerRelationshipsDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="socialSkillsBehaviourPeerRelationshipsDifficultiesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please give details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="selfEsteemConfidenceDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Does your child have difficulties with self-esteem and confidence?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("selfEsteemConfidenceDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="selfEsteemConfidenceDifficultiesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please give details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function OrganisationalSkillsSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="goodOrganisationalSkills"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Does your child have good organisational skills?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("goodOrganisationalSkills") === "no" && (
        <FormField
          control={form.control}
          name="goodOrganisationalSkillsDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If no, please give details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function MotorSkillsSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="fineGrossMotorSkillsDifficulties"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>
              Does your child have any difficulties with fine and gross motor skills e.g., body awareness, movement and balance?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("fineGrossMotorSkillsDifficulties") === "yes" && (
        <FormField
          control={form.control}
          name="fineGrossMotorSkillsDifficultiesDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please give details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="leftRightConfusion"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <FormLabel>Does your child experience left/right confusion?</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-row space-x-4"
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {form.watch("leftRightConfusion") === "yes" && (
        <FormField
          control={form.control}
          name="leftRightConfusionDetails"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <StableTextarea
                  placeholder="If yes, please give details"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function StrengthsSection() {
  const form = useFormContext<ParentFormValues>();
  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="childStrengths"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <StableTextarea
                placeholder="Please provide information about your child’s strengths, what they are good at, hobbies they enjoy etc"
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

