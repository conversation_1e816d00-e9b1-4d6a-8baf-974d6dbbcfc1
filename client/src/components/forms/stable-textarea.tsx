import * as React from "react";
import { cn } from "@/lib/utils";

export interface StableTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

const StableTextarea = React.forwardRef<HTMLTextAreaElement, StableTextareaProps>(
  ({ className, value, onChange, ...props }, ref) => (
    <textarea
      className={cn(
        "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      ref={ref}
      value={value}
      onChange={onChange}
      {...props}
    />
  )
);

StableTextarea.displayName = "StableTextarea";

export { StableTextarea };

