import React, { useEffect, useState } from "react";
import { useToast } from "@/hooks/use-toast";

declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (
        siteKey: string,
        options: { action: string },
      ) => Promise<string>;
    };
  }
}

interface ReCaptchaV3Props {
  onChange: (token: string | null) => void;
  onExpired?: () => void;
  action?: string;
}

export default function ReCaptchaV3({ onChange, onExpired, action = "submit" }: ReCaptchaV3Props) {
  const [isReady, setIsReady] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;
    console.log("🔹 reCAPTCHA v3: siteKey exists =", !!siteKey);
    
    if (!siteKey) {
      console.error('reCAPTCHA v3 site key is not configured');
      setIsReady(false);
      return;
    }

    const loadRecaptcha = () => {
      // Load reCAPTCHA v3
      const script = document.createElement('script');
      script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        console.log('reCAPTCHA v3 script loaded successfully');
        
        if (window.grecaptcha && window.grecaptcha.ready) {
          window.grecaptcha.ready(() => {
            console.log('reCAPTCHA v3 is ready');
            setIsReady(true);
          });
        }
      };
      
      script.onerror = () => {
        console.error('reCAPTCHA v3 failed to load');
        setIsReady(false);
        toast({
          title: "reCAPTCHA Error",
          description: "Failed to load reCAPTCHA verification. Please refresh the page.",
          variant: "destructive"
        });
      };
      
      document.head.appendChild(script);
    };

    // Check if script is already loaded
    if (window.grecaptcha && window.grecaptcha.ready) {
      window.grecaptcha.ready(() => {
        console.log('reCAPTCHA v3 already loaded and ready');
        setIsReady(true);
      });
    } else {
      loadRecaptcha();
    }

    return () => {
      // Cleanup if needed
    };
  }, [toast]);

  // Auto-execute when ready
  useEffect(() => {
    if (isReady) {
      executeRecaptcha();
    }
  }, [isReady]);

  const executeRecaptcha = async () => {
    const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;
    
    if (!window.grecaptcha || !siteKey) {
      console.error('reCAPTCHA v3 not ready or site key missing');
      onChange(null);
      return;
    }

    try {
      const token = await window.grecaptcha.execute(siteKey, { action });
      console.log('✅ reCAPTCHA v3 token generated:', token.substring(0, 20) + '...');
      onChange(token);
    } catch (error) {
      console.error('reCAPTCHA v3 execution failed:', error);
      onChange(null);
      
      if (onExpired) {
        onExpired();
      }
    }
  };

  // Refresh token periodically (reCAPTCHA v3 tokens expire after 2 minutes)
  useEffect(() => {
    if (!isReady) return;

    const interval = setInterval(() => {
      console.log('🔄 Refreshing reCAPTCHA v3 token');
      executeRecaptcha();
    }, 110000); // Refresh every 110 seconds (just under 2 minutes)

    return () => clearInterval(interval);
  }, [isReady, action]);

  // This component doesn't render anything visible (invisible reCAPTCHA v3)
  return null;
}