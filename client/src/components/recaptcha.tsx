import React, { useEffect, useState, useRef } from "react";
import { useToast } from "@/hooks/use-toast";

declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (
        siteKey: string,
        options: { action: string },
      ) => Promise<string>;
    };
    recaptchaLoaded?: () => void;
  }
}

interface ReCaptchaProps {
  onChange: (token: string | null) => void;
  onExpired?: () => void;
  size?: "normal" | "compact";
}

export default function RecaptchaComponent({
  onChange,
  onExpired,
  size = "normal",
}: ReCaptchaProps) {
  const { toast } = useToast();
  const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;
  const [isLoading, setIsLoading] = useState(false);
  const [isV3Loaded, setIsV3Loaded] = useState(false);
  const [useAlternate, setUseAlternate] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const executionInProgress = useRef(false);

  // Load debug logs on first render only to avoid excessive logging
  useEffect(() => {
    console.log("🔹 recaptcha: siteKey exists =", !!siteKey);
    console.log("🔹 recaptcha: isV3Loaded =", isV3Loaded);
    console.log("🔹 recaptcha: useAlternate =", useAlternate);

    // Add a global unhandled rejection handler to catch any reCAPTCHA issues
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      event.preventDefault(); // Prevents the error from showing in console

      // Only log in development
      if (import.meta.env.DEV) {
        console.warn("Caught unhandled promise rejection:", event.reason);
      }

      // If this happens during reCAPTCHA execution, make sure to clear the flag
      if (executionInProgress.current) {
        executionInProgress.current = false;
      }
    };

    // Add the event listener
    window.addEventListener("unhandledrejection", handleUnhandledRejection);

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener(
        "unhandledrejection",
        handleUnhandledRejection,
      );
    };
  }, []);

  // Load reCAPTCHA script
  useEffect(() => {
    if (!siteKey || isV3Loaded) return;
    setIsLoading(true);

    const script = document.createElement("script");
    script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      console.log("reCAPTCHA script loaded successfully");
      setTimeout(() => {
        const grecaptchaObj = window.grecaptcha;
        if (grecaptchaObj) {
          grecaptchaObj.ready(() => {
            console.log("reCAPTCHA is ready");
            setIsV3Loaded(true);
            setIsLoading(false);
          });
        } else {
          console.error("grecaptcha not available after script loaded");
          setUseAlternate(true);
          setIsLoading(false);
          setErrorMessage("reCAPTCHA not initialized correctly");
        }
      }, 1000);
    };

    script.onerror = (error) => {
      console.error("Error loading reCAPTCHA script:", error);
      setIsLoading(false);
      setUseAlternate(true);
      setErrorMessage("Failed to load reCAPTCHA script");
    };

    document.head.appendChild(script);

    const timeoutId = setTimeout(() => {
      if (!isV3Loaded) {
        console.log("reCAPTCHA load timeout, switching to manual verification");
        setUseAlternate(true);
        setIsLoading(false);
        setErrorMessage("reCAPTCHA failed to load within timeout period");
      }
    }, 5000);

    return () => {
      clearTimeout(timeoutId);
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [siteKey, isV3Loaded]);

  if (!siteKey) {
    return (
      <div className="border rounded p-3 text-sm text-muted-foreground bg-muted/50">
        <p>
          reCAPTCHA not configured. VITE_RECAPTCHA_SITE_KEY environment variable
          is missing.
        </p>
        <p className="mt-1 text-xs">
          Contact administrator to configure anti-spam protection.
        </p>
      </div>
    );
  }

  // Helper function to safely execute reCAPTCHA
  const safeExecuteReCaptcha = useRef<
    (callback: (token: string) => void) => boolean
  >(() => {
    // Initial implementation returns false (unsuccessful)
    return false;
  });

  // Set up our safe execution function - we keep this in a ref so it maintains identity across renders
  useEffect(() => {
    // This implementation will be used after reCAPTCHA is loaded
    safeExecuteReCaptcha.current = (
      callback: (token: string) => void,
    ): boolean => {
      const grecaptchaObj = window.grecaptcha;
      if (
        !grecaptchaObj ||
        !siteKey ||
        useAlternate ||
        !isV3Loaded ||
        executionInProgress.current
      ) {
        return false;
      }

      try {
        // Mark execution in progress
        executionInProgress.current = true;

        // Execute safely within grecaptcha.ready
        grecaptchaObj.ready(function () {
          try {
            // First wrap in a Promise.resolve to ensure proper error handling
            Promise.resolve()
              .then(() => {
                // Then try to execute reCAPTCHA inside this promise chain
                try {
                  return grecaptchaObj.execute(siteKey, {
                    action: "submit",
                  });
                } catch (execError) {
                  console.error("Error executing reCAPTCHA:", execError);
                  executionInProgress.current = false;
                  setUseAlternate(true);
                  setErrorMessage("Error executing reCAPTCHA");
                  return Promise.reject(execError);
                }
              })
              .then(function (token) {
                if (!token) {
                  throw new Error("Empty token received from reCAPTCHA");
                }
                console.log(
                  "🔒 reCAPTCHA token successfully generated, length:",
                  token.length,
                );
                callback(token);
                executionInProgress.current = false;
                return token; // Explicitly return for promise chain
              })
              .catch(function (error) {
                console.error("reCAPTCHA execution promise error:", error);
                executionInProgress.current = false;
                setUseAlternate(true);
                setErrorMessage("Failed to complete reCAPTCHA verification");
                // Don't rethrow, we've handled it
              });
          } catch (readyError) {
            console.error("Error in grecaptcha.ready callback:", readyError);
            executionInProgress.current = false;
            setUseAlternate(true);
            setErrorMessage("reCAPTCHA API error");
          }
        });

        return true;
      } catch (error) {
        console.error("Error in safeExecuteReCaptcha:", error);
        executionInProgress.current = false;
        setUseAlternate(true);
        setErrorMessage("reCAPTCHA error");
        return false;
      }
    };

    return () => {
      // Reset on cleanup
      safeExecuteReCaptcha.current = () => false;
    };
  }, [siteKey, isV3Loaded, useAlternate]);

  // Second effect: execute reCAPTCHA or fall back
  useEffect(() => {
    if (useAlternate || !isV3Loaded) return;

    const grecaptchaObj = window.grecaptcha;
    grecaptchaObj.ready(() => {
      grecaptchaObj
        .execute(siteKey, { action: "submit" })
        .then((token) => {
          // Log the token
          console.log("✅ got recaptcha token:", token);

          // Keep existing callback
          onChange(token);
        })
        .catch((err) => {
          console.error("reCAPTCHA execution error:", err);
          setUseAlternate(true);
          setErrorMessage("Failed to execute reCAPTCHA");
        });
    });

    // Set up a periodic refresh
    const refreshReCaptcha = function () {
      if (useAlternate) return; // Skip if we're using the fallback method

        const grecaptchaRefresh = window.grecaptcha;
      grecaptchaRefresh.ready(() => {
        grecaptchaRefresh
          .execute(siteKey, { action: "submit" })
          .then((token) => {
            console.log("✅ reCAPTCHA refresh token:", token);
            onChange(token);
          })
          .catch((err) => {
            console.error("reCAPTCHA refresh error:", err);
            setUseAlternate(true);
            setErrorMessage("Failed to refresh reCAPTCHA");
          });
      });
    };

    // Set up the interval to call our refresh function
    const refresh = setInterval(refreshReCaptcha, 110000);

    return () => clearInterval(refresh);
  }, [isV3Loaded, useAlternate, onChange, siteKey]);

  if (useAlternate) {

    // For development or other environments, show debug info
    return (
      <div className="my-4">
        <div className="border border-yellow-300 bg-yellow-50 p-3 mb-3 rounded text-sm">
          <p className="font-medium">reCAPTCHA verification unavailable</p>
          {errorMessage && (
            <p className="text-xs text-gray-500 mt-1">Reason: {errorMessage}</p>
          )}
          <p className="text-xs mt-1">
            This domain ({window.location.hostname}) may not be properly
            configured in your reCAPTCHA settings. Please verify that you've
            added this domain to your Google reCAPTCHA allowed domains list.
          </p>
        </div>
        <button
          type="button"
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          onClick={() => {
            onChange("manual_verification_token");
            toast({
              title: "Verification Successful",
              description: "Development verification completed",
            });
          }}
        >
          Verify (Development Only)
        </button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="my-4 py-2 flex items-center gap-2">
        <div className="h-4 w-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
        <span className="text-sm text-muted-foreground">
          Loading verification...
        </span>
      </div>
    );
  }

  // Invisible reCAPTCHA – no UI
  return (
    <div className="my-4">
      <div className="text-sm text-muted-foreground">
        This site is protected by reCAPTCHA and the Google
        <a
          href="https://policies.google.com/privacy"
          className="text-primary hover:underline ml-1 mr-1"
          target="_blank"
          rel="noopener noreferrer"
        >
          Privacy Policy
        </a>
        and
        <a
          href="https://policies.google.com/terms"
          className="text-primary hover:underline ml-1"
          target="_blank"
          rel="noopener noreferrer"
        >
          Terms of Service
        </a>
        apply.
      </div>
    </div>
  );
}
