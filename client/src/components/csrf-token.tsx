import { useEffect, useState } from 'react';
import { apiRequest } from '@/lib/queryClient';

interface CSRFTokenProps {
  onTokenReceived: (token: string) => void;
}

/**
 * CSRF Token Component
 * 
 * This component fetches a CSRF token from the server and provides it
 * to its parent component via a callback. It can be included in forms
 * to provide CSRF protection for form submissions.
 * 
 * Usage:
 * <CSRFToken onTokenReceived={(token) => setFormValues({...formValues, _csrf: token})} />
 */
export default function CSRFToken({ onTokenReceived }: CSRFTokenProps) {
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchToken = async () => {
      try {
        console.log('Fetching CSRF token...');
        // The server provides a token when accessing this endpoint
        const response = await apiRequest('GET', '/api/csrf-token');
        const data = await response.json();
        
        if (data.csrfToken) {
          console.log('Token received successfully');
          setToken(data.csrfToken);
          onTokenReceived(data.csrfToken);
        } else {
          setError('Could not retrieve CSRF token');
        }
      } catch (err) {
        console.error('Error fetching CSRF token:', err);
        
        // For development environment, we'll create a fallback token
        if (import.meta.env.MODE === 'development') {
          console.log('Using development fallback token');
          const fallbackToken = 'manual_verification_token';
          setToken(fallbackToken);
          onTokenReceived(fallbackToken);
          return;
        }
        
        setError('Failed to fetch security token. Please refresh the page and try again.');
      }
    };

    fetchToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (error) {
    return (
      <div className="text-sm text-red-500 bg-red-50 p-2 rounded mb-4">
        {error}
      </div>
    );
  }

  return null; // No visual rendering needed
}