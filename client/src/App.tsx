import { Switch, Route, Redirect, useLocation } from "wouter";
import { Toaster } from "@/components/ui/toaster";
import NotFound from "./pages/not-found";
import AuthPage from "./pages/auth-page";
import DashboardPage from "./pages/dashboard-page";
import AssessmentsPage from "./pages/assessments-page";
import AssessmentDetailPage from "./pages/assessment-detail-page";
import FormPage from "./pages/form-page";
import FormAccessPage from "./pages/form-access-page";
import FormTestPage from "./pages/form-test";
import CalendarPage from "./pages/calendar-page";
import PaymentsPage from "./pages/payments-page";
import DocumentsPage from "./pages/documents-page";
import SettingsPage from "./pages/settings-page";
import UsersPage from "./pages/users-page";
import ReportsPage from "./pages/reports-page";
import AddAssesseePage from "./pages/add-assessee-page";
import AddAssessmentPage from "./pages/add-assessment-page";
import UniversityReferPage from "@/pages/university-refer-page";
import PublicUniversityReferPage from "@/pages/public-university-refer-page";
import PublicIndividualReferPage from "@/pages/public-individual-refer-page";
import PublicAssesseeFormPage from "@/pages/public-assessee-form-page";
import PublicParentFormPage from "@/pages/public-parent-form-page";
import ForgotPasswordPage from "@/pages/forgot-password-page";
import ResetPasswordPage from "@/pages/reset-password-page";
import PublicSchoolFormPage from "@/pages/public-school-form-page";
import EmailTestPage from "@/pages/email-test-page";
import TestResetTokenPage from "@/pages/test-reset-token-page";
import AdminPage from "./pages/admin-page";
import AllReferralsPage from "./pages/all-referrals-page";
import TestFormPage from "./pages/test-form-page";
import RegistrationPendingPage from "./pages/registration-pending-page";
import CommunicationCenterPage from "./pages/new-communication-center-page";
import TestLoginPage from "./pages/test-login-page";
import IssuesPage from "./pages/issues-page";
import { useState, useEffect, useLayoutEffect } from "react";
import { Loader2 } from "lucide-react";
import { User } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "./lib/queryClient";
import { AuthProvider } from "@/hooks/use-auth";

// Create an auth context and router directly in App.tsx to avoid import cycles

function SimplePage({ component: Component }: { component: React.ComponentType }) {
  return <Component />;
}

function ProtectedRoute({
  component: Component,
  user,
  isLoading,
  roleCheck
}: {
  component: React.ComponentType;
  user: User | null;
  isLoading: boolean;
  roleCheck?: (user: User) => boolean;
}) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!user) {
    return <Redirect to="/auth" />;
  }

  if (roleCheck && !roleCheck(user)) {
    return <Redirect to="/" />;
  }

  return <Component />;
}

function AppRouter() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const [location] = useLocation();

  // Load user on component mount
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await apiRequest('GET', '/api/user');
        if (res.status === 401) {
          setUser(null);
        } else if (res.ok) {
          const userData = await res.json();
          setUser(userData);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUser();
  }, []);

  // Login function
  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      const res = await apiRequest('POST', '/api/login', { username, password });
      
      if (!res.ok) {
        throw new Error('Invalid username or password');
      }
      
      const userData = await res.json();
      setUser(userData);
      
      toast({
        title: 'Login successful',
        description: `Welcome back, ${userData.fullName}`,
      });
    } catch (error: any) {
      // Show a user-friendly error message regardless of the actual error details
      toast({
        title: 'Login failed',
        description: 'The email/username or password you entered is incorrect. Please try again.',
        variant: 'destructive',
      });
      throw new Error('Authentication failed'); // Simplified error for logging
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData: any) => {
    try {
      setIsLoading(true);
      const res = await apiRequest('POST', '/api/register', userData);
      
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.message || 'Registration failed');
      }
      
      const responseData = await res.json();
      
      // Check if status is pending and don't log in the user
      if (responseData.status === 'admin_approval_pending' || responseData.status === 'pending') {
        // Don't set the user state, which effectively keeps them logged out
        // Instead redirect to the pending registration page
        window.location.href = '/registration-pending';
        return;
      }
      
      // Only set the user if not pending (which should not happen but just in case)
      setUser(responseData);
      
      toast({
        title: 'Registration successful',
        description: `Welcome, ${responseData.fullName}`,
      });
    } catch (error: any) {
      toast({
        title: 'Registration failed',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      const res = await apiRequest('POST', '/api/logout');
      if (!res.ok) {
        throw new Error('Logout failed');
      }
      setUser(null);
      toast({
        title: 'Logged out successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Logout failed',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  // Create auth context value
  const authValue = {
    user,
    isLoading,
    login,
    logout,
    register
  };

  // Simple redirect if already logged in and trying to access auth page
  if (user && location === "/auth") {
    return <Redirect to="/" />;
  }

  // Custom props for AuthPage
  const authPageProps = {
    login,
    register,
    user,
    isLoading
  };
  
  return (
    <Switch>
      <Route path="/auth">
        {() => <AuthPage {...authPageProps} />}
      </Route>
      
      <Route path="/">
        {() => (
          <ProtectedRoute 
            component={() => <DashboardPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
          />
        )}
      </Route>
      
      <Route path="/assessments">
        {() => (
          <ProtectedRoute
            component={() => <AssessmentsPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
          />
        )}
      </Route>

      <Route path="/assessments/new">
        {() => (
          <ProtectedRoute
            component={() => <AddAssessmentPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => ["admin", "assessor"].includes(user.role)}
          />
        )}
      </Route>

      <Route path="/assessments/:id">
        {() => (
          <ProtectedRoute
            component={() => <AssessmentDetailPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
          />
        )}
      </Route>
      
      <Route path="/forms/:id">
        {() => (
          <ProtectedRoute 
            component={() => <FormPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
          />
        )}
      </Route>
      
      <Route path="/calendar">
        {() => (
          <ProtectedRoute 
            component={() => <CalendarPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
          />
        )}
      </Route>
      
      <Route path="/payments">
        {() => (
          <ProtectedRoute 
            component={() => <PaymentsPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => ["admin", "assessor", "parent", "assessee"].includes(user.role)}
          />
        )}
      </Route>
      
      <Route path="/documents">
        {() => (
          <ProtectedRoute 
            component={() => <DocumentsPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
          />
        )}
      </Route>
      
      <Route path="/settings">
        {() => (
          <ProtectedRoute 
            component={() => <SettingsPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
          />
        )}
      </Route>
      
      <Route path="/users">
        {() => (
          <ProtectedRoute 
            component={() => <UsersPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => user.role === "admin"}
          />
        )}
      </Route>
      
      <Route path="/reports">
        {() => (
          <ProtectedRoute 
            component={() => <ReportsPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => ["admin", "assessor"].includes(user.role)}
          />
        )}
      </Route>
      
      <Route path="/assessees/new">
        {() => (
          <ProtectedRoute 
            component={() => <AddAssesseePage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => ["admin", "assessor", "parent"].includes(user.role)}
          />
        )}
      </Route>

      <Route path="/university/refer">
        {() => (
          <ProtectedRoute 
            component={() => <UniversityReferPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => user.role === "university"}
          />
        )}
      </Route>

      {/* Public University Referral Page - No Authentication Required */}
      <Route path="/university-refer">
        {() => <PublicUniversityReferPage />}
      </Route>
      
      {/* Legacy paths for backward compatibility */}
      <Route path="/public/university-refer">
        {() => <PublicUniversityReferPage />}
      </Route>
      <Route path="/public-university-refer">
        {() => <PublicUniversityReferPage />}
      </Route>

      {/* Public Individual Referral Page - No Authentication Required */}
      <Route path="/individual-refer">
        {() => <PublicIndividualReferPage />}
      </Route>
      
      {/* Legacy paths for backward compatibility */}
      <Route path="/public/individual-refer">
        {() => <PublicIndividualReferPage />}
      </Route>
      <Route path="/public-individual-refer">
        {() => <PublicIndividualReferPage />}
      </Route>

      {/* Public Assessee Form Page - No Authentication Required (for testing) */}
      <Route path="/public/assessee-form">
        {() => <PublicAssesseeFormPage />}
      </Route>

      {/* Public Parent Form Page - No Authentication Required (for testing) */}
      <Route path="/public/parent-form">
        {() => <PublicParentFormPage />}
      </Route>
      
      {/* Form Access Route - Access forms with a token (no auth required) */}
      <Route path="/public/school-form">
        {() => <PublicSchoolFormPage />}
      </Route>
      <Route path="/forms/access/:token">
        {() => <FormAccessPage />}
      </Route>
      
      {/* Password Reset Pages - No Authentication Required */}
      <Route path="/forgot-password">
        {() => <ForgotPasswordPage />}
      </Route>
      
      {/* Reset Password Routes - Handle multiple formats */}
      <Route path="/reset-password">
        {(params) => {
          console.log("Matched /reset-password route", params);
          return <ResetPasswordPage />;
        }}
      </Route>
      
      <Route path="/reset-password/:token">
        {(params) => {
          console.log("Matched /reset-password/:token route", params);
          return <ResetPasswordPage />;
        }}
      </Route>
      
      {/* Fallback for any other reset-password format */}
      <Route path="/reset-password/*">
        {(params) => {
          console.log("Matched fallback reset-password route", params, window.location.pathname);
          return <ResetPasswordPage />;
        }}
      </Route>
      
      {/* Registration Pending Page - No Authentication Required */}
      <Route path="/registration-pending">
        {() => <RegistrationPendingPage />}
      </Route>

      {/* Email Test Page - No Authentication Required (for debugging) */}
      <Route path="/email-test">
        {() => <EmailTestPage />}
      </Route>
      
      {/* Test Reset Token Page - No Authentication Required (for debugging) */}
      <Route path="/test-reset-token">
        {() => <TestResetTokenPage />}
      </Route>
      
      {/* Test Login Page - No Authentication Required (for debugging) */}
      <Route path="/test-login">
        {() => (
          <AuthProvider>
            <TestLoginPage />
          </AuthProvider>
        )}
      </Route>
      
      {/* Test Form Page - No Authentication Required (for testing) */}
      <Route path="/test-form">
        {() => <TestFormPage />}
      </Route>
      
      <Route path="/admin">
        {() => (
          <ProtectedRoute 
            component={() => <AdminPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => user.role === "admin"}
          />
        )}
      </Route>

      <Route path="/admin/users">
        {() => (
          <ProtectedRoute 
            component={() => <AdminPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => user.role === "admin"}
          />
        )}
      </Route>

      <Route path="/admin/all-referrals">
        {() => (
          <ProtectedRoute 
            component={AllReferralsPage}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => user.role === "admin"}
          />
        )}
      </Route>

      <Route path="/communication-center">
        {() => (
          <ProtectedRoute 
            component={() => <CommunicationCenterPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
          />
        )}
      </Route>

      <Route path="/issues">
        {() => (
          <ProtectedRoute 
            component={() => <IssuesPage user={user} logout={logout} />}
            user={user}
            isLoading={isLoading}
            roleCheck={(user) => user.role === 'admin' || user.role === 'assessor'}
          />
        )}
      </Route>

      <Route path="/form-test">
        {() => (
          <ProtectedRoute 
            component={FormTestPage}
            user={user}
            isLoading={isLoading}
          />
        )}
      </Route>
      
      <Route>
        {() => <NotFound />}
      </Route>
    </Switch>
  );
}

// Handle reset password redirect based on URL
function handleResetPasswordRedirect() {
  // Don't run this function on the reset-password page itself
  if (window.location.pathname.startsWith('/reset-password/')) {
    return false;
  }
  
  // Check for token in query params (but only on non-reset-password pages)
  const params = new URLSearchParams(window.location.search);
  const token = params.get('token') || params.get('reset_token');
  
  if (token) {
    console.log('Detected reset token in query parameters, redirecting to reset password page');
    // Navigate to the path-based URL format
    window.location.href = `/reset-password/${token}`;
    return true;
  }
  
  return false;
}

function App() {
  // On initial load, check if we need to handle a reset password redirect
  useEffect(() => {
    handleResetPasswordRedirect();
  }, []);

  return (
    <div>
      <AppRouter />
      <Toaster />
    </div>
  );
}

export default App;
