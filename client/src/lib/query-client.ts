import { QueryClient } from '@tanstack/react-query';

const API_BASE_URL = (() => {
  const envUrl = (import.meta as any).env?.VITE_API_BASE_URL as string | undefined;
  const windowOrigin = typeof window !== 'undefined' ? window.location.origin : '';

  // For development, force HTTP protocol for localhost
  if (windowOrigin && windowOrigin.includes('localhost') && windowOrigin.startsWith('https:')) {
    const httpOrigin = windowOrigin.replace('https:', 'http:');
    console.log(`Development mode: Converting HTTPS to HTTP for localhost: ${windowOrigin} -> ${httpOrigin}`);
    return httpOrigin;
  }

  if (!envUrl) return windowOrigin;

  // If the env var points to localhost but we are not on localhost, ignore it
  if (envUrl.includes('localhost') && windowOrigin && !windowOrigin.includes('localhost')) {
    return windowOrigin;
  }

  return envUrl;
})();

// Create a client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  },
});

/**
 * Make an API request with the fetch API.
 * @param endpoint - The API endpoint to request
 * @param options - Additional fetch options
 */
export async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const url = `${API_BASE_URL.replace(/\/?$/, '')}${path}`;
  
  const headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    ...(options.headers || {}),
  };

  const response = await fetch(url, {
    ...options,
    headers,
  });

  // Check if the response is ok
  if (!response.ok) {
    const errorText = await response.text();
    try {
      const errorData = JSON.parse(errorText);
      throw new Error(errorData.message || 'API request failed');
    } catch (e) {
      throw new Error(errorText || 'API request failed');
    }
  }

  // If response status is 204 (No Content) or the response is empty, return null
  if (response.status === 204 || !response.headers.get('content-length')) {
    return null as T;
  }

  return response.json();
}
