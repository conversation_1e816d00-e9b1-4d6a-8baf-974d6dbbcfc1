import { QueryClient, QueryFunction } from "@tanstack/react-query";

// Base URL for API requests, allowing the frontend to target a specific backend
// host during development or production.
// For Replit, we should use the current domain instead of localhost
const API_BASE_URL = (() => {
  // Safari-compatible environment variable access
  const importMeta = import.meta as any;
  const env = importMeta?.env || {};

  const envUrl = env.VITE_API_BASE_URL as string | undefined;
  const forceHttpLocalhost = env.VITE_FORCE_HTTP_LOCALHOST === 'true';
  const windowOrigin = typeof window !== 'undefined' ? window.location.origin : '';
  const isDevelopment = env.DEV === true;

  console.log('🔧 API Base URL Configuration:', {
    envUrl,
    forceHttpLocalhost,
    windowOrigin,
    isDevelopment,
    allEnvVars: env,
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
  });

  // Safari-specific debugging
  const isSafari = typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
  if (isSafari) {
    console.log('🦁 Safari browser detected - using Safari-compatible configuration');
  }

  // In development with localhost, use relative URLs to leverage Vite proxy
  if (isDevelopment && windowOrigin && windowOrigin.includes('localhost')) {
    console.log(`🔄 Development mode: Using relative URLs for Vite proxy`);
    return '';  // Empty string means relative URLs
  }

  // For development, force HTTP protocol for localhost (fallback)
  if (forceHttpLocalhost && windowOrigin && windowOrigin.includes('localhost')) {
    const httpOrigin = windowOrigin.replace('https:', 'http:');
    console.log(`🔄 Development mode: Converting HTTPS to HTTP for localhost: ${windowOrigin} -> ${httpOrigin}`);
    return httpOrigin;
  }

  // If we have an explicit env URL, use it (especially for localhost development)
  if (envUrl !== undefined) {
    console.log(`✅ Using environment API base URL: ${envUrl}`);
    return envUrl;
  }

  // Safari fallback - if we're on localhost but env vars aren't working, force relative URLs
  if (isSafari && windowOrigin && windowOrigin.includes('localhost')) {
    console.log(`🦁 Safari localhost fallback: Using relative URLs`);
    return '';
  }

  // Fallback to window origin
  console.log(`⚠️ Using window origin as API base URL: ${windowOrigin}`);
  return windowOrigin;
})();

console.log('🎯 Final API_BASE_URL:', API_BASE_URL);


async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    // Get the original error message
    const text = (await res.text()) || res.statusText;
    
    // Provide user-friendly error messages based on status code
    let errorMessage: string;
    switch (res.status) {
      case 401:
        errorMessage = "You are not authorized to perform this action. Please log in.";
        break;
      case 403:
        errorMessage = "You don't have permission to access this resource.";
        break;
      case 404:
        errorMessage = "The requested information could not be found.";
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        errorMessage = "A server error occurred. Please try again later.";
        break;
      default:
        // For login failures and other client errors, use a generic message
        errorMessage = "An error occurred while processing your request.";
    }
    
    // Log the actual error for debugging purposes
    console.error(`API Error (${res.status}): ${text}`);
    
    // Throw the user-friendly error
    throw new Error(errorMessage);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  // Ensure URL format is valid and standardized
  const processedUrl = (() => {
    // If URL already starts with http, use it as is (for external APIs)
    if (url.startsWith('http')) return url;

    // Normalize the path
    let processed = url.startsWith('/') ? url : `/${url}`;
    processed = processed.endsWith('/')
      ? processed.substring(0, processed.length - 1)
      : processed;

    const base = API_BASE_URL.replace(/\/?$/, '');
    return `${base}${processed}`;
  })();
  
  console.log(`🚀 Making ${method} request to: ${processedUrl}`);
  console.log(`📦 Request payload:`, data);
  console.log(`🔗 API_BASE_URL used: ${API_BASE_URL}`);
  
  const headers: Record<string, string> = {
    "Accept": "application/json"
  };
  
  if (data) {
    headers["Content-Type"] = "application/json";
  }
  
  console.log(`Request headers:`, headers);
  
  let res: Response;
  try {
    res = await fetch(processedUrl, {
      method,
      headers,
      body: data ? JSON.stringify(data) : undefined,
      credentials: "include",
    });
  } catch (error) {
    console.error(`Network error in API request:`, error instanceof Error ? error.message : String(error));
    throw new Error(`Network error: Could not complete ${method} request to ${processedUrl}`);
  }

  console.log(`API ${method} response status:`, res.status);

  await throwIfResNotOk(res);
  console.log(`API ${method} request successful`);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";

export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    // Build base URL from the first query key entry
    let url = String(queryKey[0] || '').trim();

    if (!url.startsWith('/')) {
      url = `/${url}`;
    }

    // Support a second queryKey element for parameters
    if (queryKey.length > 1 && queryKey[1] != null && queryKey[1] !== '') {
      const param = queryKey[1];
      if (typeof param === 'string' || typeof param === 'number') {
        url = `${url}/${param}`;
      } else if (typeof param === 'object') {
        const search = new URLSearchParams(param as Record<string, string>).toString();
        url = search ? `${url}?${search}` : url;
      }
    }

    const fullUrl = `${API_BASE_URL.replace(/\/?$/, '')}${url}`;
    console.log('Making API request to:', fullUrl);

    try {
      const res = await fetch(fullUrl, {
        credentials: 'include',
        headers: {
          Accept: 'application/json'
        }
      });

      console.log('API response status:', res.status);

      if (unauthorizedBehavior === 'returnNull' && res.status === 401) {
        console.log('Unauthorized access (401) - returning null');
        return null;
      }

      await throwIfResNotOk(res);

      const text = await res.text();
      const contentType = res.headers.get('content-type') || '';
      if (!text) return null as T;
      if (contentType.includes('application/json')) {
        return JSON.parse(text) as T;
      }

      try {
        return JSON.parse(text) as T;
      } catch (e) {
        console.error('Failed to parse JSON response:', e);
        throw new Error('Invalid JSON response');
      }
    } catch (error) {
      console.error('API request error:', error instanceof Error ? error.message : String(error));
      console.error('Full error details:', error);
      console.error('Request URL was:', fullUrl);
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: true, // Enable refetching when window regains focus
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1, // Retry failed requests once
    },
    mutations: {
      retry: false,
    },
  },
});

