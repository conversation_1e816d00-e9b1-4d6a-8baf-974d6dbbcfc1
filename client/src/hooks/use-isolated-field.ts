import { useState, useEffect } from 'react';

/**
 * Custom hook to create completely isolated form fields that don't interfere with each other
 * This prevents the cross-contamination issue between fields like fullName and contactAddress
 * 
 * @param storageKey A unique key to save this field value in localStorage
 * @param initialValue The initial value of the field
 * @returns An object with value, setValue, and onChange handler
 */
export function useIsolatedField(storageKey: string, initialValue: string = '') {
  // Initialize state from localStorage or use initial value
  const [value, setValue] = useState(() => {
    const savedValue = localStorage.getItem(storageKey);
    return savedValue !== null ? savedValue : initialValue;
  });

  // Save to localStorage whenever value changes
  useEffect(() => {
    localStorage.setItem(storageKey, value);
  }, [value, storageKey]);

  // Generate an onChange handler for input elements
  const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setValue(e.target.value);
  };

  return {
    value,
    setValue,
    onChange
  };
}