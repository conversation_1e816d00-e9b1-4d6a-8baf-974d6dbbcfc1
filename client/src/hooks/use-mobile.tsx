import { useState, useEffect } from 'react';

export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Define the media query
    const mediaQuery = window.matchMedia('(max-width: 768px)');
    
    // Initial check
    setIsMobile(mediaQuery.matches);
    
    // Add listener for changes
    const handleResize = (e: MediaQueryListEvent) => setIsMobile(e.matches);
    
    // Modern browsers use addEventListener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleResize);
      return () => mediaQuery.removeEventListener('change', handleResize);
    } else {
      // For older browsers (like Safari < 14)
      mediaQuery.addListener(handleResize);
      return () => mediaQuery.removeListener(handleResize);
    }
  }, []);

  return isMobile;
}