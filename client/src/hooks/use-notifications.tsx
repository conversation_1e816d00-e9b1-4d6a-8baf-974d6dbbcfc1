import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useMemo } from "react";

export function useNotifications() {
  const { toast } = useToast();

  // Get all notifications for the current user
  const {
    data: rawNotifications,
    isLoading: isLoadingNotifications,
    error: notificationsError,
    refetch: refetchNotifications
  } = useQuery({
    queryKey: ['/api/notifications'],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/notifications");
      return res.json();
    },
    staleTime: 30000, // 30 seconds
  });
  
  // Helper function to clean notification content
  const cleanNotificationContent = (notification: any) => {
    if (!notification) return notification;
    
    // Create a deep copy to avoid mutating the original
    const cleaned = { ...notification };
    
    // Remove any "ID: XX" text from content
    if (cleaned.content) {
      cleaned.content = cleaned.content
        .replace(/\bID: \d+\b/g, '')
        .replace(/\brequires verification\./g, '')
        .trim();
    }
    
    return cleaned;
  };

  // Deduplicate notifications based on id and clean content
  const notifications = useMemo(() => {
    if (!rawNotifications) return [];
    
    // Create a Map to store unique notifications by their ID
    const uniqueNotifications = new Map();
    
    // Add each notification to the map, which automatically handles duplicates
    // and clean the notification content
    rawNotifications.forEach((notification: any) => {
      const cleanedNotification = cleanNotificationContent(notification);
      uniqueNotifications.set(cleanedNotification.id, cleanedNotification);
    });
    
    // Convert the Map values back to an array and sort by creation date (newest first)
    const result = Array.from(uniqueNotifications.values())
      .sort((a: any, b: any) => {
        const dateA = new Date(a.createdAt);
        const dateB = new Date(b.createdAt);
        return dateB.getTime() - dateA.getTime(); // Sort descending (newest first)
      });
    
    console.log(`Deduplicated ${rawNotifications.length} notifications to ${result.length} unique notifications`);
    
    return result;
  }, [rawNotifications]);

  // Get unread notification count
  const {
    data: unreadCount,
    isLoading: isLoadingUnreadCount,
    error: unreadCountError,
    refetch: refetchUnreadCount
  } = useQuery({
    queryKey: ['/api/notifications/count'],
    queryFn: async () => {
      try {
        const res = await apiRequest("GET", "/api/notifications/count");
        
        // Handle rate limiting with exponential backoff
        if (res.status === 429) {
          const retryAfter = res.headers.get('retry-after');
          const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : 60000; // Default 1 minute
          console.warn(`Rate limited. Waiting ${waitTime}ms before retry`);
          throw new Error('Rate limited');
        }
        
        const data = await res.json();
        
        // Log notification count to debug badge issues
        console.log("Unread notification count from API:", data.count);
        
        // Force a valid numeric count
        return typeof data.count === 'number' ? data.count : 0;
      } catch (error) {
        console.error("Error fetching notification count:", error);
        return 0;
      }
    },
    staleTime: 30000, // 30 seconds - reduce server load
    refetchInterval: 30000, // 30 seconds - less aggressive polling
    refetchIntervalInBackground: false, // Don't poll when tab is not active
    retry: (failureCount, error) => {
      // Don't retry rate limit errors immediately
      if (error?.message === 'Rate limited') {
        return failureCount < 2;
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });

  // Mark a notification as read
  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: number) => {
      const res = await apiRequest("PUT", `/api/notifications/${notificationId}/read`);
      return res.json();
    },
    onMutate: async (notificationId) => {
      // Cancel any outgoing refetches so they don't overwrite our optimistic update
      await queryClient.cancelQueries({ queryKey: ['/api/notifications'] });
      await queryClient.cancelQueries({ queryKey: ['/api/notifications/count'] });
      
      // Snapshot the previous value
      const previousNotifications = queryClient.getQueryData(['/api/notifications']);
      const previousCount = queryClient.getQueryData(['/api/notifications/count']);
      
      // Optimistically update notifications
      if (previousNotifications) {
        queryClient.setQueryData(['/api/notifications'], (old: any[]) => {
          return old.map(notification => 
            notification.id === notificationId 
              ? { ...notification, status: 'read' } 
              : notification
          );
        });
      }
      
      // Optimistically update count
      if (previousCount) {
        queryClient.setQueryData(['/api/notifications/count'], (old: any) => {
          return { count: Math.max(0, (old.count || 0) - 1) };
        });
      }
      
      // Return previous values for potential rollback
      return { previousNotifications, previousCount };
    },
    onError: (error, _, context) => {
      // Rollback on error
      if (context?.previousNotifications) {
        queryClient.setQueryData(['/api/notifications'], context.previousNotifications);
      }
      if (context?.previousCount) {
        queryClient.setQueryData(['/api/notifications/count'], context.previousCount);
      }
      
      toast({
        title: "Failed to mark notification as read",
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => {
      // Always refetch after error or success to make sure we have the latest data
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/count'] });
    },
  });

  // Mark all notifications as read
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("PUT", "/api/notifications/read-all");
      return res.json();
    },
    onMutate: async () => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['/api/notifications'] });
      await queryClient.cancelQueries({ queryKey: ['/api/notifications/count'] });
      
      // Snapshot the previous values
      const previousNotifications = queryClient.getQueryData(['/api/notifications']);
      const previousCount = queryClient.getQueryData(['/api/notifications/count']);
      
      // Optimistically update notifications
      if (previousNotifications) {
        queryClient.setQueryData(['/api/notifications'], (old: any[]) => {
          return old.map(notification => ({ ...notification, status: 'read' }));
        });
      }
      
      // Optimistically update count to 0
      queryClient.setQueryData(['/api/notifications/count'], { count: 0 });
      
      // Return previous values for rollback
      return { previousNotifications, previousCount };
    },
    onSuccess: () => {
      toast({
        title: "Notifications cleared",
        description: "All notifications have been marked as read",
      });
    },
    onError: (error: Error, _, context) => {
      // Rollback on error
      if (context?.previousNotifications) {
        queryClient.setQueryData(['/api/notifications'], context.previousNotifications);
      }
      if (context?.previousCount) {
        queryClient.setQueryData(['/api/notifications/count'], context.previousCount);
      }
      
      toast({
        title: "Failed to clear notifications",
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/count'] });
    },
  });

  // Get notification preferences
  const {
    data: preferences,
    isLoading: isLoadingPreferences,
    error: preferencesError,
    refetch: refetchPreferences
  } = useQuery({
    queryKey: ['/api/notification-preferences'],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/notification-preferences");
      return res.json();
    },
  });

  // Update notification preferences
  const updatePreferencesMutation = useMutation({
    mutationFn: async (preferences: any) => {
      const res = await apiRequest("PUT", "/api/notification-preferences", { preferences });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notification-preferences'] });
      toast({
        title: "Preferences updated",
        description: "Your notification preferences have been updated",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update preferences",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Polling function for new notifications
  const setupNotificationPolling = (interval = 3000) => {
    const intervalId = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications/count'] });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
    }, interval);

    return () => clearInterval(intervalId);
  };

  return {
    notifications,
    isLoadingNotifications,
    notificationsError,
    refetchNotifications,
    unreadCount,
    isLoadingUnreadCount,
    unreadCountError,
    refetchUnreadCount,
    markAsReadMutation,
    markAllAsReadMutation,
    preferences,
    isLoadingPreferences,
    preferencesError,
    refetchPreferences,
    updatePreferencesMutation,
    setupNotificationPolling,
  };
}