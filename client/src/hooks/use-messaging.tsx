import { useQuery, useMutation, QueryClient } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export function useMessaging() {
  const { toast } = useToast();

  // Get all conversations for the current user
  const {
    data: conversations,
    isLoading: isLoadingConversations,
    error: conversationsError,
    refetch: refetchConversations
  } = useQuery({
    queryKey: ['/api/conversations'],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/conversations");
      return res.json();
    },
    staleTime: 30000, // 30 seconds
  });

  // Get a specific conversation with messages
  const getConversation = (conversationId: number) => {
    return useQuery({
      queryKey: ['/api/conversations', conversationId],
      queryFn: async () => {
        const res = await apiRequest("GET", `/api/conversations/${conversationId}`);
        return res.json();
      },
      staleTime: 10000, // 10 seconds
    });
  };

  // Create a new conversation
  const createConversationMutation = useMutation({
    mutationFn: async (data: { title: string; participantIds: number[]; assessmentId?: number }) => {
      const res = await apiRequest("POST", "/api/conversations", data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/conversations'] });
      toast({
        title: "Conversation created",
        description: "A new conversation has been created",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create conversation",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Add a participant to a conversation
  const addParticipantMutation = useMutation({
    mutationFn: async ({ conversationId, userId }: { conversationId: number; userId: number }) => {
      const res = await apiRequest("POST", `/api/conversations/${conversationId}/participants`, { userId });
      return res.json();
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/conversations', variables.conversationId] });
      toast({
        title: "Participant added",
        description: "A new participant has been added to the conversation",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to add participant",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Remove a participant from a conversation
  const removeParticipantMutation = useMutation({
    mutationFn: async ({ conversationId, userId }: { conversationId: number; userId: number }) => {
      const res = await apiRequest("DELETE", `/api/conversations/${conversationId}/participants/${userId}`);
      return res.json();
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/conversations', variables.conversationId] });
      toast({
        title: "Participant removed",
        description: "A participant has been removed from the conversation",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to remove participant",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Send a message
  const sendMessageMutation = useMutation({
    mutationFn: async ({ conversationId, content, attachmentId }: { conversationId: number; content: string; attachmentId?: number }) => {
      // Include senderId explicitly in the request body
      const res = await apiRequest("POST", "/api/messages", { 
        conversationId, 
        content, 
        attachmentId,
        // We don't need to add senderId here as the server will set it from the authenticated user
      });
      
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || "Failed to send message");
      }
      
      return res.json();
    },
    onSuccess: (data, variables) => {
      console.log("Message sent successfully, server response:", data);
      // Make sure we refresh the conversation data to get the latest messages
      queryClient.invalidateQueries({ queryKey: ['/api/conversations', variables.conversationId] });
      toast({
        title: "Message sent",
        description: "Your message has been sent",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to send message",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Edit a message
  const editMessageMutation = useMutation({
    mutationFn: async ({ messageId, content }: { messageId: number; content: string }) => {
      const res = await apiRequest("PUT", `/api/messages/${messageId}`, { content });
      return res.json();
    },
    onSuccess: (_data, _variables) => {
      // We can't know which conversation this is without passing it as part of the variables
      toast({
        title: "Message edited",
        description: "Your message has been updated",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to edit message",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Polling function for new messages in a specific conversation
  const setupMessagePolling = (conversationId: number, interval = 3000) => {
    // Use a shorter interval (3 seconds) for more responsive updates
    const intervalId = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: ['/api/conversations', conversationId] });
    }, interval);

    return () => clearInterval(intervalId);
  };

  return {
    conversations,
    isLoadingConversations,
    conversationsError,
    refetchConversations,
    getConversation,
    createConversationMutation,
    addParticipantMutation,
    removeParticipantMutation,
    sendMessageMutation,
    editMessageMutation,
    setupMessagePolling,
  };
}