# Database Configuration
DATABASE_URL=postgresql://username:password@hostname:port/database_name

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-here

# SendGrid Email Configuration
SENDGRID_API_KEY=your-sendgrid-api-key-here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=NeuroElevate Assessment Platform

# reCAPTCHA Configuration
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key-here
RECAPTCHA_V3_SECRET_KEY=your-recaptcha-v3-secret-key-here
ALLOW_RECAPTCHA_BYPASS=true

# Application URL Configuration
APP_URL=http://localhost:3000
CUSTOM_DOMAIN=localhost:3000
PORT=3000

# Google Cloud Configuration (for any Google services)
GOOGLE_APPLICATION_CREDENTIALS={"type":"service_account","project_id":"your-project"}

# Development/Testing
NODE_ENV=development
LOG_LEVEL=info
TEST_SECRET=test-value

# Optional Replit-specific variables (for compatibility)
REPL_ID=
REPL_SLUG=
REPLIT_APP_URL=
REPLIT_DEV_URL=
REPLIT_DOMAINS=
