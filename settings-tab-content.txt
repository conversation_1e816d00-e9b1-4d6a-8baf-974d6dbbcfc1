          {/* System Settings Tab */}
          <TabsContent value="settings">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Email Configuration</CardTitle>
                  <CardDescription>Test and manage email sending functionality</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">SendGrid API Status</p>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-green-500">Connected</Badge>
                      <span className="text-sm text-muted-foreground">Using SendGrid for email delivery</span>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Send Test Email</p>
                    <p className="text-sm text-muted-foreground mb-4">
                      Send a test email to verify that your email configuration is working correctly
                    </p>
                    
                    <div className="flex gap-2">
                      <Input 
                        id="test-email" 
                        placeholder="<EMAIL>" 
                        className="flex-1"
                        type="email"
                        value={testEmail}
                        onChange={(e) => setTestEmail(e.target.value)}
                      />
                      <Button 
                        onClick={() => testEmailMutation.mutate(testEmail)}
                        disabled={testEmailMutation.isPending || !testEmail}
                      >
                        {testEmailMutation.isPending ? (
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <Mail className="mr-2 h-4 w-4" />
                        )}
                        Send Test
                      </Button>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between border-t pt-6">
                  <div className="text-xs text-muted-foreground">
                    Using SendGrid v3 API for email delivery
                  </div>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Application Settings</CardTitle>
                  <CardDescription>Configure global application settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="app-name">Application Name</Label>
                        <p className="text-sm text-muted-foreground">The name displayed throughout the application</p>
                      </div>
                      <Input id="app-name" value="A2lexa" className="w-[200px]" readOnly />
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Debug Mode</Label>
                        <p className="text-sm text-muted-foreground">Enable verbose logging for troubleshooting</p>
                      </div>
                      <Switch id="debug-mode" />
                    </div>
                  </div>

                  <Separator />
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Maintenance Mode</Label>
                        <p className="text-sm text-muted-foreground">Temporarily disable access for non-admin users</p>
                      </div>
                      <Switch id="maintenance-mode" />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="border-t pt-6">
                  <Button variant="outline" className="w-full">Save Settings</Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>