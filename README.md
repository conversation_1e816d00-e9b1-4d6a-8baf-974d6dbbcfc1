# NeuroEvaluate

## Installation

1. Clone the repository and install dependencies:

```bash
npm install
```

## Development

- Start the development server:

```bash
npm run dev
```

- Type check the project:

```bash
npm run check
```

- Push database migrations (requires `DATABASE_URL`):

```bash
npm run db:push
```

## Environment variables

The server relies on several environment variables. Create a `.env` file or export them in your environment:

- `DATABASE_URL` – PostgreSQL connection string.
- `SESSION_SECRET` – secret for session cookies.
- `SENDGRID_API_KEY` – SendGrid API key used for sending emails.
- `SENDGRID_FROM_EMAIL` – default "from" email address.
- `SENDGRID_FROM_NAME` – name associated with the from address.
- `RECAPTCHA_SECRET_KEY` – Google reCAPTCHA secret key (required only when using the standard reCAPTCHA API).
- `RECAPTCHA_V3_SECRET_KEY` – secret key for verifying reCAPTCHA v3 tokens server‑side.
- The backend expects reCAPTCHA tokens generated with the action `submit`.
- `ALLOW_RECAPTCHA_BYPASS` – set to `true` to bypass reCAP<PERSON>HA in development.
- `APP_URL` / `CUSTOM_DOMAIN` – base URL used when generating links.
- `REPLIT_APP_URL`, `REPLIT_DEV_URL`, `REPLIT_DOMAINS`, `REPL_SLUG`, `REPL_ID` – deployment specific settings.
- `PORT` – port number (defaults to 3000 if unset).
- `NODE_ENV` – environment name (`development` or `production`).
- `VITE_RECAPTCHA_SITE_KEY` – front‑end key for reCAPTCHA used by the client.

## Running tests

This project currently has no automated test suite. You can run TypeScript checks with:

```bash
npm run check
```

## Building for production

Build the client and server bundles:

```bash
npm run build
```

Start the compiled server:

```bash
npm start
```
